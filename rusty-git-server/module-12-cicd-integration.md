# Module 12: Integrated CI/CD for Hosted Repositories

## 🎯 Learning Objectives

By the end of this module, you will:
- Build a complete CI/CD pipeline system integrated with your Git server
- Implement YAML-based pipeline configuration similar to GitHub Actions
- Create self-hosted and cloud-based build runners
- Master artifact management and package registry integration
- Build environment management for staging and production deployments
- Understand pipeline visualization and monitoring

## 🚀 Why Integrated CI/CD Matters

Modern development requires seamless integration between version control and deployment:
- **Automated Testing**: Run tests on every commit and pull request
- **Continuous Deployment**: Automatically deploy to staging and production
- **Quality Gates**: Prevent bad code from reaching production
- **Developer Productivity**: Reduce manual deployment overhead
- **Compliance**: Maintain audit trails for all deployments

### CI/CD Architecture Overview

```mermaid
graph TB
    subgraph "Git Events"
        PUSH[Push to Branch]
        PR[Pull Request]
        TAG[Tag Creation]
        MERGE[Merge to Main]
    end
    
    subgraph "Pipeline Engine"
        TRIGGER[Event Trigger]
        PARSE[Parse Pipeline Config]
        QUEUE[Job Queue]
        SCHEDULE[Job Scheduler]
    end
    
    subgraph "Build Runners"
        DOCKER[Docker Runner]
        K8S[Kubernetes Runner]
        CLOUD[Cloud Runner]
        SELF[Self-hosted Runner]
    end
    
    subgraph "Pipeline Stages"
        BUILD[Build & Test]
        SECURITY[Security Scan]
        ARTIFACT[Build Artifacts]
        DEPLOY_STAGE[Deploy to Staging]
        E2E[E2E Tests]
        DEPLOY_PROD[Deploy to Production]
    end
    
    subgraph "Storage & Registry"
        ARTIFACTS[Artifact Storage]
        DOCKER_REG[Docker Registry]
        PACKAGE_REG[Package Registry]
        LOGS[Build Logs]
    end
    
    PUSH --> TRIGGER
    PR --> TRIGGER
    TAG --> TRIGGER
    MERGE --> TRIGGER
    
    TRIGGER --> PARSE
    PARSE --> QUEUE
    QUEUE --> SCHEDULE
    
    SCHEDULE --> DOCKER
    SCHEDULE --> K8S
    SCHEDULE --> CLOUD
    SCHEDULE --> SELF
    
    DOCKER --> BUILD
    BUILD --> SECURITY
    SECURITY --> ARTIFACT
    ARTIFACT --> DEPLOY_STAGE
    DEPLOY_STAGE --> E2E
    E2E --> DEPLOY_PROD
    
    ARTIFACT --> ARTIFACTS
    ARTIFACT --> DOCKER_REG
    ARTIFACT --> PACKAGE_REG
    BUILD --> LOGS
    
    style TRIGGER fill:#e8f5e8
    style DEPLOY_PROD fill:#fff3e0
    style SECURITY fill:#ffebee
```

## 📋 Pipeline Configuration System

### YAML Pipeline Definition

```yaml
# .rusty-ci.yml - Pipeline configuration file
name: "Build and Deploy"
version: "1.0"

# Trigger configuration
on:
  push:
    branches: [main, develop]
    paths-ignore: ["docs/**", "*.md"]
  pull_request:
    branches: [main]
  tag:
    pattern: "v*"

# Environment variables
env:
  NODE_VERSION: "18"
  RUST_VERSION: "1.75"
  DOCKER_REGISTRY: "ghcr.io"

# Job definitions
jobs:
  # Build and test job
  build:
    name: "Build and Test"
    runs-on: ubuntu-latest
    container:
      image: rust:1.75-slim
      options: --user root
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.cargo/registry
            ~/.cargo/git
            target/
          key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
      
      - name: Install dependencies
        run: |
          apt-get update
          apt-get install -y libpq-dev pkg-config libssl-dev
      
      - name: Run tests
        run: cargo test --verbose
        env:
          DATABASE_URL: ********************************************/test_db
      
      - name: Build release
        run: cargo build --release
      
      - name: Upload artifacts
        uses: actions/upload-artifact@v3
        with:
          name: binary
          path: target/release/rusty-git-server

  # Security scanning job
  security:
    name: "Security Scan"
    runs-on: ubuntu-latest
    needs: build
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
      
      - name: Audit Rust dependencies
        run: |
          cargo install cargo-audit
          cargo audit
      
      - name: Upload security results
        uses: actions/upload-artifact@v3
        with:
          name: security-results
          path: trivy-results.sarif

  # Docker build job
  docker:
    name: "Build Docker Image"
    runs-on: ubuntu-latest
    needs: [build, security]
    if: github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/tags/')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Download artifacts
        uses: actions/download-artifact@v3
        with:
          name: binary
          path: ./artifacts
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: Login to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.DOCKER_REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}
          tags: |
            type=ref,event=branch
            type=ref,event=tag
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}
      
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Deploy to staging
  deploy-staging:
    name: "Deploy to Staging"
    runs-on: ubuntu-latest
    needs: docker
    if: github.ref == 'refs/heads/main'
    environment: staging
    
    steps:
      - name: Deploy to Kubernetes
        uses: azure/k8s-deploy@v1
        with:
          manifests: |
            k8s/staging/deployment.yaml
            k8s/staging/service.yaml
          images: |
            ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}:${{ github.sha }}
          kubectl-version: 'latest'
      
      - name: Run smoke tests
        run: |
          sleep 30
          curl -f https://staging.git.example.com/health

  # Deploy to production
  deploy-production:
    name: "Deploy to Production"
    runs-on: ubuntu-latest
    needs: deploy-staging
    if: startsWith(github.ref, 'refs/tags/')
    environment: production
    
    steps:
      - name: Deploy to Kubernetes
        uses: azure/k8s-deploy@v1
        with:
          manifests: |
            k8s/production/deployment.yaml
            k8s/production/service.yaml
          images: |
            ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}:${{ github.ref_name }}
          kubectl-version: 'latest'
      
      - name: Run production health checks
        run: |
          sleep 60
          curl -f https://git.example.com/health
          ./scripts/production-smoke-tests.sh

# Notification configuration
notifications:
  slack:
    webhook_url: ${{ secrets.SLACK_WEBHOOK }}
    on_success: true
    on_failure: true
  email:
    recipients: ["<EMAIL>"]
    on_failure: true
```

## 🔧 Pipeline Engine Implementation

### Core Pipeline Data Model

```rust
// src/cicd/mod.rs
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Pipeline {
    pub id: Uuid,
    pub repository_id: Uuid,
    pub name: String,
    pub config_path: String,        // Path to pipeline config file
    pub config_content: String,     // YAML content
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct PipelineRun {
    pub id: Uuid,
    pub pipeline_id: Uuid,
    pub repository_id: Uuid,
    pub trigger_event: TriggerEvent,
    pub trigger_ref: String,        // branch, tag, or commit
    pub trigger_commit: String,
    pub trigger_user_id: Option<Uuid>,
    pub status: PipelineStatus,
    pub started_at: DateTime<Utc>,
    pub finished_at: Option<DateTime<Utc>>,
    pub duration_seconds: Option<i32>,
    
    // Context
    pub environment_variables: serde_json::Value,
    pub secrets: serde_json::Value,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "trigger_event", rename_all = "lowercase")]
pub enum TriggerEvent {
    Push,
    PullRequest,
    Tag,
    Schedule,
    Manual,
    WebhookExternal,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "pipeline_status", rename_all = "lowercase")]
pub enum PipelineStatus {
    Queued,
    Running,
    Success,
    Failed,
    Cancelled,
    Skipped,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Job {
    pub id: Uuid,
    pub pipeline_run_id: Uuid,
    pub name: String,
    pub runner_type: RunnerType,
    pub runner_id: Option<String>,
    pub status: JobStatus,
    pub started_at: Option<DateTime<Utc>>,
    pub finished_at: Option<DateTime<Utc>>,
    pub duration_seconds: Option<i32>,
    
    // Configuration
    pub container_image: Option<String>,
    pub environment_variables: serde_json::Value,
    pub depends_on: Vec<String>,        // Job dependencies
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "runner_type", rename_all = "lowercase")]
pub enum RunnerType {
    Docker,
    Kubernetes,
    SelfHosted,
    Cloud,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "job_status", rename_all = "lowercase")]
pub enum JobStatus {
    Pending,
    Queued,
    Running,
    Success,
    Failed,
    Cancelled,
    Skipped,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct JobStep {
    pub id: Uuid,
    pub job_id: Uuid,
    pub name: String,
    pub step_type: StepType,
    pub command: Option<String>,
    pub action: Option<String>,        // For reusable actions
    pub parameters: serde_json::Value,
    pub status: StepStatus,
    pub started_at: Option<DateTime<Utc>>,
    pub finished_at: Option<DateTime<Utc>>,
    pub exit_code: Option<i32>,
    pub output: Option<String>,
    pub error_output: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "step_type", rename_all = "lowercase")]
pub enum StepType {
    Run,            // Shell command
    Action,         // Reusable action
    Checkout,       // Git checkout
    Cache,          // Cache management
    Artifact,       // Artifact upload/download
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "step_status", rename_all = "lowercase")]
pub enum StepStatus {
    Pending,
    Running,
    Success,
    Failed,
    Skipped,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct BuildRunner {
    pub id: Uuid,
    pub name: String,
    pub runner_type: RunnerType,
    pub is_online: bool,
    pub is_busy: bool,
    pub last_heartbeat: DateTime<Utc>,
    pub capabilities: serde_json::Value,
    pub configuration: serde_json::Value,
    pub created_at: DateTime<Utc>,
}

// Pipeline configuration structures
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PipelineConfig {
    pub name: String,
    pub version: String,
    pub on: TriggerConfig,
    pub env: Option<HashMap<String, String>>,
    pub jobs: HashMap<String, JobConfig>,
    pub notifications: Option<NotificationConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TriggerConfig {
    pub push: Option<PushTrigger>,
    pub pull_request: Option<PullRequestTrigger>,
    pub tag: Option<TagTrigger>,
    pub schedule: Option<ScheduleTrigger>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PushTrigger {
    pub branches: Option<Vec<String>>,
    pub paths: Option<Vec<String>>,
    pub paths_ignore: Option<Vec<String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PullRequestTrigger {
    pub branches: Option<Vec<String>>,
    pub types: Option<Vec<String>>, // opened, synchronize, closed
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TagTrigger {
    pub pattern: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScheduleTrigger {
    pub cron: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JobConfig {
    pub name: String,
    pub runs_on: String,
    pub container: Option<ContainerConfig>,
    pub services: Option<HashMap<String, ServiceConfig>>,
    pub needs: Option<Vec<String>>,
    pub if_condition: Option<String>,
    pub environment: Option<String>,
    pub env: Option<HashMap<String, String>>,
    pub steps: Vec<StepConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContainerConfig {
    pub image: String,
    pub options: Option<String>,
    pub env: Option<HashMap<String, String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceConfig {
    pub image: String,
    pub env: Option<HashMap<String, String>>,
    pub options: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StepConfig {
    pub name: String,
    pub uses: Option<String>,        // Action to use
    pub run: Option<String>,         // Command to run
    pub with: Option<HashMap<String, serde_json::Value>>,
    pub env: Option<HashMap<String, String>>,
    pub if_condition: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationConfig {
    pub slack: Option<SlackNotification>,
    pub email: Option<EmailNotification>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SlackNotification {
    pub webhook_url: String,
    pub on_success: bool,
    pub on_failure: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmailNotification {
    pub recipients: Vec<String>,
    pub on_success: bool,
    pub on_failure: bool,
}
```

## ⚙️ Pipeline Execution Engine

### Pipeline Service Implementation

```rust
// src/cicd/service.rs
use sqlx::PgPool;
use uuid::Uuid;
use tokio::sync::mpsc;
use std::collections::HashMap;
use crate::cicd::*;

pub struct PipelineService {
    db: PgPool,
    job_queue: mpsc::UnboundedSender<JobExecution>,
    runner_manager: RunnerManager,
}

#[derive(Debug, Clone)]
pub struct JobExecution {
    pub job_id: Uuid,
    pub pipeline_run_id: Uuid,
    pub config: JobConfig,
    pub context: ExecutionContext,
}

#[derive(Debug, Clone)]
pub struct ExecutionContext {
    pub repository_id: Uuid,
    pub commit_hash: String,
    pub branch: String,
    pub environment_variables: HashMap<String, String>,
    pub secrets: HashMap<String, String>,
}

impl PipelineService {
    pub fn new(db: PgPool) -> Self {
        let (job_sender, job_receiver) = mpsc::unbounded_channel();
        let runner_manager = RunnerManager::new(db.clone());

        // Start job processor
        let processor = JobProcessor::new(db.clone(), job_receiver, runner_manager.clone());
        tokio::spawn(async move {
            processor.start().await;
        });

        Self {
            db,
            job_queue: job_sender,
            runner_manager,
        }
    }

    /// Trigger pipeline execution from Git event
    pub async fn trigger_pipeline(
        &self,
        repository_id: Uuid,
        event: TriggerEvent,
        ref_name: String,
        commit_hash: String,
        user_id: Option<Uuid>,
    ) -> Result<PipelineRun, PipelineError> {
        // Get active pipeline for repository
        let pipeline = self.get_active_pipeline(repository_id).await?;

        // Parse pipeline configuration
        let config = self.parse_pipeline_config(&pipeline.config_content)?;

        // Check if event should trigger pipeline
        if !self.should_trigger(&config.on, &event, &ref_name) {
            return Err(PipelineError::TriggerNotMatched);
        }

        // Create pipeline run
        let pipeline_run = self.create_pipeline_run(
            pipeline.id,
            repository_id,
            event,
            ref_name,
            commit_hash,
            user_id,
            &config,
        ).await?;

        // Create and queue jobs
        self.create_and_queue_jobs(&pipeline_run, &config).await?;

        Ok(pipeline_run)
    }

    async fn get_active_pipeline(&self, repository_id: Uuid) -> Result<Pipeline, PipelineError> {
        let pipeline = sqlx::query_as::<_, Pipeline>(
            "SELECT * FROM pipelines WHERE repository_id = $1 AND is_active = true LIMIT 1"
        )
        .bind(repository_id)
        .fetch_optional(&self.db)
        .await?
        .ok_or(PipelineError::PipelineNotFound)?;

        Ok(pipeline)
    }

    fn parse_pipeline_config(&self, yaml_content: &str) -> Result<PipelineConfig, PipelineError> {
        serde_yaml::from_str(yaml_content)
            .map_err(|e| PipelineError::ConfigParseError(e.to_string()))
    }

    fn should_trigger(&self, trigger_config: &TriggerConfig, event: &TriggerEvent, ref_name: &str) -> bool {
        match event {
            TriggerEvent::Push => {
                if let Some(push_config) = &trigger_config.push {
                    if let Some(branches) = &push_config.branches {
                        return branches.iter().any(|pattern| self.matches_pattern(pattern, ref_name));
                    }
                    return true;
                }
                false
            }
            TriggerEvent::PullRequest => {
                trigger_config.pull_request.is_some()
            }
            TriggerEvent::Tag => {
                if let Some(tag_config) = &trigger_config.tag {
                    return self.matches_pattern(&tag_config.pattern, ref_name);
                }
                false
            }
            _ => true,
        }
    }

    fn matches_pattern(&self, pattern: &str, value: &str) -> bool {
        // Simple glob pattern matching
        if pattern == "*" {
            return true;
        }

        if pattern.contains('*') {
            let regex_pattern = pattern.replace('*', ".*");
            if let Ok(regex) = regex::Regex::new(&regex_pattern) {
                return regex.is_match(value);
            }
        }

        pattern == value
    }

    async fn create_pipeline_run(
        &self,
        pipeline_id: Uuid,
        repository_id: Uuid,
        event: TriggerEvent,
        ref_name: String,
        commit_hash: String,
        user_id: Option<Uuid>,
        config: &PipelineConfig,
    ) -> Result<PipelineRun, PipelineError> {
        let env_vars = config.env.clone().unwrap_or_default();
        let env_json = serde_json::to_value(env_vars)?;

        let pipeline_run = sqlx::query_as::<_, PipelineRun>(
            r#"
            INSERT INTO pipeline_runs (
                pipeline_id, repository_id, trigger_event, trigger_ref,
                trigger_commit, trigger_user_id, status, environment_variables, secrets
            )
            VALUES ($1, $2, $3, $4, $5, $6, 'queued', $7, '{}')
            RETURNING *
            "#
        )
        .bind(pipeline_id)
        .bind(repository_id)
        .bind(event)
        .bind(&ref_name)
        .bind(&commit_hash)
        .bind(user_id)
        .bind(&env_json)
        .fetch_one(&self.db)
        .await?;

        Ok(pipeline_run)
    }

    async fn create_and_queue_jobs(
        &self,
        pipeline_run: &PipelineRun,
        config: &PipelineConfig,
    ) -> Result<(), PipelineError> {
        let mut created_jobs = HashMap::new();

        // Create all jobs first
        for (job_name, job_config) in &config.jobs {
            let job = sqlx::query_as::<_, Job>(
                r#"
                INSERT INTO jobs (
                    pipeline_run_id, name, runner_type, status,
                    container_image, environment_variables, depends_on
                )
                VALUES ($1, $2, $3, 'pending', $4, $5, $6)
                RETURNING *
                "#
            )
            .bind(pipeline_run.id)
            .bind(job_name)
            .bind(self.determine_runner_type(&job_config.runs_on))
            .bind(job_config.container.as_ref().map(|c| &c.image))
            .bind(serde_json::to_value(&job_config.env).unwrap_or_default())
            .bind(serde_json::to_value(&job_config.needs).unwrap_or_default())
            .fetch_one(&self.db)
            .await?;

            created_jobs.insert(job_name.clone(), job);
        }

        // Queue jobs that have no dependencies
        for (job_name, job) in &created_jobs {
            let job_config = &config.jobs[job_name];

            if job_config.needs.is_none() || job_config.needs.as_ref().unwrap().is_empty() {
                self.queue_job(job.clone(), job_config.clone(), pipeline_run).await?;
            }
        }

        Ok(())
    }

    fn determine_runner_type(&self, runs_on: &str) -> RunnerType {
        match runs_on {
            "ubuntu-latest" | "ubuntu-20.04" | "ubuntu-22.04" => RunnerType::Docker,
            "self-hosted" => RunnerType::SelfHosted,
            "kubernetes" => RunnerType::Kubernetes,
            _ => RunnerType::Docker,
        }
    }

    async fn queue_job(
        &self,
        job: Job,
        job_config: JobConfig,
        pipeline_run: &PipelineRun,
    ) -> Result<(), PipelineError> {
        // Update job status to queued
        sqlx::query("UPDATE jobs SET status = 'queued' WHERE id = $1")
            .bind(job.id)
            .execute(&self.db)
            .await?;

        // Create execution context
        let context = ExecutionContext {
            repository_id: pipeline_run.repository_id,
            commit_hash: pipeline_run.trigger_commit.clone(),
            branch: pipeline_run.trigger_ref.clone(),
            environment_variables: serde_json::from_value(pipeline_run.environment_variables.clone())?,
            secrets: serde_json::from_value(pipeline_run.secrets.clone())?,
        };

        // Send to job queue
        let job_execution = JobExecution {
            job_id: job.id,
            pipeline_run_id: pipeline_run.id,
            config: job_config,
            context,
        };

        self.job_queue.send(job_execution)
            .map_err(|_| PipelineError::QueueError)?;

        Ok(())
    }

    /// Get pipeline run with all jobs and steps
    pub async fn get_pipeline_run_details(
        &self,
        run_id: Uuid,
    ) -> Result<PipelineRunResponse, PipelineError> {
        let pipeline_run = self.get_pipeline_run(run_id).await?;
        let jobs = self.get_pipeline_jobs(run_id).await?;

        let mut job_responses = Vec::new();
        for job in jobs {
            let steps = self.get_job_steps(job.id).await?;
            job_responses.push(JobResponse { job, steps });
        }

        Ok(PipelineRunResponse {
            pipeline_run,
            jobs: job_responses,
        })
    }

    async fn get_pipeline_run(&self, run_id: Uuid) -> Result<PipelineRun, PipelineError> {
        let run = sqlx::query_as::<_, PipelineRun>(
            "SELECT * FROM pipeline_runs WHERE id = $1"
        )
        .bind(run_id)
        .fetch_optional(&self.db)
        .await?
        .ok_or(PipelineError::PipelineRunNotFound)?;

        Ok(run)
    }

    async fn get_pipeline_jobs(&self, run_id: Uuid) -> Result<Vec<Job>, PipelineError> {
        let jobs = sqlx::query_as::<_, Job>(
            "SELECT * FROM jobs WHERE pipeline_run_id = $1 ORDER BY started_at ASC NULLS LAST"
        )
        .bind(run_id)
        .fetch_all(&self.db)
        .await?;

        Ok(jobs)
    }

    async fn get_job_steps(&self, job_id: Uuid) -> Result<Vec<JobStep>, PipelineError> {
        let steps = sqlx::query_as::<_, JobStep>(
            "SELECT * FROM job_steps WHERE job_id = $1 ORDER BY started_at ASC NULLS LAST"
        )
        .bind(job_id)
        .fetch_all(&self.db)
        .await?;

        Ok(steps)
    }
}

#[derive(Debug, Serialize)]
pub struct PipelineRunResponse {
    pub pipeline_run: PipelineRun,
    pub jobs: Vec<JobResponse>,
}

#[derive(Debug, Serialize)]
pub struct JobResponse {
    pub job: Job,
    pub steps: Vec<JobStep>,
}

#[derive(Debug, thiserror::Error)]
pub enum PipelineError {
    #[error("Pipeline not found")]
    PipelineNotFound,
    #[error("Pipeline run not found")]
    PipelineRunNotFound,
    #[error("Trigger not matched")]
    TriggerNotMatched,
    #[error("Config parse error: {0}")]
    ConfigParseError(String),
    #[error("Queue error")]
    QueueError,
    #[error("Database error: {0}")]
    DatabaseError(#[from] sqlx::Error),
    #[error("JSON error: {0}")]
    JsonError(#[from] serde_json::Error),
}
```

### Job Processor and Runner Management

```rust
// src/cicd/processor.rs
use sqlx::PgPool;
use tokio::sync::mpsc;
use uuid::Uuid;
use std::collections::HashMap;
use crate::cicd::*;

pub struct JobProcessor {
    db: PgPool,
    job_receiver: mpsc::UnboundedReceiver<JobExecution>,
    runner_manager: RunnerManager,
}

impl JobProcessor {
    pub fn new(
        db: PgPool,
        job_receiver: mpsc::UnboundedReceiver<JobExecution>,
        runner_manager: RunnerManager,
    ) -> Self {
        Self {
            db,
            job_receiver,
            runner_manager,
        }
    }

    pub async fn start(mut self) {
        while let Some(job_execution) = self.job_receiver.recv().await {
            if let Err(e) = self.process_job(job_execution).await {
                tracing::error!("Failed to process job: {}", e);
            }
        }
    }

    async fn process_job(&self, job_execution: JobExecution) -> Result<(), PipelineError> {
        // Find available runner
        let runner = self.runner_manager
            .find_available_runner(job_execution.config.runs_on.clone())
            .await?;

        // Update job status to running
        sqlx::query(
            "UPDATE jobs SET status = 'running', runner_id = $1, started_at = NOW() WHERE id = $2"
        )
        .bind(&runner.id.to_string())
        .bind(job_execution.job_id)
        .execute(&self.db)
        .await?;

        // Execute job on runner
        let result = self.execute_job_on_runner(&job_execution, &runner).await;

        // Update job status based on result
        let (status, finished_at) = match result {
            Ok(_) => ("success", "NOW()"),
            Err(_) => ("failed", "NOW()"),
        };

        sqlx::query(&format!(
            "UPDATE jobs SET status = '{}', finished_at = {}, duration_seconds = EXTRACT(EPOCH FROM (NOW() - started_at)) WHERE id = $1",
            status, finished_at
        ))
        .bind(job_execution.job_id)
        .execute(&self.db)
        .await?;

        // Check if pipeline run is complete
        self.check_pipeline_completion(job_execution.pipeline_run_id).await?;

        Ok(())
    }

    async fn execute_job_on_runner(
        &self,
        job_execution: &JobExecution,
        runner: &BuildRunner,
    ) -> Result<(), PipelineError> {
        match runner.runner_type {
            RunnerType::Docker => {
                self.execute_docker_job(job_execution, runner).await
            }
            RunnerType::Kubernetes => {
                self.execute_k8s_job(job_execution, runner).await
            }
            RunnerType::SelfHosted => {
                self.execute_self_hosted_job(job_execution, runner).await
            }
            RunnerType::Cloud => {
                self.execute_cloud_job(job_execution, runner).await
            }
        }
    }

    async fn execute_docker_job(
        &self,
        job_execution: &JobExecution,
        runner: &BuildRunner,
    ) -> Result<(), PipelineError> {
        // Create job steps
        for (step_index, step_config) in job_execution.config.steps.iter().enumerate() {
            let step = self.create_job_step(
                job_execution.job_id,
                step_config,
                step_index as i32,
            ).await?;

            // Execute step
            let step_result = self.execute_step(&step, step_config, job_execution).await;

            // Update step status
            let (status, exit_code) = match step_result {
                Ok(code) => (StepStatus::Success, Some(code)),
                Err(_) => (StepStatus::Failed, Some(1)),
            };

            sqlx::query(
                r#"
                UPDATE job_steps
                SET status = $1, exit_code = $2, finished_at = NOW()
                WHERE id = $3
                "#
            )
            .bind(status)
            .bind(exit_code)
            .bind(step.id)
            .execute(&self.db)
            .await?;

            // Stop on failure
            if matches!(status, StepStatus::Failed) {
                return Err(PipelineError::StepFailed);
            }
        }

        Ok(())
    }

    async fn create_job_step(
        &self,
        job_id: Uuid,
        step_config: &StepConfig,
        position: i32,
    ) -> Result<JobStep, PipelineError> {
        let step_type = if step_config.uses.is_some() {
            StepType::Action
        } else if step_config.run.is_some() {
            StepType::Run
        } else {
            StepType::Run
        };

        let step = sqlx::query_as::<_, JobStep>(
            r#"
            INSERT INTO job_steps (
                job_id, name, step_type, command, action, parameters, status, started_at
            )
            VALUES ($1, $2, $3, $4, $5, $6, 'running', NOW())
            RETURNING *
            "#
        )
        .bind(job_id)
        .bind(&step_config.name)
        .bind(step_type)
        .bind(&step_config.run)
        .bind(&step_config.uses)
        .bind(serde_json::to_value(&step_config.with).unwrap_or_default())
        .fetch_one(&self.db)
        .await?;

        Ok(step)
    }

    async fn execute_step(
        &self,
        step: &JobStep,
        step_config: &StepConfig,
        job_execution: &JobExecution,
    ) -> Result<i32, PipelineError> {
        if let Some(command) = &step_config.run {
            // Execute shell command
            self.execute_shell_command(command, job_execution).await
        } else if let Some(action) = &step_config.uses {
            // Execute action
            self.execute_action(action, step_config, job_execution).await
        } else {
            Ok(0)
        }
    }

    async fn execute_shell_command(
        &self,
        command: &str,
        job_execution: &JobExecution,
    ) -> Result<i32, PipelineError> {
        use tokio::process::Command;

        let mut cmd = Command::new("sh");
        cmd.arg("-c").arg(command);

        // Set environment variables
        for (key, value) in &job_execution.context.environment_variables {
            cmd.env(key, value);
        }

        let output = cmd.output().await
            .map_err(|e| PipelineError::ExecutionError(e.to_string()))?;

        Ok(output.status.code().unwrap_or(1))
    }

    async fn execute_action(
        &self,
        action: &str,
        step_config: &StepConfig,
        job_execution: &JobExecution,
    ) -> Result<i32, PipelineError> {
        // Built-in actions
        match action {
            "actions/checkout@v4" => {
                self.checkout_action(job_execution).await
            }
            "actions/cache@v3" => {
                self.cache_action(step_config, job_execution).await
            }
            "actions/upload-artifact@v3" => {
                self.upload_artifact_action(step_config, job_execution).await
            }
            "actions/download-artifact@v3" => {
                self.download_artifact_action(step_config, job_execution).await
            }
            _ => {
                // Custom action - would load and execute
                Ok(0)
            }
        }
    }

    async fn checkout_action(&self, job_execution: &JobExecution) -> Result<i32, PipelineError> {
        // Implementation would clone the repository
        tracing::info!("Checking out repository for job {}", job_execution.job_id);
        Ok(0)
    }

    async fn cache_action(&self, step_config: &StepConfig, job_execution: &JobExecution) -> Result<i32, PipelineError> {
        // Implementation would handle caching
        tracing::info!("Cache action for job {}", job_execution.job_id);
        Ok(0)
    }

    async fn upload_artifact_action(&self, step_config: &StepConfig, job_execution: &JobExecution) -> Result<i32, PipelineError> {
        // Implementation would upload artifacts
        tracing::info!("Upload artifact for job {}", job_execution.job_id);
        Ok(0)
    }

    async fn download_artifact_action(&self, step_config: &StepConfig, job_execution: &JobExecution) -> Result<i32, PipelineError> {
        // Implementation would download artifacts
        tracing::info!("Download artifact for job {}", job_execution.job_id);
        Ok(0)
    }

    async fn execute_k8s_job(&self, job_execution: &JobExecution, runner: &BuildRunner) -> Result<(), PipelineError> {
        // Implementation would create Kubernetes Job
        Ok(())
    }

    async fn execute_self_hosted_job(&self, job_execution: &JobExecution, runner: &BuildRunner) -> Result<(), PipelineError> {
        // Implementation would execute on self-hosted runner
        Ok(())
    }

    async fn execute_cloud_job(&self, job_execution: &JobExecution, runner: &BuildRunner) -> Result<(), PipelineError> {
        // Implementation would execute on cloud runner
        Ok(())
    }

    async fn check_pipeline_completion(&self, pipeline_run_id: Uuid) -> Result<(), PipelineError> {
        // Check if all jobs are complete
        let incomplete_jobs: i64 = sqlx::query_scalar(
            "SELECT COUNT(*) FROM jobs WHERE pipeline_run_id = $1 AND status NOT IN ('success', 'failed', 'cancelled')"
        )
        .bind(pipeline_run_id)
        .fetch_one(&self.db)
        .await?;

        if incomplete_jobs == 0 {
            // All jobs complete, update pipeline run status
            let failed_jobs: i64 = sqlx::query_scalar(
                "SELECT COUNT(*) FROM jobs WHERE pipeline_run_id = $1 AND status = 'failed'"
            )
            .bind(pipeline_run_id)
            .fetch_one(&self.db)
            .await?;

            let final_status = if failed_jobs > 0 { "failed" } else { "success" };

            sqlx::query(&format!(
                "UPDATE pipeline_runs SET status = '{}', finished_at = NOW(), duration_seconds = EXTRACT(EPOCH FROM (NOW() - started_at)) WHERE id = $1",
                final_status
            ))
            .bind(pipeline_run_id)
            .execute(&self.db)
            .await?;
        }

        Ok(())
    }
}

#[derive(Debug, Clone)]
pub struct RunnerManager {
    db: PgPool,
}

impl RunnerManager {
    pub fn new(db: PgPool) -> Self {
        Self { db }
    }

    pub async fn find_available_runner(&self, runs_on: String) -> Result<BuildRunner, PipelineError> {
        let runner = sqlx::query_as::<_, BuildRunner>(
            r#"
            SELECT * FROM build_runners
            WHERE is_online = true AND is_busy = false
            AND runner_type = $1
            ORDER BY last_heartbeat DESC
            LIMIT 1
            "#
        )
        .bind(self.map_runs_on_to_runner_type(&runs_on))
        .fetch_optional(&self.db)
        .await?
        .ok_or(PipelineError::NoAvailableRunner)?;

        // Mark runner as busy
        sqlx::query("UPDATE build_runners SET is_busy = true WHERE id = $1")
            .bind(runner.id)
            .execute(&self.db)
            .await?;

        Ok(runner)
    }

    fn map_runs_on_to_runner_type(&self, runs_on: &str) -> RunnerType {
        match runs_on {
            "ubuntu-latest" | "ubuntu-20.04" | "ubuntu-22.04" => RunnerType::Docker,
            "self-hosted" => RunnerType::SelfHosted,
            "kubernetes" => RunnerType::Kubernetes,
            _ => RunnerType::Docker,
        }
    }
}

#[derive(Debug, thiserror::Error)]
pub enum PipelineError {
    #[error("Step failed")]
    StepFailed,
    #[error("No available runner")]
    NoAvailableRunner,
    #[error("Execution error: {0}")]
    ExecutionError(String),
    #[error("Database error: {0}")]
    DatabaseError(#[from] sqlx::Error),
}
```

## 📦 Artifact Management System

### Artifact Storage Service

```rust
// src/cicd/artifacts.rs
use sqlx::PgPool;
use uuid::Uuid;
use tokio::fs;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct Artifact {
    pub id: Uuid,
    pub pipeline_run_id: Uuid,
    pub job_id: Uuid,
    pub name: String,
    pub file_path: String,
    pub size_bytes: i64,
    pub content_type: String,
    pub checksum: String,
    pub created_at: DateTime<Utc>,
    pub expires_at: Option<DateTime<Utc>>,
}

pub struct ArtifactService {
    db: PgPool,
    storage_path: String,
}

impl ArtifactService {
    pub fn new(db: PgPool, storage_path: String) -> Self {
        Self { db, storage_path }
    }

    /// Upload artifact
    pub async fn upload_artifact(
        &self,
        pipeline_run_id: Uuid,
        job_id: Uuid,
        name: String,
        data: Vec<u8>,
        content_type: String,
    ) -> Result<Artifact, ArtifactError> {
        // Calculate checksum
        let checksum = sha2::Sha256::digest(&data);
        let checksum_hex = format!("{:x}", checksum);

        // Generate storage path
        let artifact_id = Uuid::new_v4();
        let storage_path = self.get_artifact_path(&artifact_id);

        // Ensure directory exists
        if let Some(parent) = std::path::Path::new(&storage_path).parent() {
            fs::create_dir_all(parent).await?;
        }

        // Write file
        fs::write(&storage_path, &data).await?;

        // Store metadata in database
        let artifact = sqlx::query_as::<_, Artifact>(
            r#"
            INSERT INTO artifacts (
                id, pipeline_run_id, job_id, name, file_path,
                size_bytes, content_type, checksum, expires_at
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW() + INTERVAL '30 days')
            RETURNING *
            "#
        )
        .bind(artifact_id)
        .bind(pipeline_run_id)
        .bind(job_id)
        .bind(&name)
        .bind(&storage_path)
        .bind(data.len() as i64)
        .bind(&content_type)
        .bind(&checksum_hex)
        .fetch_one(&self.db)
        .await?;

        Ok(artifact)
    }

    /// Download artifact
    pub async fn download_artifact(&self, artifact_id: Uuid) -> Result<Vec<u8>, ArtifactError> {
        let artifact = self.get_artifact(artifact_id).await?;

        // Check if expired
        if let Some(expires_at) = artifact.expires_at {
            if expires_at < Utc::now() {
                return Err(ArtifactError::ArtifactExpired);
            }
        }

        // Read file
        let data = fs::read(&artifact.file_path).await?;

        // Verify checksum
        let checksum = sha2::Sha256::digest(&data);
        let checksum_hex = format!("{:x}", checksum);

        if checksum_hex != artifact.checksum {
            return Err(ArtifactError::ChecksumMismatch);
        }

        Ok(data)
    }

    /// List artifacts for pipeline run
    pub async fn list_artifacts(&self, pipeline_run_id: Uuid) -> Result<Vec<Artifact>, ArtifactError> {
        let artifacts = sqlx::query_as::<_, Artifact>(
            "SELECT * FROM artifacts WHERE pipeline_run_id = $1 ORDER BY created_at DESC"
        )
        .bind(pipeline_run_id)
        .fetch_all(&self.db)
        .await?;

        Ok(artifacts)
    }

    /// Clean up expired artifacts
    pub async fn cleanup_expired_artifacts(&self) -> Result<i64, ArtifactError> {
        let expired_artifacts = sqlx::query_as::<_, Artifact>(
            "SELECT * FROM artifacts WHERE expires_at < NOW()"
        )
        .fetch_all(&self.db)
        .await?;

        let mut deleted_count = 0;

        for artifact in expired_artifacts {
            // Delete file
            if let Err(e) = fs::remove_file(&artifact.file_path).await {
                tracing::warn!("Failed to delete artifact file {}: {}", artifact.file_path, e);
            }

            // Delete database record
            sqlx::query("DELETE FROM artifacts WHERE id = $1")
                .bind(artifact.id)
                .execute(&self.db)
                .await?;

            deleted_count += 1;
        }

        Ok(deleted_count)
    }

    async fn get_artifact(&self, artifact_id: Uuid) -> Result<Artifact, ArtifactError> {
        let artifact = sqlx::query_as::<_, Artifact>(
            "SELECT * FROM artifacts WHERE id = $1"
        )
        .bind(artifact_id)
        .fetch_optional(&self.db)
        .await?
        .ok_or(ArtifactError::ArtifactNotFound)?;

        Ok(artifact)
    }

    fn get_artifact_path(&self, artifact_id: &Uuid) -> String {
        let id_str = artifact_id.to_string();
        let prefix = &id_str[0..2];
        let suffix = &id_str[2..4];
        format!("{}/{}/{}/{}", self.storage_path, prefix, suffix, id_str)
    }
}

#[derive(Debug, thiserror::Error)]
pub enum ArtifactError {
    #[error("Artifact not found")]
    ArtifactNotFound,
    #[error("Artifact expired")]
    ArtifactExpired,
    #[error("Checksum mismatch")]
    ChecksumMismatch,
    #[error("IO error: {0}")]
    IoError(#[from] tokio::io::Error),
    #[error("Database error: {0}")]
    DatabaseError(#[from] sqlx::Error),
}
```

## 🎨 Angular Pipeline Visualization

### Pipeline Dashboard Component

```typescript
// components/pipeline-dashboard/pipeline-dashboard.component.ts
import { Component, OnInit, Input } from '@angular/core';
import { Observable, interval } from 'rxjs';
import { switchMap, startWith } from 'rxjs/operators';

export interface PipelineRun {
  id: string;
  pipelineId: string;
  triggerEvent: 'push' | 'pull_request' | 'tag' | 'manual';
  triggerRef: string;
  triggerCommit: string;
  status: 'queued' | 'running' | 'success' | 'failed' | 'cancelled';
  startedAt: Date;
  finishedAt?: Date;
  durationSeconds?: number;
  jobs: JobRun[];
}

export interface JobRun {
  id: string;
  name: string;
  status: 'pending' | 'queued' | 'running' | 'success' | 'failed' | 'cancelled';
  startedAt?: Date;
  finishedAt?: Date;
  durationSeconds?: number;
  steps: StepRun[];
}

export interface StepRun {
  id: string;
  name: string;
  status: 'pending' | 'running' | 'success' | 'failed' | 'skipped';
  startedAt?: Date;
  finishedAt?: Date;
  exitCode?: number;
}

@Component({
  selector: 'app-pipeline-dashboard',
  template: `
    <div class="pipeline-dashboard">
      <div class="dashboard-header">
        <h2>CI/CD Pipelines</h2>
        <div class="actions">
          <button mat-raised-button color="primary" (click)="triggerPipeline()">
            <mat-icon>play_arrow</mat-icon>
            Run Pipeline
          </button>
          <button mat-button (click)="refreshPipelines()">
            <mat-icon>refresh</mat-icon>
            Refresh
          </button>
        </div>
      </div>

      <!-- Pipeline Runs List -->
      <div class="pipeline-runs" *ngIf="pipelineRuns$ | async as runs">
        <mat-card *ngFor="let run of runs" class="pipeline-run-card">
          <mat-card-header>
            <div mat-card-avatar>
              <mat-icon [class]="'status-' + run.status">
                {{ getStatusIcon(run.status) }}
              </mat-icon>
            </div>

            <mat-card-title>
              <div class="run-title">
                <span class="trigger-info">
                  {{ run.triggerEvent }} on {{ run.triggerRef }}
                </span>
                <mat-chip [class]="'status-' + run.status">
                  {{ run.status | titlecase }}
                </mat-chip>
              </div>
            </mat-card-title>

            <mat-card-subtitle>
              <div class="run-meta">
                <span class="commit">{{ run.triggerCommit.substring(0, 8) }}</span>
                <span class="date">{{ run.startedAt | timeAgo }}</span>
                <span *ngIf="run.durationSeconds" class="duration">
                  {{ formatDuration(run.durationSeconds) }}
                </span>
              </div>
            </mat-card-subtitle>
          </mat-card-header>

          <mat-card-content>
            <!-- Jobs Progress -->
            <div class="jobs-progress">
              <div class="progress-bar">
                <div
                  *ngFor="let job of run.jobs"
                  class="job-segment"
                  [class]="'status-' + job.status"
                  [style.width.%]="100 / run.jobs.length"
                  [matTooltip]="job.name + ': ' + job.status">
                </div>
              </div>

              <div class="jobs-summary">
                <span class="job-count">{{ run.jobs.length }} jobs</span>
                <span class="success-count" *ngIf="getJobCountByStatus(run.jobs, 'success') > 0">
                  {{ getJobCountByStatus(run.jobs, 'success') }} passed
                </span>
                <span class="failed-count" *ngIf="getJobCountByStatus(run.jobs, 'failed') > 0">
                  {{ getJobCountByStatus(run.jobs, 'failed') }} failed
                </span>
              </div>
            </div>

            <!-- Job Details (Expandable) -->
            <mat-expansion-panel class="job-details">
              <mat-expansion-panel-header>
                <mat-panel-title>Job Details</mat-panel-title>
              </mat-expansion-panel-header>

              <div class="jobs-list">
                <div *ngFor="let job of run.jobs" class="job-item">
                  <div class="job-header">
                    <mat-icon [class]="'status-' + job.status">
                      {{ getStatusIcon(job.status) }}
                    </mat-icon>
                    <span class="job-name">{{ job.name }}</span>
                    <span class="job-duration" *ngIf="job.durationSeconds">
                      {{ formatDuration(job.durationSeconds) }}
                    </span>
                  </div>

                  <!-- Steps -->
                  <div class="steps-list" *ngIf="job.steps.length > 0">
                    <div *ngFor="let step of job.steps" class="step-item">
                      <mat-icon [class]="'status-' + step.status" class="step-icon">
                        {{ getStepIcon(step.status) }}
                      </mat-icon>
                      <span class="step-name">{{ step.name }}</span>
                      <span class="step-exit-code" *ngIf="step.exitCode !== undefined">
                        ({{ step.exitCode }})
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </mat-expansion-panel>
          </mat-card-content>

          <mat-card-actions align="end">
            <button mat-button (click)="viewLogs(run)">
              <mat-icon>description</mat-icon>
              View Logs
            </button>
            <button mat-button (click)="viewArtifacts(run)">
              <mat-icon>archive</mat-icon>
              Artifacts
            </button>
            <button
              mat-button
              color="warn"
              *ngIf="run.status === 'running'"
              (click)="cancelPipeline(run)">
              <mat-icon>stop</mat-icon>
              Cancel
            </button>
          </mat-card-actions>
        </mat-card>
      </div>

      <!-- Empty State -->
      <div *ngIf="(pipelineRuns$ | async)?.length === 0" class="no-pipelines">
        <mat-icon>build_circle</mat-icon>
        <h3>No pipeline runs</h3>
        <p>Trigger your first pipeline run to see it here.</p>
        <button mat-raised-button color="primary" (click)="triggerPipeline()">
          Run Pipeline
        </button>
      </div>
    </div>
  `,
  styleUrls: ['./pipeline-dashboard.component.scss']
})
export class PipelineDashboardComponent implements OnInit {
  @Input() repositoryId!: string;

  pipelineRuns$: Observable<PipelineRun[]>;

  constructor(
    private pipelineService: PipelineService,
    private dialog: MatDialog,
    private router: Router
  ) {
    // Auto-refresh every 30 seconds
    this.pipelineRuns$ = interval(30000).pipe(
      startWith(0),
      switchMap(() => this.pipelineService.getPipelineRuns(this.repositoryId))
    );
  }

  ngOnInit(): void {}

  refreshPipelines(): void {
    // Trigger refresh by resubscribing
    this.pipelineRuns$ = this.pipelineService.getPipelineRuns(this.repositoryId);
  }

  triggerPipeline(): void {
    const dialogRef = this.dialog.open(TriggerPipelineDialogComponent, {
      width: '500px',
      data: { repositoryId: this.repositoryId }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.refreshPipelines();
      }
    });
  }

  viewLogs(run: PipelineRun): void {
    this.router.navigate(['/repositories', this.repositoryId, 'pipelines', run.id, 'logs']);
  }

  viewArtifacts(run: PipelineRun): void {
    this.router.navigate(['/repositories', this.repositoryId, 'pipelines', run.id, 'artifacts']);
  }

  cancelPipeline(run: PipelineRun): void {
    this.pipelineService.cancelPipelineRun(run.id).subscribe(() => {
      this.refreshPipelines();
    });
  }

  getStatusIcon(status: string): string {
    switch (status) {
      case 'queued': return 'schedule';
      case 'running': return 'play_circle';
      case 'success': return 'check_circle';
      case 'failed': return 'error';
      case 'cancelled': return 'cancel';
      default: return 'help';
    }
  }

  getStepIcon(status: string): string {
    switch (status) {
      case 'pending': return 'radio_button_unchecked';
      case 'running': return 'play_circle_outline';
      case 'success': return 'check_circle_outline';
      case 'failed': return 'error_outline';
      case 'skipped': return 'skip_next';
      default: return 'help_outline';
    }
  }

  getJobCountByStatus(jobs: JobRun[], status: string): number {
    return jobs.filter(job => job.status === status).length;
  }

  formatDuration(seconds: number): string {
    if (seconds < 60) {
      return `${seconds}s`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}m ${remainingSeconds}s`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours}h ${minutes}m`;
    }
  }
}
```

### Pipeline Logs Viewer Component

```typescript
// components/pipeline-logs/pipeline-logs.component.ts
import { Component, OnInit, OnDestroy, ViewChild, ElementRef } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subject, interval } from 'rxjs';
import { takeUntil, switchMap } from 'rxjs/operators';

@Component({
  selector: 'app-pipeline-logs',
  template: `
    <div class="pipeline-logs">
      <div class="logs-header">
        <h3>Pipeline Logs</h3>
        <div class="controls">
          <mat-slide-toggle [(ngModel)]="autoScroll">
            Auto-scroll
          </mat-slide-toggle>
          <mat-slide-toggle [(ngModel)]="autoRefresh">
            Auto-refresh
          </mat-slide-toggle>
          <button mat-button (click)="downloadLogs()">
            <mat-icon>download</mat-icon>
            Download
          </button>
        </div>
      </div>

      <!-- Job Tabs -->
      <mat-tab-group [(selectedIndex)]="selectedJobIndex">
        <mat-tab *ngFor="let job of jobs; let i = index" [label]="job.name">
          <div class="job-logs">
            <!-- Step Selector -->
            <mat-form-field appearance="outline" class="step-selector">
              <mat-label>Step</mat-label>
              <mat-select [(value)]="selectedStepIds[i]">
                <mat-option value="all">All Steps</mat-option>
                <mat-option *ngFor="let step of job.steps" [value]="step.id">
                  {{ step.name }}
                </mat-option>
              </mat-select>
            </mat-form-field>

            <!-- Log Output -->
            <div class="log-container" #logContainer>
              <div class="log-content">
                <div
                  *ngFor="let logLine of getFilteredLogs(job, selectedStepIds[i]); let lineIndex = index"
                  class="log-line"
                  [class.error]="logLine.level === 'error'"
                  [class.warning]="logLine.level === 'warning'"
                  [class.info]="logLine.level === 'info'">
                  <span class="line-number">{{ lineIndex + 1 }}</span>
                  <span class="timestamp">{{ logLine.timestamp | date:'HH:mm:ss.SSS' }}</span>
                  <span class="step-name" *ngIf="selectedStepIds[i] === 'all'">
                    [{{ logLine.stepName }}]
                  </span>
                  <span class="log-message">{{ logLine.message }}</span>
                </div>
              </div>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  `,
  styleUrls: ['./pipeline-logs.component.scss']
})
export class PipelineLogsComponent implements OnInit, OnDestroy {
  @ViewChild('logContainer') logContainer!: ElementRef;

  private destroy$ = new Subject<void>();

  pipelineRunId!: string;
  jobs: JobWithLogs[] = [];
  selectedJobIndex = 0;
  selectedStepIds: string[] = [];
  autoScroll = true;
  autoRefresh = true;

  constructor(
    private route: ActivatedRoute,
    private pipelineService: PipelineService
  ) {}

  ngOnInit(): void {
    this.pipelineRunId = this.route.snapshot.params['runId'];

    // Initial load
    this.loadLogs();

    // Auto-refresh if enabled
    interval(5000).pipe(
      takeUntil(this.destroy$),
      switchMap(() => this.autoRefresh ? this.pipelineService.getPipelineLogs(this.pipelineRunId) : [])
    ).subscribe(logs => {
      this.updateLogs(logs);
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadLogs(): void {
    this.pipelineService.getPipelineLogs(this.pipelineRunId)
      .subscribe(logs => {
        this.updateLogs(logs);
      });
  }

  private updateLogs(logs: JobWithLogs[]): void {
    const previousLength = this.jobs.reduce((sum, job) => sum + job.logs.length, 0);

    this.jobs = logs;

    // Initialize step selectors
    if (this.selectedStepIds.length === 0) {
      this.selectedStepIds = this.jobs.map(() => 'all');
    }

    // Auto-scroll if new logs and auto-scroll enabled
    const newLength = this.jobs.reduce((sum, job) => sum + job.logs.length, 0);
    if (this.autoScroll && newLength > previousLength) {
      setTimeout(() => this.scrollToBottom(), 100);
    }
  }

  private scrollToBottom(): void {
    if (this.logContainer) {
      const element = this.logContainer.nativeElement;
      element.scrollTop = element.scrollHeight;
    }
  }

  getFilteredLogs(job: JobWithLogs, selectedStepId: string): LogLine[] {
    if (selectedStepId === 'all') {
      return job.logs;
    }

    return job.logs.filter(log => log.stepId === selectedStepId);
  }

  downloadLogs(): void {
    const allLogs = this.jobs.flatMap(job =>
      job.logs.map(log =>
        `${log.timestamp.toISOString()} [${job.name}:${log.stepName}] ${log.message}`
      )
    ).join('\n');

    const blob = new Blob([allLogs], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `pipeline-${this.pipelineRunId}-logs.txt`;
    link.click();
    window.URL.revokeObjectURL(url);
  }
}

interface JobWithLogs {
  id: string;
  name: string;
  steps: Array<{ id: string; name: string; }>;
  logs: LogLine[];
}

interface LogLine {
  stepId: string;
  stepName: string;
  timestamp: Date;
  level: 'info' | 'warning' | 'error';
  message: string;
}
```

## 🎯 Key Takeaways

### CI/CD Integration Benefits

1. **Automated Quality Gates**: Prevent bad code from reaching production
2. **Developer Productivity**: Reduce manual deployment overhead
3. **Consistent Environments**: Same configuration from dev to production
4. **Audit Trail**: Complete history of all deployments and changes
5. **Rapid Feedback**: Quick notification of build and test results

### Advanced Features Implemented

- **YAML Configuration**: GitHub Actions-compatible pipeline definitions
- **Multi-Runner Support**: Docker, Kubernetes, self-hosted, and cloud runners
- **Artifact Management**: Secure storage and retrieval of build outputs
- **Real-time Monitoring**: Live pipeline status and log streaming
- **Environment Management**: Separate staging and production deployments
- **Notification System**: Slack, email, and webhook integrations

### Performance Considerations

- **Job Queuing**: Efficient job scheduling and resource allocation
- **Parallel Execution**: Run independent jobs simultaneously
- **Caching**: Speed up builds with dependency and artifact caching
- **Log Streaming**: Real-time log delivery without overwhelming the system
- **Artifact Cleanup**: Automatic cleanup of expired build artifacts

### Security Best Practices

- **Secret Management**: Secure storage and injection of sensitive data
- **Sandboxed Execution**: Isolated build environments
- **Permission Checks**: Verify user access to trigger pipelines
- **Audit Logging**: Track all pipeline activities
- **Vulnerability Scanning**: Automated security checks in pipelines

Ready to continue with additional advanced modules or would you like me to create a comprehensive summary of all the modules we've built?
