# Getting Started with Rusty Git Server

## 🚀 Quick Start

### Prerequisites

1. **Install Rust** (if not already installed):
   ```bash
   curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
   source ~/.cargo/env
   ```

2. **Verify Installation**:
   ```bash
   rustc --version
   cargo --version
   ```

### Running the Server

1. **Navigate to the backend directory**:
   ```bash
   cd rusty-git-server/backend
   ```

2. **Install dependencies and run**:
   ```bash
   cargo run
   ```

3. **Test the server**:
   ```bash
   # In another terminal
   curl http://localhost:3000
   # Should return: "Welcome to Rusty Git Server! 🦀"
   
   curl http://localhost:3000/repositories
   # Should return JSON with repository list
   ```

### Running Tests

```bash
# Run all tests
cargo test

# Run tests with output
cargo test -- --nocapture

# Run specific test module
cargo test git::tests

# Run with coverage (requires cargo-tarpaulin)
cargo install cargo-tarpaulin
cargo tarpaulin --out Html
```

## 📚 Tutorial Progress

### ✅ Completed Modules

#### Module 1: Project Setup and Rust Fundamentals
- [x] Rust installation and tooling setup
- [x] Basic HTTP server with Axum
- [x] Understanding ownership, borrowing, and lifetimes
- [x] Error handling patterns
- [x] Unit testing with cargo test

**Key Files Created**:
- `backend/Cargo.toml` - Project dependencies
- `backend/src/main.rs` - HTTP server implementation
- `backend/src/lib.rs` - Library code with tests

#### Module 2: Git Protocol Implementation (In Progress)
- [x] Git object model implementation (Blob, Tree, Commit, Tag)
- [x] SHA-1 hashing and serialization
- [x] Custom error types with thiserror
- [x] Comprehensive test suite for Git objects
- [ ] Repository creation and management
- [ ] Git smart HTTP protocol
- [ ] File system operations

**Key Files Created**:
- `backend/src/git/mod.rs` - Git object implementations

### 🔄 Next Steps

1. **Complete Module 2**: Repository management and Git protocol
2. **Module 3**: Authentication and authorization
3. **Module 4**: Angular frontend setup
4. **Module 5**: Advanced Git operations
5. **Module 6**: Real-time features with WebSockets
6. **Module 7**: Performance and scalability
7. **Module 8**: CI/CD pipeline setup

## 🧪 Testing Your Understanding

### Rust Concepts Covered So Far

1. **Ownership Model**:
   ```rust
   let data = String::from("hello");  // `data` owns the string
   let data2 = data;                  // Ownership moved to `data2`
   // println!("{}", data);           // ❌ Compile error - `data` no longer valid
   ```

2. **Borrowing**:
   ```rust
   fn print_length(s: &String) {      // Borrows the string
       println!("Length: {}", s.len());
   }
   
   let name = String::from("hello");
   print_length(&name);               // Lend the string
   println!("{}", name);              // ✅ Still can use `name`
   ```

3. **Pattern Matching**:
   ```rust
   match result {
       Ok(value) => println!("Success: {}", value),
       Err(error) => println!("Error: {}", error),
   }
   ```

4. **Error Handling**:
   ```rust
   #[derive(Error, Debug)]
   pub enum MyError {
       #[error("Something went wrong: {message}")]
       SomethingWrong { message: String },
   }
   ```

### Git Concepts Implemented

1. **Object Model**: Blob, Tree, Commit, Tag objects
2. **Content Addressing**: SHA-1 hashing for object identification
3. **Serialization**: Git's internal object format
4. **Immutability**: Objects never change once created

## 🔧 Development Tips

### VS Code Setup

Install these extensions for the best development experience:
- `rust-analyzer` - Intelligent code completion and error checking
- `CodeLLDB` - Debugging support
- `crates` - Dependency management
- `Better TOML` - Cargo.toml syntax highlighting

### Useful Cargo Commands

```bash
# Check code without building
cargo check

# Format code
cargo fmt

# Lint code
cargo clippy

# Build optimized release version
cargo build --release

# Run with environment variables
RUST_LOG=debug cargo run

# Generate documentation
cargo doc --open
```

### Debugging Tips

1. **Use `dbg!` macro for quick debugging**:
   ```rust
   let result = some_function();
   dbg!(&result);  // Prints debug info
   ```

2. **Enable logging**:
   ```rust
   use tracing::{info, warn, error};
   
   info!("Server starting on port 3000");
   warn!("This is a warning");
   error!("Something went wrong: {}", error);
   ```

3. **Use `unwrap_or_else` for better error messages**:
   ```rust
   let result = risky_operation()
       .unwrap_or_else(|e| panic!("Failed to do something: {}", e));
   ```

## 🎯 Learning Objectives Check

After completing the current modules, you should be able to:

- [ ] Explain Rust's ownership model and why it prevents memory leaks
- [ ] Create and manage Rust projects with Cargo
- [ ] Implement HTTP servers using async/await
- [ ] Write comprehensive unit tests
- [ ] Handle errors gracefully with custom error types
- [ ] Understand Git's internal object model
- [ ] Implement content-addressable storage systems

## 🤝 Contributing

As you work through the tutorial, feel free to:
1. Add more test cases
2. Improve error messages
3. Add documentation comments
4. Optimize performance
5. Add new features

## 📖 Additional Resources

- [The Rust Book](https://doc.rust-lang.org/book/) - Official Rust documentation
- [Rust by Example](https://doc.rust-lang.org/rust-by-example/) - Learn by examples
- [Tokio Tutorial](https://tokio.rs/tokio/tutorial) - Async programming in Rust
- [Git Internals](https://git-scm.com/book/en/v2/Git-Internals-Plumbing-and-Porcelain) - How Git works internally

---

**Ready to continue?** Check out the next module in the [docs](./docs/) directory!
