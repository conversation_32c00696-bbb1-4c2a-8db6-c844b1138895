# Module 13: Advanced Security & Compliance

## 🎯 Learning Objectives

By the end of this module, you will:
- Implement enterprise-grade LDAP/Active Directory integration
- Build SAML and OIDC single sign-on capabilities
- Create automated security scanning for vulnerabilities and secrets
- Develop compliance reporting for SOC2, GDPR, and HIPAA
- Master advanced access controls and security policies
- Build comprehensive audit logging and monitoring systems

## 🔒 Why Advanced Security Matters

Enterprise adoption requires robust security and compliance features:
- **Regulatory Compliance**: Meet SOC2, GDPR, HIPAA, and other standards
- **Enterprise Integration**: Seamless integration with existing identity systems
- **Risk Mitigation**: Proactive security scanning and threat detection
- **Audit Requirements**: Comprehensive logging for security audits
- **Zero Trust Architecture**: Never trust, always verify approach

### Security Architecture Overview

```mermaid
graph TB
    subgraph "Identity Providers"
        LDAP[LDAP/AD]
        SAML[SAML IdP]
        OIDC[OIDC Provider]
        LOCAL[Local Auth]
    end
    
    subgraph "Authentication Layer"
        SSO[SSO Gateway]
        MFA[Multi-Factor Auth]
        SESSION[Session Manager]
    end
    
    subgraph "Authorization Engine"
        RBAC[Role-Based Access]
        ABAC[Attribute-Based Access]
        POLICY[Policy Engine]
    end
    
    subgraph "Security Scanning"
        VULN[Vulnerability Scanner]
        SECRET[Secret Scanner]
        SAST[Static Analysis]
        DAST[Dynamic Analysis]
    end
    
    subgraph "Compliance & Audit"
        AUDIT[Audit Logger]
        COMPLIANCE[Compliance Reporter]
        MONITOR[Security Monitor]
        ALERT[Alert System]
    end
    
    subgraph "Network Security"
        WAF[Web Application Firewall]
        RATE[Rate Limiting]
        IP[IP Filtering]
        TLS[TLS Termination]
    end
    
    LDAP --> SSO
    SAML --> SSO
    OIDC --> SSO
    LOCAL --> SSO
    
    SSO --> MFA
    MFA --> SESSION
    SESSION --> RBAC
    RBAC --> ABAC
    ABAC --> POLICY
    
    POLICY --> VULN
    POLICY --> SECRET
    POLICY --> SAST
    POLICY --> DAST
    
    VULN --> AUDIT
    SECRET --> AUDIT
    SAST --> AUDIT
    DAST --> AUDIT
    
    AUDIT --> COMPLIANCE
    AUDIT --> MONITOR
    MONITOR --> ALERT
    
    WAF --> RATE
    RATE --> IP
    IP --> TLS
    
    style SSO fill:#e8f5e8
    style POLICY fill:#e1f5fe
    style AUDIT fill:#fff3e0
    style ALERT fill:#ffebee
```

## 🏢 LDAP/Active Directory Integration

### LDAP Authentication Service

```rust
// src/security/ldap.rs
use ldap3::{LdapConn, Scope, SearchEntry};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use std::collections::HashMap;
use crate::auth::{User, AuthError};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LdapConfig {
    pub server_url: String,
    pub bind_dn: String,
    pub bind_password: String,
    pub base_dn: String,
    pub user_filter: String,
    pub group_filter: String,
    pub user_attributes: LdapUserAttributes,
    pub group_attributes: LdapGroupAttributes,
    pub tls_enabled: bool,
    pub connection_timeout: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LdapUserAttributes {
    pub username: String,
    pub email: String,
    pub first_name: String,
    pub last_name: String,
    pub display_name: String,
    pub member_of: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LdapGroupAttributes {
    pub name: String,
    pub description: String,
    pub members: String,
}

#[derive(Debug, Clone)]
pub struct LdapUser {
    pub username: String,
    pub email: String,
    pub first_name: String,
    pub last_name: String,
    pub display_name: String,
    pub groups: Vec<String>,
    pub attributes: HashMap<String, Vec<String>>,
}

pub struct LdapService {
    config: LdapConfig,
}

impl LdapService {
    pub fn new(config: LdapConfig) -> Self {
        Self { config }
    }
    
    /// Authenticate user against LDAP
    pub async fn authenticate(
        &self,
        username: &str,
        password: &str,
    ) -> Result<LdapUser, LdapError> {
        let mut ldap = self.connect().await?;
        
        // First, bind with service account to search for user
        ldap.simple_bind(&self.config.bind_dn, &self.config.bind_password)
            .await?;
        
        // Search for user
        let user_dn = self.find_user_dn(&mut ldap, username).await?;
        
        // Attempt to bind as the user to verify password
        match ldap.simple_bind(&user_dn, password).await {
            Ok(_) => {
                // Password is correct, get user details
                self.get_user_details(&mut ldap, username).await
            }
            Err(_) => Err(LdapError::InvalidCredentials),
        }
    }
    
    /// Get user details from LDAP
    pub async fn get_user_details(
        &self,
        ldap: &mut LdapConn,
        username: &str,
    ) -> Result<LdapUser, LdapError> {
        let filter = self.config.user_filter.replace("{username}", username);
        let attrs = vec![
            &self.config.user_attributes.username,
            &self.config.user_attributes.email,
            &self.config.user_attributes.first_name,
            &self.config.user_attributes.last_name,
            &self.config.user_attributes.display_name,
            &self.config.user_attributes.member_of,
        ];
        
        let (rs, _res) = ldap
            .search(&self.config.base_dn, Scope::Subtree, &filter, attrs)
            .await?;
        
        if rs.is_empty() {
            return Err(LdapError::UserNotFound);
        }
        
        let entry = SearchEntry::construct(rs[0].clone());
        
        let username = self.get_attribute(&entry, &self.config.user_attributes.username)?;
        let email = self.get_attribute(&entry, &self.config.user_attributes.email)?;
        let first_name = self.get_attribute(&entry, &self.config.user_attributes.first_name)?;
        let last_name = self.get_attribute(&entry, &self.config.user_attributes.last_name)?;
        let display_name = self.get_attribute(&entry, &self.config.user_attributes.display_name)?;
        
        // Get user groups
        let groups = self.get_user_groups(ldap, &entry.dn).await?;
        
        Ok(LdapUser {
            username,
            email,
            first_name,
            last_name,
            display_name,
            groups,
            attributes: entry.attrs,
        })
    }
    
    /// Sync users from LDAP to local database
    pub async fn sync_users(&self) -> Result<Vec<User>, LdapError> {
        let mut ldap = self.connect().await?;
        
        // Bind with service account
        ldap.simple_bind(&self.config.bind_dn, &self.config.bind_password)
            .await?;
        
        // Search for all users
        let filter = &self.config.user_filter.replace("{username}", "*");
        let attrs = vec![
            &self.config.user_attributes.username,
            &self.config.user_attributes.email,
            &self.config.user_attributes.first_name,
            &self.config.user_attributes.last_name,
            &self.config.user_attributes.display_name,
        ];
        
        let (rs, _res) = ldap
            .search(&self.config.base_dn, Scope::Subtree, filter, attrs)
            .await?;
        
        let mut users = Vec::new();
        
        for entry in rs {
            let search_entry = SearchEntry::construct(entry);
            
            if let Ok(ldap_user) = self.parse_ldap_user(search_entry) {
                // Convert to local user format
                let user = User {
                    id: Uuid::new_v4(),
                    username: ldap_user.username,
                    email: ldap_user.email,
                    first_name: Some(ldap_user.first_name),
                    last_name: Some(ldap_user.last_name),
                    display_name: Some(ldap_user.display_name),
                    is_active: true,
                    is_admin: false,
                    auth_provider: Some("ldap".to_string()),
                    created_at: chrono::Utc::now(),
                    updated_at: chrono::Utc::now(),
                    last_login: None,
                    password_hash: None, // LDAP users don't have local passwords
                };
                
                users.push(user);
            }
        }
        
        Ok(users)
    }
    
    async fn connect(&self) -> Result<LdapConn, LdapError> {
        let ldap = if self.config.tls_enabled {
            LdapConn::new(&self.config.server_url)?
        } else {
            LdapConn::new(&self.config.server_url)?
        };
        
        Ok(ldap)
    }
    
    async fn find_user_dn(
        &self,
        ldap: &mut LdapConn,
        username: &str,
    ) -> Result<String, LdapError> {
        let filter = self.config.user_filter.replace("{username}", username);
        let (rs, _res) = ldap
            .search(&self.config.base_dn, Scope::Subtree, &filter, vec!["dn"])
            .await?;
        
        if rs.is_empty() {
            return Err(LdapError::UserNotFound);
        }
        
        let entry = SearchEntry::construct(rs[0].clone());
        Ok(entry.dn)
    }
    
    async fn get_user_groups(
        &self,
        ldap: &mut LdapConn,
        user_dn: &str,
    ) -> Result<Vec<String>, LdapError> {
        let filter = self.config.group_filter.replace("{user_dn}", user_dn);
        let (rs, _res) = ldap
            .search(
                &self.config.base_dn,
                Scope::Subtree,
                &filter,
                vec![&self.config.group_attributes.name],
            )
            .await?;
        
        let mut groups = Vec::new();
        for entry in rs {
            let search_entry = SearchEntry::construct(entry);
            if let Some(group_names) = search_entry.attrs.get(&self.config.group_attributes.name) {
                groups.extend(group_names.clone());
            }
        }
        
        Ok(groups)
    }
    
    fn get_attribute(&self, entry: &SearchEntry, attr_name: &str) -> Result<String, LdapError> {
        entry
            .attrs
            .get(attr_name)
            .and_then(|values| values.first())
            .map(|value| value.clone())
            .ok_or(LdapError::MissingAttribute(attr_name.to_string()))
    }
    
    fn parse_ldap_user(&self, entry: SearchEntry) -> Result<LdapUser, LdapError> {
        let username = self.get_attribute(&entry, &self.config.user_attributes.username)?;
        let email = self.get_attribute(&entry, &self.config.user_attributes.email)?;
        let first_name = self.get_attribute(&entry, &self.config.user_attributes.first_name)?;
        let last_name = self.get_attribute(&entry, &self.config.user_attributes.last_name)?;
        let display_name = self.get_attribute(&entry, &self.config.user_attributes.display_name)?;
        
        Ok(LdapUser {
            username,
            email,
            first_name,
            last_name,
            display_name,
            groups: Vec::new(), // Groups would be populated separately
            attributes: entry.attrs,
        })
    }
}

#[derive(Debug, thiserror::Error)]
pub enum LdapError {
    #[error("LDAP connection error: {0}")]
    ConnectionError(#[from] ldap3::LdapError),
    #[error("User not found")]
    UserNotFound,
    #[error("Invalid credentials")]
    InvalidCredentials,
    #[error("Missing attribute: {0}")]
    MissingAttribute(String),
    #[error("Configuration error: {0}")]
    ConfigError(String),
}

impl From<LdapError> for AuthError {
    fn from(err: LdapError) -> Self {
        match err {
            LdapError::UserNotFound => AuthError::UserNotFound,
            LdapError::InvalidCredentials => AuthError::InvalidCredentials,
            _ => AuthError::ExternalAuthError(err.to_string()),
        }
    }
}
```

## 🔐 SAML & OIDC Single Sign-On

### SAML Authentication Service

```rust
// src/security/saml.rs
use saml2::{ServiceProvider, IdentityProvider, AuthnRequest, Response};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SamlConfig {
    pub entity_id: String,
    pub acs_url: String,           // Assertion Consumer Service URL
    pub sls_url: String,           // Single Logout Service URL
    pub idp_entity_id: String,
    pub idp_sso_url: String,
    pub idp_sls_url: String,
    pub idp_certificate: String,
    pub sp_certificate: String,
    pub sp_private_key: String,
    pub name_id_format: String,
    pub attribute_mapping: SamlAttributeMapping,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SamlAttributeMapping {
    pub username: String,
    pub email: String,
    pub first_name: String,
    pub last_name: String,
    pub groups: String,
}

#[derive(Debug, Clone)]
pub struct SamlUser {
    pub name_id: String,
    pub username: String,
    pub email: String,
    pub first_name: String,
    pub last_name: String,
    pub groups: Vec<String>,
    pub attributes: HashMap<String, Vec<String>>,
    pub session_index: Option<String>,
}

pub struct SamlService {
    config: SamlConfig,
    service_provider: ServiceProvider,
    identity_provider: IdentityProvider,
}

impl SamlService {
    pub fn new(config: SamlConfig) -> Result<Self, SamlError> {
        let sp = ServiceProvider::new(
            &config.entity_id,
            &config.acs_url,
            &config.sls_url,
            &config.sp_certificate,
            &config.sp_private_key,
        )?;

        let idp = IdentityProvider::new(
            &config.idp_entity_id,
            &config.idp_sso_url,
            &config.idp_sls_url,
            &config.idp_certificate,
        )?;

        Ok(Self {
            config,
            service_provider: sp,
            identity_provider: idp,
        })
    }

    /// Generate SAML authentication request
    pub fn generate_auth_request(&self, relay_state: Option<String>) -> Result<String, SamlError> {
        let authn_request = AuthnRequest::new(
            &self.service_provider,
            &self.identity_provider,
            &self.config.name_id_format,
        )?;

        let request_url = authn_request.to_redirect_url(relay_state.as_deref())?;
        Ok(request_url)
    }

    /// Process SAML response
    pub fn process_response(&self, saml_response: &str) -> Result<SamlUser, SamlError> {
        let response = Response::from_base64(saml_response)?;

        // Validate response
        response.validate(&self.identity_provider, &self.service_provider)?;

        // Extract user information
        let assertion = response.get_assertion()?;
        let name_id = assertion.get_name_id()?;
        let attributes = assertion.get_attributes()?;

        let username = self.get_attribute_value(&attributes, &self.config.attribute_mapping.username)?;
        let email = self.get_attribute_value(&attributes, &self.config.attribute_mapping.email)?;
        let first_name = self.get_attribute_value(&attributes, &self.config.attribute_mapping.first_name)?;
        let last_name = self.get_attribute_value(&attributes, &self.config.attribute_mapping.last_name)?;
        let groups = self.get_attribute_values(&attributes, &self.config.attribute_mapping.groups);

        let session_index = assertion.get_session_index();

        Ok(SamlUser {
            name_id,
            username,
            email,
            first_name,
            last_name,
            groups,
            attributes,
            session_index,
        })
    }

    /// Generate logout request
    pub fn generate_logout_request(&self, name_id: &str, session_index: Option<&str>) -> Result<String, SamlError> {
        let logout_request = saml2::LogoutRequest::new(
            &self.service_provider,
            &self.identity_provider,
            name_id,
            session_index,
        )?;

        let request_url = logout_request.to_redirect_url()?;
        Ok(request_url)
    }

    fn get_attribute_value(
        &self,
        attributes: &HashMap<String, Vec<String>>,
        attribute_name: &str,
    ) -> Result<String, SamlError> {
        attributes
            .get(attribute_name)
            .and_then(|values| values.first())
            .map(|value| value.clone())
            .ok_or_else(|| SamlError::MissingAttribute(attribute_name.to_string()))
    }

    fn get_attribute_values(
        &self,
        attributes: &HashMap<String, Vec<String>>,
        attribute_name: &str,
    ) -> Vec<String> {
        attributes
            .get(attribute_name)
            .map(|values| values.clone())
            .unwrap_or_default()
    }
}

#[derive(Debug, thiserror::Error)]
pub enum SamlError {
    #[error("SAML configuration error: {0}")]
    ConfigError(String),
    #[error("SAML validation error: {0}")]
    ValidationError(String),
    #[error("Missing attribute: {0}")]
    MissingAttribute(String),
    #[error("SAML processing error: {0}")]
    ProcessingError(String),
}
```

### OIDC Authentication Service

```rust
// src/security/oidc.rs
use openidconnect::{
    AuthorizationCode, ClientId, ClientSecret, CsrfToken, Nonce, PkceCodeChallenge,
    RedirectUrl, Scope, TokenResponse, UserInfoClaims, IssuerUrl, JsonWebKeySet,
    core::{CoreClient, CoreProviderMetadata, CoreResponseType, CoreAuthenticationFlow}
};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OidcConfig {
    pub issuer_url: String,
    pub client_id: String,
    pub client_secret: String,
    pub redirect_uri: String,
    pub scopes: Vec<String>,
    pub claims_mapping: OidcClaimsMapping,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OidcClaimsMapping {
    pub username: String,
    pub email: String,
    pub first_name: String,
    pub last_name: String,
    pub groups: String,
}

#[derive(Debug, Clone)]
pub struct OidcUser {
    pub subject: String,
    pub username: String,
    pub email: String,
    pub first_name: String,
    pub last_name: String,
    pub groups: Vec<String>,
    pub claims: HashMap<String, serde_json::Value>,
}

pub struct OidcService {
    config: OidcConfig,
    client: CoreClient,
}

impl OidcService {
    pub async fn new(config: OidcConfig) -> Result<Self, OidcError> {
        let issuer_url = IssuerUrl::new(config.issuer_url.clone())?;
        let provider_metadata = CoreProviderMetadata::discover_async(
            issuer_url,
            async_http_client,
        ).await?;

        let client = CoreClient::from_provider_metadata(
            provider_metadata,
            ClientId::new(config.client_id.clone()),
            Some(ClientSecret::new(config.client_secret.clone())),
        )
        .set_redirect_uri(RedirectUrl::new(config.redirect_uri.clone())?);

        Ok(Self { config, client })
    }

    /// Generate authorization URL
    pub fn generate_auth_url(&self) -> (String, CsrfToken, Nonce) {
        let mut auth_request = self.client.authorize_url(
            CoreAuthenticationFlow::AuthorizationCode,
            CsrfToken::new_random,
            Nonce::new_random,
        );

        // Add scopes
        for scope in &self.config.scopes {
            auth_request = auth_request.add_scope(Scope::new(scope.clone()));
        }

        let (auth_url, csrf_token, nonce) = auth_request.url();
        (auth_url.to_string(), csrf_token, nonce)
    }

    /// Exchange authorization code for tokens
    pub async fn exchange_code(
        &self,
        code: AuthorizationCode,
        nonce: Nonce,
    ) -> Result<OidcUser, OidcError> {
        // Exchange code for token
        let token_response = self
            .client
            .exchange_code(code)
            .request_async(async_http_client)
            .await?;

        // Get ID token claims
        let id_token = token_response
            .id_token()
            .ok_or(OidcError::MissingIdToken)?;

        let claims = id_token.claims(&self.client.id_token_verifier(), &nonce)?;

        // Get user info if access token is available
        let user_info = if let Some(access_token) = token_response.access_token() {
            Some(
                self.client
                    .user_info(access_token.clone(), None)?
                    .request_async(async_http_client)
                    .await?,
            )
        } else {
            None
        };

        // Extract user information
        let subject = claims.subject().to_string();
        let username = self.extract_claim_string(&claims, &user_info, &self.config.claims_mapping.username)?;
        let email = self.extract_claim_string(&claims, &user_info, &self.config.claims_mapping.email)?;
        let first_name = self.extract_claim_string(&claims, &user_info, &self.config.claims_mapping.first_name)?;
        let last_name = self.extract_claim_string(&claims, &user_info, &self.config.claims_mapping.last_name)?;
        let groups = self.extract_claim_array(&claims, &user_info, &self.config.claims_mapping.groups);

        // Combine all claims
        let mut all_claims = HashMap::new();
        if let Ok(claims_json) = serde_json::to_value(&claims) {
            if let serde_json::Value::Object(map) = claims_json {
                all_claims.extend(map);
            }
        }

        if let Some(user_info) = &user_info {
            if let Ok(user_info_json) = serde_json::to_value(user_info) {
                if let serde_json::Value::Object(map) = user_info_json {
                    all_claims.extend(map);
                }
            }
        }

        Ok(OidcUser {
            subject,
            username,
            email,
            first_name,
            last_name,
            groups,
            claims: all_claims,
        })
    }

    fn extract_claim_string(
        &self,
        id_claims: &openidconnect::IdTokenClaims<openidconnect::EmptyAdditionalClaims>,
        user_info: &Option<UserInfoClaims<openidconnect::EmptyAdditionalClaims, openidconnect::core::CoreGenderClaim>>,
        claim_name: &str,
    ) -> Result<String, OidcError> {
        // Try to get from ID token first
        if let Some(value) = id_claims.additional_claims().get(claim_name) {
            if let Some(string_value) = value.as_str() {
                return Ok(string_value.to_string());
            }
        }

        // Try to get from user info
        if let Some(user_info) = user_info {
            if let Some(value) = user_info.additional_claims().get(claim_name) {
                if let Some(string_value) = value.as_str() {
                    return Ok(string_value.to_string());
                }
            }
        }

        // Handle standard claims
        match claim_name {
            "email" => Ok(id_claims.email().map(|e| e.to_string()).unwrap_or_default()),
            "given_name" => Ok(id_claims.given_name().map(|n| n.to_string()).unwrap_or_default()),
            "family_name" => Ok(id_claims.family_name().map(|n| n.to_string()).unwrap_or_default()),
            "preferred_username" => Ok(id_claims.preferred_username().map(|u| u.to_string()).unwrap_or_default()),
            _ => Err(OidcError::MissingClaim(claim_name.to_string())),
        }
    }

    fn extract_claim_array(
        &self,
        id_claims: &openidconnect::IdTokenClaims<openidconnect::EmptyAdditionalClaims>,
        user_info: &Option<UserInfoClaims<openidconnect::EmptyAdditionalClaims, openidconnect::core::CoreGenderClaim>>,
        claim_name: &str,
    ) -> Vec<String> {
        // Try to get from ID token first
        if let Some(value) = id_claims.additional_claims().get(claim_name) {
            if let Some(array) = value.as_array() {
                return array
                    .iter()
                    .filter_map(|v| v.as_str().map(|s| s.to_string()))
                    .collect();
            }
        }

        // Try to get from user info
        if let Some(user_info) = user_info {
            if let Some(value) = user_info.additional_claims().get(claim_name) {
                if let Some(array) = value.as_array() {
                    return array
                        .iter()
                        .filter_map(|v| v.as_str().map(|s| s.to_string()))
                        .collect();
                }
            }
        }

        Vec::new()
    }
}

async fn async_http_client(
    request: openidconnect::HttpRequest,
) -> Result<openidconnect::HttpResponse, openidconnect::reqwest::Error<reqwest::Error>> {
    let client = reqwest::Client::new();
    let mut request_builder = client
        .request(request.method, request.url.as_str())
        .body(request.body);

    for (name, value) in &request.headers {
        request_builder = request_builder.header(name.as_str(), value.as_bytes());
    }

    let response = request_builder.send().await?;
    let status_code = response.status();
    let headers = response.headers().clone();
    let body = response.bytes().await?;

    Ok(openidconnect::HttpResponse {
        status_code,
        headers,
        body: body.to_vec(),
    })
}

#[derive(Debug, thiserror::Error)]
pub enum OidcError {
    #[error("OIDC configuration error: {0}")]
    ConfigError(String),
    #[error("Missing ID token")]
    MissingIdToken,
    #[error("Missing claim: {0}")]
    MissingClaim(String),
    #[error("Token exchange error: {0}")]
    TokenExchangeError(String),
    #[error("OpenID Connect error: {0}")]
    OpenIdConnectError(#[from] openidconnect::ConfigurationError),
    #[error("URL parse error: {0}")]
    UrlParseError(#[from] openidconnect::url::ParseError),
}
```

## 🔍 Security Scanning & Vulnerability Detection

### Vulnerability Scanner Service

```rust
// src/security/scanner.rs
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use tokio::process::Command;
use regex::Regex;

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct SecurityScan {
    pub id: Uuid,
    pub repository_id: Uuid,
    pub scan_type: ScanType,
    pub status: ScanStatus,
    pub started_at: DateTime<Utc>,
    pub finished_at: Option<DateTime<Utc>>,
    pub findings_count: i32,
    pub critical_count: i32,
    pub high_count: i32,
    pub medium_count: i32,
    pub low_count: i32,
    pub scan_config: serde_json::Value,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "scan_type", rename_all = "lowercase")]
pub enum ScanType {
    Vulnerability,  // Dependency vulnerabilities
    Secret,        // Secret detection
    Sast,          // Static Application Security Testing
    License,       // License compliance
    Container,     // Container image scanning
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "scan_status", rename_all = "lowercase")]
pub enum ScanStatus {
    Queued,
    Running,
    Completed,
    Failed,
    Cancelled,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct SecurityFinding {
    pub id: Uuid,
    pub scan_id: Uuid,
    pub finding_type: FindingType,
    pub severity: Severity,
    pub title: String,
    pub description: String,
    pub file_path: Option<String>,
    pub line_number: Option<i32>,
    pub column_number: Option<i32>,
    pub cve_id: Option<String>,
    pub cwe_id: Option<String>,
    pub package_name: Option<String>,
    pub package_version: Option<String>,
    pub fixed_version: Option<String>,
    pub confidence: f32,
    pub status: FindingStatus,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "finding_type", rename_all = "lowercase")]
pub enum FindingType {
    Vulnerability,
    Secret,
    CodeSmell,
    License,
    Configuration,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "severity", rename_all = "lowercase")]
pub enum Severity {
    Critical,
    High,
    Medium,
    Low,
    Info,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "finding_status", rename_all = "lowercase")]
pub enum FindingStatus {
    Open,
    Acknowledged,
    Fixed,
    FalsePositive,
    WontFix,
}

pub struct SecurityScanner {
    db: sqlx::PgPool,
    config: ScannerConfig,
}

#[derive(Debug, Clone)]
pub struct ScannerConfig {
    pub trivy_path: String,
    pub semgrep_path: String,
    pub gitleaks_path: String,
    pub workspace_path: String,
}

impl SecurityScanner {
    pub fn new(db: sqlx::PgPool, config: ScannerConfig) -> Self {
        Self { db, config }
    }

    /// Start comprehensive security scan
    pub async fn start_scan(
        &self,
        repository_id: Uuid,
        scan_types: Vec<ScanType>,
    ) -> Result<Vec<Uuid>, ScannerError> {
        let mut scan_ids = Vec::new();

        for scan_type in scan_types {
            let scan_id = self.create_scan(repository_id, scan_type.clone()).await?;
            scan_ids.push(scan_id);

            // Start scan in background
            let scanner = self.clone();
            tokio::spawn(async move {
                if let Err(e) = scanner.execute_scan(scan_id, scan_type).await {
                    tracing::error!("Scan failed: {}", e);
                }
            });
        }

        Ok(scan_ids)
    }

    async fn create_scan(
        &self,
        repository_id: Uuid,
        scan_type: ScanType,
    ) -> Result<Uuid, ScannerError> {
        let scan_id = sqlx::query_scalar(
            r#"
            INSERT INTO security_scans (repository_id, scan_type, status)
            VALUES ($1, $2, 'queued')
            RETURNING id
            "#
        )
        .bind(repository_id)
        .bind(scan_type)
        .fetch_one(&self.db)
        .await?;

        Ok(scan_id)
    }

    async fn execute_scan(&self, scan_id: Uuid, scan_type: ScanType) -> Result<(), ScannerError> {
        // Update status to running
        sqlx::query("UPDATE security_scans SET status = 'running', started_at = NOW() WHERE id = $1")
            .bind(scan_id)
            .execute(&self.db)
            .await?;

        let result = match scan_type {
            ScanType::Vulnerability => self.run_vulnerability_scan(scan_id).await,
            ScanType::Secret => self.run_secret_scan(scan_id).await,
            ScanType::Sast => self.run_sast_scan(scan_id).await,
            ScanType::License => self.run_license_scan(scan_id).await,
            ScanType::Container => self.run_container_scan(scan_id).await,
        };

        // Update scan status
        let status = match result {
            Ok(_) => "completed",
            Err(_) => "failed",
        };

        sqlx::query(
            r#"
            UPDATE security_scans
            SET status = $1, finished_at = NOW(),
                findings_count = (SELECT COUNT(*) FROM security_findings WHERE scan_id = $2),
                critical_count = (SELECT COUNT(*) FROM security_findings WHERE scan_id = $2 AND severity = 'critical'),
                high_count = (SELECT COUNT(*) FROM security_findings WHERE scan_id = $2 AND severity = 'high'),
                medium_count = (SELECT COUNT(*) FROM security_findings WHERE scan_id = $2 AND severity = 'medium'),
                low_count = (SELECT COUNT(*) FROM security_findings WHERE scan_id = $2 AND severity = 'low')
            WHERE id = $2
            "#
        )
        .bind(status)
        .bind(scan_id)
        .execute(&self.db)
        .await?;

        result
    }

    async fn run_vulnerability_scan(&self, scan_id: Uuid) -> Result<(), ScannerError> {
        let repo_path = self.get_repository_path(scan_id).await?;

        // Run Trivy for vulnerability scanning
        let output = Command::new(&self.config.trivy_path)
            .args(&["fs", "--format", "json", &repo_path])
            .output()
            .await?;

        if !output.status.success() {
            return Err(ScannerError::ScanFailed(
                String::from_utf8_lossy(&output.stderr).to_string()
            ));
        }

        let trivy_results: TrivyResults = serde_json::from_slice(&output.stdout)?;

        // Process results and store findings
        for result in trivy_results.Results {
            for vulnerability in result.Vulnerabilities {
                self.store_vulnerability_finding(scan_id, &vulnerability).await?;
            }
        }

        Ok(())
    }

    async fn run_secret_scan(&self, scan_id: Uuid) -> Result<(), ScannerError> {
        let repo_path = self.get_repository_path(scan_id).await?;

        // Run Gitleaks for secret detection
        let output = Command::new(&self.config.gitleaks_path)
            .args(&["detect", "--source", &repo_path, "--format", "json"])
            .output()
            .await?;

        // Gitleaks returns non-zero exit code when secrets are found
        let gitleaks_results: Vec<GitleaksResult> = if output.stdout.is_empty() {
            Vec::new()
        } else {
            serde_json::from_slice(&output.stdout)?
        };

        // Process results and store findings
        for secret in gitleaks_results {
            self.store_secret_finding(scan_id, &secret).await?;
        }

        Ok(())
    }

    async fn run_sast_scan(&self, scan_id: Uuid) -> Result<(), ScannerError> {
        let repo_path = self.get_repository_path(scan_id).await?;

        // Run Semgrep for static analysis
        let output = Command::new(&self.config.semgrep_path)
            .args(&["--config=auto", "--json", &repo_path])
            .output()
            .await?;

        if !output.status.success() {
            return Err(ScannerError::ScanFailed(
                String::from_utf8_lossy(&output.stderr).to_string()
            ));
        }

        let semgrep_results: SemgrepResults = serde_json::from_slice(&output.stdout)?;

        // Process results and store findings
        for finding in semgrep_results.results {
            self.store_sast_finding(scan_id, &finding).await?;
        }

        Ok(())
    }

    async fn run_license_scan(&self, scan_id: Uuid) -> Result<(), ScannerError> {
        // Implementation would scan for license compliance issues
        // This could integrate with tools like FOSSA, Black Duck, or custom license detection
        Ok(())
    }

    async fn run_container_scan(&self, scan_id: Uuid) -> Result<(), ScannerError> {
        // Implementation would scan container images for vulnerabilities
        // This could use Trivy, Clair, or other container scanning tools
        Ok(())
    }

    async fn store_vulnerability_finding(
        &self,
        scan_id: Uuid,
        vulnerability: &TrivyVulnerability,
    ) -> Result<(), ScannerError> {
        sqlx::query(
            r#"
            INSERT INTO security_findings (
                scan_id, finding_type, severity, title, description,
                cve_id, package_name, package_version, fixed_version, confidence
            )
            VALUES ($1, 'vulnerability', $2, $3, $4, $5, $6, $7, $8, $9)
            "#
        )
        .bind(scan_id)
        .bind(self.map_trivy_severity(&vulnerability.Severity))
        .bind(&vulnerability.Title)
        .bind(&vulnerability.Description)
        .bind(&vulnerability.VulnerabilityID)
        .bind(&vulnerability.PkgName)
        .bind(&vulnerability.InstalledVersion)
        .bind(&vulnerability.FixedVersion)
        .bind(0.9) // High confidence for CVE-based findings
        .execute(&self.db)
        .await?;

        Ok(())
    }

    async fn store_secret_finding(
        &self,
        scan_id: Uuid,
        secret: &GitleaksResult,
    ) -> Result<(), ScannerError> {
        sqlx::query(
            r#"
            INSERT INTO security_findings (
                scan_id, finding_type, severity, title, description,
                file_path, line_number, confidence
            )
            VALUES ($1, 'secret', 'high', $2, $3, $4, $5, $6)
            "#
        )
        .bind(scan_id)
        .bind(&format!("Secret detected: {}", secret.RuleID))
        .bind(&secret.Description)
        .bind(&secret.File)
        .bind(secret.StartLine as i32)
        .bind(0.8) // Good confidence for secret detection
        .execute(&self.db)
        .await?;

        Ok(())
    }

    async fn store_sast_finding(
        &self,
        scan_id: Uuid,
        finding: &SemgrepFinding,
    ) -> Result<(), ScannerError> {
        sqlx::query(
            r#"
            INSERT INTO security_findings (
                scan_id, finding_type, severity, title, description,
                file_path, line_number, column_number, confidence
            )
            VALUES ($1, 'code_smell', $2, $3, $4, $5, $6, $7, $8)
            "#
        )
        .bind(scan_id)
        .bind(self.map_semgrep_severity(&finding.extra.severity))
        .bind(&finding.check_id)
        .bind(&finding.extra.message)
        .bind(&finding.path)
        .bind(finding.start.line as i32)
        .bind(finding.start.col as i32)
        .bind(0.7) // Moderate confidence for SAST findings
        .execute(&self.db)
        .await?;

        Ok(())
    }

    async fn get_repository_path(&self, scan_id: Uuid) -> Result<String, ScannerError> {
        let repository_id: Uuid = sqlx::query_scalar(
            "SELECT repository_id FROM security_scans WHERE id = $1"
        )
        .bind(scan_id)
        .fetch_one(&self.db)
        .await?;

        // In a real implementation, this would get the actual repository path
        Ok(format!("{}/{}", self.config.workspace_path, repository_id))
    }

    fn map_trivy_severity(&self, severity: &str) -> Severity {
        match severity.to_uppercase().as_str() {
            "CRITICAL" => Severity::Critical,
            "HIGH" => Severity::High,
            "MEDIUM" => Severity::Medium,
            "LOW" => Severity::Low,
            _ => Severity::Info,
        }
    }

    fn map_semgrep_severity(&self, severity: &str) -> Severity {
        match severity.to_uppercase().as_str() {
            "ERROR" => Severity::High,
            "WARNING" => Severity::Medium,
            "INFO" => Severity::Low,
            _ => Severity::Info,
        }
    }
}

// External tool result structures
#[derive(Debug, Deserialize)]
struct TrivyResults {
    Results: Vec<TrivyResult>,
}

#[derive(Debug, Deserialize)]
struct TrivyResult {
    Vulnerabilities: Vec<TrivyVulnerability>,
}

#[derive(Debug, Deserialize)]
struct TrivyVulnerability {
    VulnerabilityID: String,
    PkgName: String,
    InstalledVersion: String,
    FixedVersion: Option<String>,
    Severity: String,
    Title: String,
    Description: String,
}

#[derive(Debug, Deserialize)]
struct GitleaksResult {
    RuleID: String,
    Description: String,
    File: String,
    StartLine: u32,
    EndLine: u32,
}

#[derive(Debug, Deserialize)]
struct SemgrepResults {
    results: Vec<SemgrepFinding>,
}

#[derive(Debug, Deserialize)]
struct SemgrepFinding {
    check_id: String,
    path: String,
    start: SemgrepPosition,
    end: SemgrepPosition,
    extra: SemgrepExtra,
}

#[derive(Debug, Deserialize)]
struct SemgrepPosition {
    line: u32,
    col: u32,
}

#[derive(Debug, Deserialize)]
struct SemgrepExtra {
    message: String,
    severity: String,
}

impl Clone for SecurityScanner {
    fn clone(&self) -> Self {
        Self {
            db: self.db.clone(),
            config: self.config.clone(),
        }
    }
}

#[derive(Debug, thiserror::Error)]
pub enum ScannerError {
    #[error("Scan failed: {0}")]
    ScanFailed(String),
    #[error("Database error: {0}")]
    DatabaseError(#[from] sqlx::Error),
    #[error("IO error: {0}")]
    IoError(#[from] tokio::io::Error),
    #[error("JSON error: {0}")]
    JsonError(#[from] serde_json::Error),
}
```

## 📊 Compliance Reporting & Audit Logging

### Comprehensive Audit System

```rust
// src/security/audit.rs
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use sqlx::PgPool;
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct AuditEvent {
    pub id: Uuid,
    pub event_type: AuditEventType,
    pub actor_id: Option<Uuid>,
    pub actor_type: ActorType,
    pub resource_type: ResourceType,
    pub resource_id: Option<Uuid>,
    pub action: String,
    pub outcome: AuditOutcome,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub session_id: Option<String>,
    pub details: serde_json::Value,
    pub risk_score: i32,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "audit_event_type", rename_all = "lowercase")]
pub enum AuditEventType {
    Authentication,
    Authorization,
    DataAccess,
    DataModification,
    SystemConfiguration,
    SecurityEvent,
    ComplianceEvent,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "actor_type", rename_all = "lowercase")]
pub enum ActorType {
    User,
    System,
    Service,
    Anonymous,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "resource_type", rename_all = "lowercase")]
pub enum ResourceType {
    Repository,
    User,
    Organization,
    PullRequest,
    Issue,
    Branch,
    Tag,
    Webhook,
    ApiKey,
    SecurityScan,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "audit_outcome", rename_all = "lowercase")]
pub enum AuditOutcome {
    Success,
    Failure,
    Denied,
    Error,
}

pub struct AuditLogger {
    db: PgPool,
}

impl AuditLogger {
    pub fn new(db: PgPool) -> Self {
        Self { db }
    }

    /// Log authentication event
    pub async fn log_auth_event(
        &self,
        actor_id: Option<Uuid>,
        action: &str,
        outcome: AuditOutcome,
        ip_address: Option<String>,
        user_agent: Option<String>,
        details: serde_json::Value,
    ) -> Result<(), AuditError> {
        let risk_score = self.calculate_auth_risk_score(&outcome, &details);

        self.log_event(AuditEvent {
            id: Uuid::new_v4(),
            event_type: AuditEventType::Authentication,
            actor_id,
            actor_type: if actor_id.is_some() { ActorType::User } else { ActorType::Anonymous },
            resource_type: ResourceType::User,
            resource_id: actor_id,
            action: action.to_string(),
            outcome,
            ip_address,
            user_agent,
            session_id: None,
            details,
            risk_score,
            created_at: Utc::now(),
        }).await
    }

    /// Log data access event
    pub async fn log_data_access(
        &self,
        actor_id: Uuid,
        resource_type: ResourceType,
        resource_id: Uuid,
        action: &str,
        outcome: AuditOutcome,
        session_id: Option<String>,
        details: serde_json::Value,
    ) -> Result<(), AuditError> {
        let risk_score = self.calculate_access_risk_score(&resource_type, &action, &outcome);

        self.log_event(AuditEvent {
            id: Uuid::new_v4(),
            event_type: AuditEventType::DataAccess,
            actor_id: Some(actor_id),
            actor_type: ActorType::User,
            resource_type,
            resource_id: Some(resource_id),
            action: action.to_string(),
            outcome,
            ip_address: None,
            user_agent: None,
            session_id,
            details,
            risk_score,
            created_at: Utc::now(),
        }).await
    }

    /// Log security event
    pub async fn log_security_event(
        &self,
        event_type: &str,
        severity: &str,
        description: &str,
        actor_id: Option<Uuid>,
        resource_id: Option<Uuid>,
        details: serde_json::Value,
    ) -> Result<(), AuditError> {
        let risk_score = self.calculate_security_risk_score(severity);

        self.log_event(AuditEvent {
            id: Uuid::new_v4(),
            event_type: AuditEventType::SecurityEvent,
            actor_id,
            actor_type: if actor_id.is_some() { ActorType::User } else { ActorType::System },
            resource_type: ResourceType::Repository, // Default, could be more specific
            resource_id,
            action: event_type.to_string(),
            outcome: AuditOutcome::Success,
            ip_address: None,
            user_agent: None,
            session_id: None,
            details,
            risk_score,
            created_at: Utc::now(),
        }).await
    }

    async fn log_event(&self, event: AuditEvent) -> Result<(), AuditError> {
        sqlx::query(
            r#"
            INSERT INTO audit_events (
                id, event_type, actor_id, actor_type, resource_type, resource_id,
                action, outcome, ip_address, user_agent, session_id, details, risk_score
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
            "#
        )
        .bind(event.id)
        .bind(event.event_type)
        .bind(event.actor_id)
        .bind(event.actor_type)
        .bind(event.resource_type)
        .bind(event.resource_id)
        .bind(event.action)
        .bind(event.outcome)
        .bind(event.ip_address)
        .bind(event.user_agent)
        .bind(event.session_id)
        .bind(event.details)
        .bind(event.risk_score)
        .execute(&self.db)
        .await?;

        // Check for high-risk events and trigger alerts
        if event.risk_score >= 80 {
            self.trigger_security_alert(&event).await?;
        }

        Ok(())
    }

    fn calculate_auth_risk_score(&self, outcome: &AuditOutcome, details: &serde_json::Value) -> i32 {
        let mut score = match outcome {
            AuditOutcome::Success => 10,
            AuditOutcome::Failure => 50,
            AuditOutcome::Denied => 70,
            AuditOutcome::Error => 30,
        };

        // Increase score for suspicious patterns
        if let Some(failed_attempts) = details.get("failed_attempts") {
            if let Some(count) = failed_attempts.as_u64() {
                score += (count as i32) * 10;
            }
        }

        score.min(100)
    }

    fn calculate_access_risk_score(&self, resource_type: &ResourceType, action: &str, outcome: &AuditOutcome) -> i32 {
        let base_score = match resource_type {
            ResourceType::User => 30,
            ResourceType::Organization => 40,
            ResourceType::ApiKey => 60,
            ResourceType::SecurityScan => 50,
            _ => 20,
        };

        let action_modifier = match action {
            "delete" => 30,
            "modify" => 20,
            "create" => 15,
            "read" => 5,
            _ => 10,
        };

        let outcome_modifier = match outcome {
            AuditOutcome::Success => 0,
            AuditOutcome::Failure => 20,
            AuditOutcome::Denied => 40,
            AuditOutcome::Error => 10,
        };

        (base_score + action_modifier + outcome_modifier).min(100)
    }

    fn calculate_security_risk_score(&self, severity: &str) -> i32 {
        match severity.to_lowercase().as_str() {
            "critical" => 100,
            "high" => 80,
            "medium" => 60,
            "low" => 40,
            _ => 20,
        }
    }

    async fn trigger_security_alert(&self, event: &AuditEvent) -> Result<(), AuditError> {
        // Implementation would send alerts via email, Slack, etc.
        tracing::warn!(
            "High-risk security event detected: {} by {:?} with risk score {}",
            event.action,
            event.actor_id,
            event.risk_score
        );
        Ok(())
    }

    /// Generate compliance report
    pub async fn generate_compliance_report(
        &self,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
        compliance_type: ComplianceType,
    ) -> Result<ComplianceReport, AuditError> {
        match compliance_type {
            ComplianceType::SOC2 => self.generate_soc2_report(start_date, end_date).await,
            ComplianceType::GDPR => self.generate_gdpr_report(start_date, end_date).await,
            ComplianceType::HIPAA => self.generate_hipaa_report(start_date, end_date).await,
        }
    }

    async fn generate_soc2_report(
        &self,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
    ) -> Result<ComplianceReport, AuditError> {
        // SOC 2 focuses on Security, Availability, Processing Integrity, Confidentiality, Privacy

        let auth_events = self.get_auth_events(start_date, end_date).await?;
        let access_events = self.get_access_events(start_date, end_date).await?;
        let security_events = self.get_security_events(start_date, end_date).await?;

        let mut findings = Vec::new();

        // Check for failed authentication patterns
        let failed_auth_count = auth_events.iter()
            .filter(|e| matches!(e.outcome, AuditOutcome::Failure))
            .count();

        if failed_auth_count > 100 {
            findings.push(ComplianceFinding {
                control_id: "CC6.1".to_string(),
                description: "High number of failed authentication attempts detected".to_string(),
                severity: "Medium".to_string(),
                evidence: format!("{} failed authentication attempts", failed_auth_count),
            });
        }

        // Check for unauthorized access attempts
        let denied_access_count = access_events.iter()
            .filter(|e| matches!(e.outcome, AuditOutcome::Denied))
            .count();

        if denied_access_count > 50 {
            findings.push(ComplianceFinding {
                control_id: "CC6.2".to_string(),
                description: "Unauthorized access attempts detected".to_string(),
                severity: "High".to_string(),
                evidence: format!("{} denied access attempts", denied_access_count),
            });
        }

        Ok(ComplianceReport {
            report_type: ComplianceType::SOC2,
            period_start: start_date,
            period_end: end_date,
            total_events: auth_events.len() + access_events.len() + security_events.len(),
            findings,
            generated_at: Utc::now(),
        })
    }

    async fn generate_gdpr_report(
        &self,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
    ) -> Result<ComplianceReport, AuditError> {
        // GDPR focuses on data protection and privacy

        let data_access_events = sqlx::query_as::<_, AuditEvent>(
            r#"
            SELECT * FROM audit_events
            WHERE event_type = 'data_access'
            AND resource_type = 'user'
            AND created_at BETWEEN $1 AND $2
            "#
        )
        .bind(start_date)
        .bind(end_date)
        .fetch_all(&self.db)
        .await?;

        let mut findings = Vec::new();

        // Check for data subject access patterns
        let personal_data_access = data_access_events.len();

        findings.push(ComplianceFinding {
            control_id: "GDPR.32".to_string(),
            description: "Personal data access monitoring".to_string(),
            severity: "Info".to_string(),
            evidence: format!("{} personal data access events recorded", personal_data_access),
        });

        Ok(ComplianceReport {
            report_type: ComplianceType::GDPR,
            period_start: start_date,
            period_end: end_date,
            total_events: data_access_events.len(),
            findings,
            generated_at: Utc::now(),
        })
    }

    async fn generate_hipaa_report(
        &self,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
    ) -> Result<ComplianceReport, AuditError> {
        // HIPAA focuses on healthcare data protection

        let all_events = sqlx::query_as::<_, AuditEvent>(
            "SELECT * FROM audit_events WHERE created_at BETWEEN $1 AND $2"
        )
        .bind(start_date)
        .bind(end_date)
        .fetch_all(&self.db)
        .await?;

        let findings = vec![
            ComplianceFinding {
                control_id: "164.312(b)".to_string(),
                description: "Audit controls implemented".to_string(),
                severity: "Info".to_string(),
                evidence: format!("{} audit events recorded", all_events.len()),
            }
        ];

        Ok(ComplianceReport {
            report_type: ComplianceType::HIPAA,
            period_start: start_date,
            period_end: end_date,
            total_events: all_events.len(),
            findings,
            generated_at: Utc::now(),
        })
    }

    async fn get_auth_events(&self, start_date: DateTime<Utc>, end_date: DateTime<Utc>) -> Result<Vec<AuditEvent>, AuditError> {
        let events = sqlx::query_as::<_, AuditEvent>(
            "SELECT * FROM audit_events WHERE event_type = 'authentication' AND created_at BETWEEN $1 AND $2"
        )
        .bind(start_date)
        .bind(end_date)
        .fetch_all(&self.db)
        .await?;

        Ok(events)
    }

    async fn get_access_events(&self, start_date: DateTime<Utc>, end_date: DateTime<Utc>) -> Result<Vec<AuditEvent>, AuditError> {
        let events = sqlx::query_as::<_, AuditEvent>(
            "SELECT * FROM audit_events WHERE event_type = 'data_access' AND created_at BETWEEN $1 AND $2"
        )
        .bind(start_date)
        .bind(end_date)
        .fetch_all(&self.db)
        .await?;

        Ok(events)
    }

    async fn get_security_events(&self, start_date: DateTime<Utc>, end_date: DateTime<Utc>) -> Result<Vec<AuditEvent>, AuditError> {
        let events = sqlx::query_as::<_, AuditEvent>(
            "SELECT * FROM audit_events WHERE event_type = 'security_event' AND created_at BETWEEN $1 AND $2"
        )
        .bind(start_date)
        .bind(end_date)
        .fetch_all(&self.db)
        .await?;

        Ok(events)
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ComplianceType {
    SOC2,
    GDPR,
    HIPAA,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceReport {
    pub report_type: ComplianceType,
    pub period_start: DateTime<Utc>,
    pub period_end: DateTime<Utc>,
    pub total_events: usize,
    pub findings: Vec<ComplianceFinding>,
    pub generated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceFinding {
    pub control_id: String,
    pub description: String,
    pub severity: String,
    pub evidence: String,
}

#[derive(Debug, thiserror::Error)]
pub enum AuditError {
    #[error("Database error: {0}")]
    DatabaseError(#[from] sqlx::Error),
    #[error("Serialization error: {0}")]
    SerializationError(#[from] serde_json::Error),
}
```

## 🎯 Key Takeaways

### Advanced Security Benefits

1. **Enterprise Integration**: Seamless LDAP/AD and SSO integration
2. **Proactive Security**: Automated vulnerability and secret scanning
3. **Compliance Ready**: SOC2, GDPR, HIPAA reporting capabilities
4. **Comprehensive Auditing**: Complete activity tracking and monitoring
5. **Risk Management**: Intelligent risk scoring and alerting

### Security Features Implemented

- **Multi-Provider Authentication**: LDAP, SAML, OIDC support
- **Automated Security Scanning**: Vulnerability, secret, and SAST scanning
- **Compliance Reporting**: Automated compliance report generation
- **Advanced Audit Logging**: Risk-based event logging and monitoring
- **Security Policies**: Configurable security rules and enforcement
- **Threat Detection**: Pattern-based anomaly detection

### Performance Considerations

- **Async Scanning**: Non-blocking security scans
- **Efficient Logging**: Optimized audit event storage
- **Caching**: Cache authentication results and permissions
- **Batch Processing**: Process compliance reports efficiently

### Security Best Practices

- **Zero Trust**: Never trust, always verify approach
- **Defense in Depth**: Multiple layers of security controls
- **Least Privilege**: Minimal required access permissions
- **Continuous Monitoring**: Real-time security event monitoring
- **Incident Response**: Automated alerting and response procedures

Ready to continue with [Module 14: Package Registry & Artifact Management](./module-14-package-registry.md)?
