# Module 9: Advanced Git Features

## 🎯 Learning Objectives

By the end of this module, you will:
- Implement Git LFS (Large File Storage) for handling binary files efficiently
- Build shallow clone support for faster repository access
- Create server-side and client-side Git hooks system
- Understand and implement Git submodules management
- Master advanced merge strategies and conflict resolution
- Build Git worktrees support for multiple working directories

## 🗂️ Why Advanced Git Features Matter

Modern Git platforms need to handle diverse use cases:
- **Game Development**: Large binary assets (textures, models, audio)
- **Machine Learning**: Large datasets and model files
- **Enterprise**: Complex repository hierarchies and dependencies
- **CI/CD**: Efficient cloning for build systems
- **Automation**: Custom workflows triggered by Git events

### Advanced Git Architecture

```mermaid
graph TB
    subgraph "Git Core Extensions"
        LFS[Git LFS<br/>Large File Storage]
        SHALLOW[Shallow Clones<br/>Partial History]
        HOOKS[Git Hooks<br/>Event Triggers]
        SUBMOD[Submodules<br/>Repository Dependencies]
        WORKTREE[Worktrees<br/>Multiple Checkouts]
    end
    
    subgraph "Storage Layer"
        OBJECTS[Git Objects]
        LFS_STORE[LFS Object Store]
        REFS[References]
        HOOKS_DIR[Hooks Directory]
    end
    
    subgraph "Network Protocol"
        SMART_HTTP[Smart HTTP]
        LFS_API[LFS Transfer API]
        BATCH_API[Batch API]
    end
    
    LFS --> LFS_STORE
    LFS --> LFS_API
    SHALLOW --> OBJECTS
    HOOKS --> HOOKS_DIR
    SUBMOD --> REFS
    WORKTREE --> OBJECTS
    
    SMART_HTTP --> BATCH_API
    LFS_API --> BATCH_API
    
    style LFS fill:#e8f5e8
    style HOOKS fill:#fff3e0
    style SUBMOD fill:#e1f5fe
```

## 📦 Git LFS Implementation

### Understanding Git LFS

Git LFS replaces large files with text pointers inside Git, while storing the file contents on a remote server. This keeps your repository size manageable while still versioning large files.

**LFS Pointer File Format**:
```
version https://git-lfs.github.com/spec/v1
oid sha256:4d7a214614ab2935c943f9e0ff69d22eadbb8f32b1258daaa5e2ca24d17e2393
size 12345
```

### LFS Server Implementation

```rust
// src/lfs/mod.rs
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::fs;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use sha2::{Digest, Sha256};
use axum::{
    extract::{Path, State, Json},
    http::{StatusCode, HeaderMap},
    response::Json as ResponseJson,
};

#[derive(Debug, Serialize, Deserialize)]
pub struct LfsObject {
    pub oid: String,
    pub size: u64,
    pub authenticated: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LfsBatchRequest {
    pub operation: String, // "download" or "upload"
    pub transfers: Vec<String>, // ["basic"]
    pub objects: Vec<LfsObject>,
    pub hash_algo: Option<String>, // "sha256"
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LfsBatchResponse {
    pub transfer: String,
    pub objects: Vec<LfsObjectResponse>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LfsObjectResponse {
    pub oid: String,
    pub size: u64,
    pub authenticated: Option<bool>,
    pub actions: Option<HashMap<String, LfsAction>>,
    pub error: Option<LfsError>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LfsAction {
    pub href: String,
    pub header: Option<HashMap<String, String>>,
    pub expires_in: Option<u64>,
    pub expires_at: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LfsError {
    pub code: u32,
    pub message: String,
}

pub struct LfsServer {
    storage_path: String,
    base_url: String,
}

impl LfsServer {
    pub fn new(storage_path: String, base_url: String) -> Self {
        Self {
            storage_path,
            base_url,
        }
    }
    
    /// Handle LFS batch API request
    pub async fn handle_batch(
        &self,
        repository_id: &str,
        request: LfsBatchRequest,
    ) -> Result<LfsBatchResponse, LfsError> {
        let mut objects = Vec::new();
        
        for obj in request.objects {
            match request.operation.as_str() {
                "download" => {
                    objects.push(self.handle_download_object(repository_id, obj).await?);
                }
                "upload" => {
                    objects.push(self.handle_upload_object(repository_id, obj).await?);
                }
                _ => {
                    return Err(LfsError {
                        code: 400,
                        message: format!("Unsupported operation: {}", request.operation),
                    });
                }
            }
        }
        
        Ok(LfsBatchResponse {
            transfer: "basic".to_string(),
            objects,
        })
    }
    
    async fn handle_download_object(
        &self,
        repository_id: &str,
        obj: LfsObject,
    ) -> Result<LfsObjectResponse, LfsError> {
        let object_path = self.get_object_path(repository_id, &obj.oid);
        
        match fs::metadata(&object_path).await {
            Ok(metadata) => {
                if metadata.len() != obj.size {
                    return Ok(LfsObjectResponse {
                        oid: obj.oid,
                        size: obj.size,
                        authenticated: obj.authenticated,
                        actions: None,
                        error: Some(LfsError {
                            code: 422,
                            message: "Size mismatch".to_string(),
                        }),
                    });
                }
                
                // Object exists, provide download URL
                let mut actions = HashMap::new();
                actions.insert("download".to_string(), LfsAction {
                    href: format!("{}/lfs/objects/{}", self.base_url, obj.oid),
                    header: Some(HashMap::new()),
                    expires_in: Some(3600), // 1 hour
                    expires_at: None,
                });
                
                Ok(LfsObjectResponse {
                    oid: obj.oid,
                    size: obj.size,
                    authenticated: obj.authenticated,
                    actions: Some(actions),
                    error: None,
                })
            }
            Err(_) => {
                // Object doesn't exist
                Ok(LfsObjectResponse {
                    oid: obj.oid,
                    size: obj.size,
                    authenticated: obj.authenticated,
                    actions: None,
                    error: Some(LfsError {
                        code: 404,
                        message: "Object not found".to_string(),
                    }),
                })
            }
        }
    }
    
    async fn handle_upload_object(
        &self,
        repository_id: &str,
        obj: LfsObject,
    ) -> Result<LfsObjectResponse, LfsError> {
        let object_path = self.get_object_path(repository_id, &obj.oid);
        
        // Check if object already exists
        if fs::metadata(&object_path).await.is_ok() {
            return Ok(LfsObjectResponse {
                oid: obj.oid,
                size: obj.size,
                authenticated: obj.authenticated,
                actions: None, // No action needed, object exists
                error: None,
            });
        }
        
        // Object doesn't exist, provide upload URL
        let mut actions = HashMap::new();
        actions.insert("upload".to_string(), LfsAction {
            href: format!("{}/lfs/objects/{}", self.base_url, obj.oid),
            header: Some({
                let mut headers = HashMap::new();
                headers.insert("Content-Type".to_string(), "application/octet-stream".to_string());
                headers
            }),
            expires_in: Some(3600), // 1 hour
            expires_at: None,
        });
        
        // Optionally provide verify action
        actions.insert("verify".to_string(), LfsAction {
            href: format!("{}/lfs/verify", self.base_url),
            header: Some({
                let mut headers = HashMap::new();
                headers.insert("Content-Type".to_string(), "application/json".to_string());
                headers
            }),
            expires_in: Some(3600),
            expires_at: None,
        });
        
        Ok(LfsObjectResponse {
            oid: obj.oid,
            size: obj.size,
            authenticated: obj.authenticated,
            actions: Some(actions),
            error: None,
        })
    }
    
    /// Handle direct object upload
    pub async fn upload_object(
        &self,
        repository_id: &str,
        oid: &str,
        data: Vec<u8>,
    ) -> Result<(), LfsError> {
        // Verify SHA256
        let mut hasher = Sha256::new();
        hasher.update(&data);
        let computed_oid = format!("{:x}", hasher.finalize());
        
        if computed_oid != oid {
            return Err(LfsError {
                code: 422,
                message: "SHA256 mismatch".to_string(),
            });
        }
        
        let object_path = self.get_object_path(repository_id, oid);
        
        // Ensure directory exists
        if let Some(parent) = std::path::Path::new(&object_path).parent() {
            fs::create_dir_all(parent).await.map_err(|e| LfsError {
                code: 500,
                message: format!("Failed to create directory: {}", e),
            })?;
        }
        
        // Write object to disk
        fs::write(&object_path, data).await.map_err(|e| LfsError {
            code: 500,
            message: format!("Failed to write object: {}", e),
        })?;
        
        Ok(())
    }
    
    /// Handle object download
    pub async fn download_object(
        &self,
        repository_id: &str,
        oid: &str,
    ) -> Result<Vec<u8>, LfsError> {
        let object_path = self.get_object_path(repository_id, oid);
        
        fs::read(&object_path).await.map_err(|e| LfsError {
            code: 404,
            message: format!("Object not found: {}", e),
        })
    }
    
    fn get_object_path(&self, repository_id: &str, oid: &str) -> String {
        // Store objects in a directory structure like: storage/repo_id/lfs/objects/ab/cd/abcd...
        let prefix = &oid[0..2];
        let suffix = &oid[2..4];
        format!("{}/{}/lfs/objects/{}/{}/{}", 
                self.storage_path, repository_id, prefix, suffix, oid)
    }
}

// Axum handlers
pub async fn lfs_batch_handler(
    Path((owner, repo)): Path<(String, String)>,
    State(lfs_server): State<std::sync::Arc<LfsServer>>,
    Json(request): Json<LfsBatchRequest>,
) -> Result<ResponseJson<LfsBatchResponse>, StatusCode> {
    let repository_id = format!("{}/{}", owner, repo);
    
    match lfs_server.handle_batch(&repository_id, request).await {
        Ok(response) => Ok(ResponseJson(response)),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn lfs_upload_handler(
    Path((owner, repo, oid)): Path<(String, String, String)>,
    State(lfs_server): State<std::sync::Arc<LfsServer>>,
    headers: HeaderMap,
    body: axum::body::Bytes,
) -> StatusCode {
    let repository_id = format!("{}/{}", owner, repo);
    
    // Verify content type
    if let Some(content_type) = headers.get("content-type") {
        if content_type != "application/octet-stream" {
            return StatusCode::BAD_REQUEST;
        }
    }
    
    match lfs_server.upload_object(&repository_id, &oid, body.to_vec()).await {
        Ok(_) => StatusCode::OK,
        Err(e) => match e.code {
            422 => StatusCode::UNPROCESSABLE_ENTITY,
            _ => StatusCode::INTERNAL_SERVER_ERROR,
        },
    }
}

pub async fn lfs_download_handler(
    Path((owner, repo, oid)): Path<(String, String, String)>,
    State(lfs_server): State<std::sync::Arc<LfsServer>>,
) -> Result<Vec<u8>, StatusCode> {
    let repository_id = format!("{}/{}", owner, repo);
    
    match lfs_server.download_object(&repository_id, &oid).await {
        Ok(data) => Ok(data),
        Err(_) => Err(StatusCode::NOT_FOUND),
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    
    #[tokio::test]
    async fn test_lfs_upload_download() {
        let temp_dir = TempDir::new().unwrap();
        let lfs_server = LfsServer::new(
            temp_dir.path().to_string_lossy().to_string(),
            "http://localhost:3000".to_string(),
        );
        
        let test_data = b"Hello, LFS World!";
        let mut hasher = Sha256::new();
        hasher.update(test_data);
        let oid = format!("{:x}", hasher.finalize());
        
        // Upload object
        lfs_server.upload_object("test/repo", &oid, test_data.to_vec())
            .await
            .unwrap();
        
        // Download object
        let downloaded = lfs_server.download_object("test/repo", &oid)
            .await
            .unwrap();
        
        assert_eq!(downloaded, test_data);
    }
    
    #[tokio::test]
    async fn test_lfs_batch_download() {
        let temp_dir = TempDir::new().unwrap();
        let lfs_server = LfsServer::new(
            temp_dir.path().to_string_lossy().to_string(),
            "http://localhost:3000".to_string(),
        );
        
        let test_data = b"Test data for batch";
        let mut hasher = Sha256::new();
        hasher.update(test_data);
        let oid = format!("{:x}", hasher.finalize());
        
        // Upload object first
        lfs_server.upload_object("test/repo", &oid, test_data.to_vec())
            .await
            .unwrap();
        
        // Test batch download request
        let request = LfsBatchRequest {
            operation: "download".to_string(),
            transfers: vec!["basic".to_string()],
            objects: vec![LfsObject {
                oid: oid.clone(),
                size: test_data.len() as u64,
                authenticated: Some(true),
            }],
            hash_algo: Some("sha256".to_string()),
        };
        
        let response = lfs_server.handle_batch("test/repo", request).await.unwrap();
        
        assert_eq!(response.objects.len(), 1);
        assert_eq!(response.objects[0].oid, oid);
        assert!(response.objects[0].actions.is_some());
        assert!(response.objects[0].error.is_none());
    }
}
```

## 🏊 Shallow Clone Implementation

### Understanding Shallow Clones

Shallow clones allow clients to fetch only recent history, dramatically reducing clone time and bandwidth for large repositories. This is essential for CI/CD systems and developers who don't need full history.

```rust
// src/git/shallow.rs
use std::collections::{HashMap, HashSet, VecDeque};
use serde::{Deserialize, Serialize};
use crate::git::{CommitObject, GitResult, GitError};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ShallowCloneRequest {
    pub depth: Option<u32>,           // Number of commits to fetch
    pub since: Option<String>,        // ISO 8601 date string
    pub until: Option<String>,        // ISO 8601 date string
    pub shallow_since: Option<String>, // Shallow boundary
    pub deepen: Option<u32>,          // Deepen existing shallow clone
    pub unshallow: bool,              // Convert shallow to full clone
}

#[derive(Debug, Clone)]
pub struct ShallowBoundary {
    pub commit_hash: String,
    pub is_boundary: bool,
}

pub struct ShallowCloneManager {
    commits: HashMap<String, CommitObject>,
    shallow_boundaries: HashMap<String, HashSet<String>>, // repo_id -> boundary commits
}

impl ShallowCloneManager {
    pub fn new() -> Self {
        Self {
            commits: HashMap::new(),
            shallow_boundaries: HashMap::new(),
        }
    }

    /// Calculate commits to include in shallow clone
    pub fn calculate_shallow_commits(
        &self,
        repository_id: &str,
        head_commits: Vec<String>,
        request: &ShallowCloneRequest,
    ) -> GitResult<(Vec<CommitObject>, Vec<String>)> {
        let mut included_commits = Vec::new();
        let mut shallow_boundaries = Vec::new();
        let mut visited = HashSet::new();
        let mut queue = VecDeque::new();

        // Initialize queue with head commits
        for head in head_commits {
            queue.push_back((head, 0u32)); // (commit_hash, depth)
        }

        while let Some((commit_hash, depth)) = queue.pop_front() {
            if visited.contains(&commit_hash) {
                continue;
            }
            visited.insert(commit_hash.clone());

            let commit = self.commits.get(&commit_hash)
                .ok_or_else(|| GitError::ObjectNotFound { hash: commit_hash.clone() })?;

            // Check if we should include this commit
            let should_include = self.should_include_commit(commit, depth, request)?;

            if should_include {
                included_commits.push(commit.clone());

                // Add parents to queue if within depth limit
                if let Some(max_depth) = request.depth {
                    if depth < max_depth - 1 {
                        for parent in &commit.parents {
                            queue.push_back((parent.clone(), depth + 1));
                        }
                    } else if depth == max_depth - 1 {
                        // Parents become shallow boundaries
                        shallow_boundaries.extend(commit.parents.clone());
                    }
                } else {
                    // No depth limit, add all parents
                    for parent in &commit.parents {
                        queue.push_back((parent.clone(), depth + 1));
                    }
                }
            } else {
                // This commit becomes a shallow boundary
                shallow_boundaries.push(commit_hash);
            }
        }

        Ok((included_commits, shallow_boundaries))
    }

    fn should_include_commit(
        &self,
        commit: &CommitObject,
        depth: u32,
        request: &ShallowCloneRequest,
    ) -> GitResult<bool> {
        // Check depth limit
        if let Some(max_depth) = request.depth {
            if depth >= max_depth {
                return Ok(false);
            }
        }

        // Check date constraints
        if let Some(since) = &request.since {
            let since_date = chrono::DateTime::parse_from_rfc3339(since)
                .map_err(|_| GitError::InvalidObjectType {
                    object_type: "Invalid date format".to_string()
                })?;

            if commit.committer.timestamp < since_date.with_timezone(&chrono::Utc) {
                return Ok(false);
            }
        }

        if let Some(until) = &request.until {
            let until_date = chrono::DateTime::parse_from_rfc3339(until)
                .map_err(|_| GitError::InvalidObjectType {
                    object_type: "Invalid date format".to_string()
                })?;

            if commit.committer.timestamp > until_date.with_timezone(&chrono::Utc) {
                return Ok(false);
            }
        }

        Ok(true)
    }

    /// Update shallow boundaries for a repository
    pub fn update_shallow_boundaries(
        &mut self,
        repository_id: &str,
        boundaries: Vec<String>,
    ) {
        self.shallow_boundaries.insert(repository_id.to_string(), boundaries.into_iter().collect());
    }

    /// Get shallow boundaries for a repository
    pub fn get_shallow_boundaries(&self, repository_id: &str) -> Option<&HashSet<String>> {
        self.shallow_boundaries.get(repository_id)
    }

    /// Check if a repository has shallow boundaries
    pub fn is_shallow(&self, repository_id: &str) -> bool {
        self.shallow_boundaries.contains_key(repository_id)
    }

    /// Deepen a shallow clone
    pub fn deepen_clone(
        &self,
        repository_id: &str,
        additional_depth: u32,
    ) -> GitResult<Vec<CommitObject>> {
        let boundaries = self.get_shallow_boundaries(repository_id)
            .ok_or_else(|| GitError::InvalidObjectType {
                object_type: "Repository is not shallow".to_string()
            })?;

        let mut additional_commits = Vec::new();
        let mut visited = HashSet::new();
        let mut queue = VecDeque::new();

        // Start from current boundaries
        for boundary in boundaries {
            queue.push_back((boundary.clone(), 0u32));
        }

        while let Some((commit_hash, depth)) = queue.pop_front() {
            if visited.contains(&commit_hash) || depth >= additional_depth {
                continue;
            }
            visited.insert(commit_hash.clone());

            if let Some(commit) = self.commits.get(&commit_hash) {
                additional_commits.push(commit.clone());

                // Add parents
                for parent in &commit.parents {
                    queue.push_back((parent.clone(), depth + 1));
                }
            }
        }

        Ok(additional_commits)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::git::GitSignature;
    use chrono::Utc;

    fn create_test_commit(hash: &str, parents: Vec<String>, days_ago: i64) -> CommitObject {
        let timestamp = Utc::now() - chrono::Duration::days(days_ago);

        CommitObject {
            tree: format!("tree_{}", hash),
            parents,
            author: GitSignature {
                name: "Test Author".to_string(),
                email: "<EMAIL>".to_string(),
                timestamp,
            },
            committer: GitSignature {
                name: "Test Author".to_string(),
                email: "<EMAIL>".to_string(),
                timestamp,
            },
            message: format!("Commit {}", hash),
        }
    }

    #[test]
    fn test_shallow_clone_depth() {
        let mut manager = ShallowCloneManager::new();

        // Create a linear history: A <- B <- C <- D
        let commit_a = create_test_commit("aaa", vec![], 3);
        let commit_b = create_test_commit("bbb", vec!["aaa".to_string()], 2);
        let commit_c = create_test_commit("ccc", vec!["bbb".to_string()], 1);
        let commit_d = create_test_commit("ddd", vec!["ccc".to_string()], 0);

        manager.commits.insert("aaa".to_string(), commit_a);
        manager.commits.insert("bbb".to_string(), commit_b);
        manager.commits.insert("ccc".to_string(), commit_c);
        manager.commits.insert("ddd".to_string(), commit_d);

        let request = ShallowCloneRequest {
            depth: Some(2),
            since: None,
            until: None,
            shallow_since: None,
            deepen: None,
            unshallow: false,
        };

        let (commits, boundaries) = manager
            .calculate_shallow_commits("test/repo", vec!["ddd".to_string()], &request)
            .unwrap();

        // Should include D and C (depth 2)
        assert_eq!(commits.len(), 2);
        // B should be a shallow boundary
        assert_eq!(boundaries.len(), 1);
        assert!(boundaries.contains(&"bbb".to_string()));
    }

    #[test]
    fn test_shallow_clone_since() {
        let mut manager = ShallowCloneManager::new();

        let commit_a = create_test_commit("aaa", vec![], 5);
        let commit_b = create_test_commit("bbb", vec!["aaa".to_string()], 2);
        let commit_c = create_test_commit("ccc", vec!["bbb".to_string()], 1);

        manager.commits.insert("aaa".to_string(), commit_a);
        manager.commits.insert("bbb".to_string(), commit_b);
        manager.commits.insert("ccc".to_string(), commit_c);

        let since_date = (Utc::now() - chrono::Duration::days(3)).to_rfc3339();
        let request = ShallowCloneRequest {
            depth: None,
            since: Some(since_date),
            until: None,
            shallow_since: None,
            deepen: None,
            unshallow: false,
        };

        let (commits, _) = manager
            .calculate_shallow_commits("test/repo", vec!["ccc".to_string()], &request)
            .unwrap();

        // Should only include commits B and C (within 3 days)
        assert_eq!(commits.len(), 2);
    }
}
```

## 🪝 Git Hooks System

### Understanding Git Hooks

Git hooks are scripts that run automatically at certain points in the Git workflow. They're essential for:
- **Code Quality**: Automated linting, testing before commits
- **Security**: Preventing sensitive data commits
- **Workflow Enforcement**: Ensuring proper commit messages, branch naming
- **Integration**: Triggering CI/CD pipelines, notifications

```rust
// src/git/hooks.rs
use std::collections::HashMap;
use std::process::{Command, Stdio};
use serde::{Deserialize, Serialize};
use tokio::process::Command as AsyncCommand;
use tokio::io::{AsyncWriteExt, AsyncReadExt};
use crate::git::{CommitObject, GitResult, GitError};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum HookType {
    // Client-side hooks
    PreCommit,
    PrepareCommitMsg,
    CommitMsg,
    PostCommit,

    // Server-side hooks
    PreReceive,
    Update,
    PostReceive,
    PostUpdate,

    // Custom hooks
    Custom(String),
}

impl HookType {
    pub fn as_str(&self) -> &str {
        match self {
            HookType::PreCommit => "pre-commit",
            HookType::PrepareCommitMsg => "prepare-commit-msg",
            HookType::CommitMsg => "commit-msg",
            HookType::PostCommit => "post-commit",
            HookType::PreReceive => "pre-receive",
            HookType::Update => "update",
            HookType::PostReceive => "post-receive",
            HookType::PostUpdate => "post-update",
            HookType::Custom(name) => name,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HookScript {
    pub name: String,
    pub hook_type: HookType,
    pub script_content: String,
    pub language: ScriptLanguage,
    pub enabled: bool,
    pub timeout_seconds: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ScriptLanguage {
    Shell,
    Python,
    Node,
    Ruby,
    Custom(String),
}

#[derive(Debug, Clone)]
pub struct HookContext {
    pub repository_id: String,
    pub user_id: String,
    pub ref_name: Option<String>,
    pub old_commit: Option<String>,
    pub new_commit: Option<String>,
    pub commit_message: Option<String>,
    pub environment: HashMap<String, String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct HookResult {
    pub success: bool,
    pub exit_code: i32,
    pub stdout: String,
    pub stderr: String,
    pub execution_time_ms: u64,
}

pub struct HookManager {
    hooks: HashMap<String, Vec<HookScript>>, // repository_id -> hooks
    hook_storage_path: String,
}

impl HookManager {
    pub fn new(hook_storage_path: String) -> Self {
        Self {
            hooks: HashMap::new(),
            hook_storage_path,
        }
    }

    /// Register a hook for a repository
    pub fn register_hook(&mut self, repository_id: String, hook: HookScript) {
        self.hooks
            .entry(repository_id)
            .or_insert_with(Vec::new)
            .push(hook);
    }

    /// Execute hooks of a specific type
    pub async fn execute_hooks(
        &self,
        repository_id: &str,
        hook_type: HookType,
        context: HookContext,
    ) -> GitResult<Vec<HookResult>> {
        let hooks = self.hooks.get(repository_id)
            .map(|h| h.iter().filter(|hook| {
                std::mem::discriminant(&hook.hook_type) == std::mem::discriminant(&hook_type)
                && hook.enabled
            }).collect::<Vec<_>>())
            .unwrap_or_default();

        let mut results = Vec::new();

        for hook in hooks {
            let result = self.execute_single_hook(hook, &context).await?;
            results.push(result);

            // Stop execution if hook fails (for blocking hooks)
            if !result.success && self.is_blocking_hook(&hook.hook_type) {
                break;
            }
        }

        Ok(results)
    }

    async fn execute_single_hook(
        &self,
        hook: &HookScript,
        context: &HookContext,
    ) -> GitResult<HookResult> {
        let start_time = std::time::Instant::now();

        // Create temporary script file
        let script_path = format!("{}/{}/{}.{}",
            self.hook_storage_path,
            context.repository_id,
            hook.name,
            self.get_file_extension(&hook.language)
        );

        // Ensure directory exists
        if let Some(parent) = std::path::Path::new(&script_path).parent() {
            tokio::fs::create_dir_all(parent).await.map_err(|e| GitError::SerializationError(e.to_string()))?;
        }

        // Write script content
        tokio::fs::write(&script_path, &hook.script_content).await
            .map_err(|e| GitError::SerializationError(e.to_string()))?;

        // Make script executable
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            let mut perms = tokio::fs::metadata(&script_path).await
                .map_err(|e| GitError::SerializationError(e.to_string()))?
                .permissions();
            perms.set_mode(0o755);
            tokio::fs::set_permissions(&script_path, perms).await
                .map_err(|e| GitError::SerializationError(e.to_string()))?;
        }

        // Execute script
        let mut cmd = self.create_command(&hook.language, &script_path);

        // Set environment variables
        for (key, value) in &context.environment {
            cmd.env(key, value);
        }

        // Set Git-specific environment variables
        cmd.env("GIT_REPOSITORY_ID", &context.repository_id);
        cmd.env("GIT_USER_ID", &context.user_id);

        if let Some(ref_name) = &context.ref_name {
            cmd.env("GIT_REF_NAME", ref_name);
        }
        if let Some(old_commit) = &context.old_commit {
            cmd.env("GIT_OLD_COMMIT", old_commit);
        }
        if let Some(new_commit) = &context.new_commit {
            cmd.env("GIT_NEW_COMMIT", new_commit);
        }

        // Execute with timeout
        let output = tokio::time::timeout(
            std::time::Duration::from_secs(hook.timeout_seconds as u64),
            cmd.output()
        ).await
        .map_err(|_| GitError::SerializationError("Hook execution timeout".to_string()))?
        .map_err(|e| GitError::SerializationError(format!("Hook execution failed: {}", e)))?;

        let execution_time = start_time.elapsed();

        // Clean up script file
        let _ = tokio::fs::remove_file(&script_path).await;

        Ok(HookResult {
            success: output.status.success(),
            exit_code: output.status.code().unwrap_or(-1),
            stdout: String::from_utf8_lossy(&output.stdout).to_string(),
            stderr: String::from_utf8_lossy(&output.stderr).to_string(),
            execution_time_ms: execution_time.as_millis() as u64,
        })
    }

    fn create_command(&self, language: &ScriptLanguage, script_path: &str) -> AsyncCommand {
        match language {
            ScriptLanguage::Shell => AsyncCommand::new("sh"),
            ScriptLanguage::Python => {
                let mut cmd = AsyncCommand::new("python3");
                cmd.arg(script_path);
                cmd
            },
            ScriptLanguage::Node => {
                let mut cmd = AsyncCommand::new("node");
                cmd.arg(script_path);
                cmd
            },
            ScriptLanguage::Ruby => {
                let mut cmd = AsyncCommand::new("ruby");
                cmd.arg(script_path);
                cmd
            },
            ScriptLanguage::Custom(interpreter) => {
                let mut cmd = AsyncCommand::new(interpreter);
                cmd.arg(script_path);
                cmd
            },
        }
    }

    fn get_file_extension(&self, language: &ScriptLanguage) -> &str {
        match language {
            ScriptLanguage::Shell => "sh",
            ScriptLanguage::Python => "py",
            ScriptLanguage::Node => "js",
            ScriptLanguage::Ruby => "rb",
            ScriptLanguage::Custom(_) => "script",
        }
    }

    fn is_blocking_hook(&self, hook_type: &HookType) -> bool {
        matches!(hook_type,
            HookType::PreCommit |
            HookType::CommitMsg |
            HookType::PreReceive |
            HookType::Update
        )
    }

    /// Built-in hook: Commit message validation
    pub fn create_commit_msg_validator() -> HookScript {
        HookScript {
            name: "commit-msg-validator".to_string(),
            hook_type: HookType::CommitMsg,
            script_content: r#"#!/bin/sh
# Validate commit message format
commit_msg=$(cat "$1")

# Check minimum length
if [ ${#commit_msg} -lt 10 ]; then
    echo "Error: Commit message too short (minimum 10 characters)"
    exit 1
fi

# Check for conventional commit format
if ! echo "$commit_msg" | grep -qE "^(feat|fix|docs|style|refactor|test|chore)(\(.+\))?: .+"; then
    echo "Error: Commit message must follow conventional commit format"
    echo "Examples:"
    echo "  feat: add new feature"
    echo "  fix(auth): resolve login issue"
    echo "  docs: update README"
    exit 1
fi

echo "Commit message validation passed"
exit 0
"#.to_string(),
            language: ScriptLanguage::Shell,
            enabled: true,
            timeout_seconds: 30,
        }
    }

    /// Built-in hook: Pre-receive security check
    pub fn create_security_checker() -> HookScript {
        HookScript {
            name: "security-checker".to_string(),
            hook_type: HookType::PreReceive,
            script_content: r#"#!/bin/sh
# Security checks for incoming commits

while read oldrev newrev refname; do
    # Check for secrets in commit messages and diffs
    if git log --oneline "$oldrev..$newrev" | grep -iE "(password|secret|key|token)" > /dev/null; then
        echo "Error: Potential secrets detected in commit messages"
        exit 1
    fi

    # Check for large files (>50MB)
    if git diff --name-only "$oldrev..$newrev" | xargs -I {} sh -c 'test -f "{}" && test $(stat -f%z "{}" 2>/dev/null || stat -c%s "{}" 2>/dev/null || echo 0) -gt 52428800' 2>/dev/null; then
        echo "Error: Files larger than 50MB detected. Consider using Git LFS."
        exit 1
    fi
done

echo "Security checks passed"
exit 0
"#.to_string(),
            language: ScriptLanguage::Shell,
            enabled: true,
            timeout_seconds: 60,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_hook_execution() {
        let temp_dir = TempDir::new().unwrap();
        let hook_manager = HookManager::new(temp_dir.path().to_string_lossy().to_string());

        let hook = HookScript {
            name: "test-hook".to_string(),
            hook_type: HookType::PostCommit,
            script_content: "#!/bin/sh\necho 'Hook executed successfully'\nexit 0".to_string(),
            language: ScriptLanguage::Shell,
            enabled: true,
            timeout_seconds: 30,
        };

        let context = HookContext {
            repository_id: "test/repo".to_string(),
            user_id: "user123".to_string(),
            ref_name: Some("refs/heads/main".to_string()),
            old_commit: Some("abc123".to_string()),
            new_commit: Some("def456".to_string()),
            commit_message: Some("Test commit".to_string()),
            environment: HashMap::new(),
        };

        let result = hook_manager.execute_single_hook(&hook, &context).await.unwrap();

        assert!(result.success);
        assert_eq!(result.exit_code, 0);
        assert!(result.stdout.contains("Hook executed successfully"));
    }

    #[test]
    fn test_commit_msg_validator() {
        let hook = HookManager::create_commit_msg_validator();
        assert_eq!(hook.hook_type.as_str(), "commit-msg");
        assert!(hook.enabled);
        assert!(hook.script_content.contains("conventional commit"));
    }
}
```

## 📦 Git Submodules Implementation

### Understanding Submodules

Git submodules allow you to keep a Git repository as a subdirectory of another Git repository. This is useful for:
- **Library Dependencies**: Include external libraries as submodules
- **Microservices**: Manage multiple related repositories
- **Shared Components**: Reuse code across projects

```rust
// src/git/submodules.rs
use std::collections::HashMap;
use serde::{Deserialize, Serialize};
use url::Url;
use crate::git::{GitResult, GitError};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Submodule {
    pub name: String,
    pub path: String,
    pub url: String,
    pub commit_hash: String,
    pub branch: Option<String>,
    pub update_strategy: SubmoduleUpdateStrategy,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SubmoduleUpdateStrategy {
    Checkout,  // Default: checkout specific commit
    Rebase,    // Rebase local changes onto upstream
    Merge,     // Merge upstream changes
    None,      // Don't update automatically
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitmodulesFile {
    pub submodules: Vec<Submodule>,
}

impl GitmodulesFile {
    /// Parse .gitmodules file content
    pub fn parse(content: &str) -> GitResult<Self> {
        let mut submodules = Vec::new();
        let mut current_submodule: Option<Submodule> = None;

        for line in content.lines() {
            let line = line.trim();

            if line.is_empty() || line.starts_with('#') {
                continue;
            }

            if line.starts_with('[') && line.ends_with(']') {
                // Save previous submodule
                if let Some(submodule) = current_submodule.take() {
                    submodules.push(submodule);
                }

                // Parse submodule section header
                if let Some(name) = line.strip_prefix("[submodule \"").and_then(|s| s.strip_suffix("\"]")) {
                    current_submodule = Some(Submodule {
                        name: name.to_string(),
                        path: String::new(),
                        url: String::new(),
                        commit_hash: String::new(),
                        branch: None,
                        update_strategy: SubmoduleUpdateStrategy::Checkout,
                    });
                }
            } else if let Some(ref mut submodule) = current_submodule {
                // Parse key-value pairs
                if let Some((key, value)) = line.split_once('=') {
                    let key = key.trim();
                    let value = value.trim();

                    match key {
                        "path" => submodule.path = value.to_string(),
                        "url" => submodule.url = value.to_string(),
                        "branch" => submodule.branch = Some(value.to_string()),
                        "update" => {
                            submodule.update_strategy = match value {
                                "rebase" => SubmoduleUpdateStrategy::Rebase,
                                "merge" => SubmoduleUpdateStrategy::Merge,
                                "none" => SubmoduleUpdateStrategy::None,
                                _ => SubmoduleUpdateStrategy::Checkout,
                            };
                        }
                        _ => {} // Ignore unknown keys
                    }
                }
            }
        }

        // Save last submodule
        if let Some(submodule) = current_submodule {
            submodules.push(submodule);
        }

        Ok(GitmodulesFile { submodules })
    }

    /// Generate .gitmodules file content
    pub fn serialize(&self) -> String {
        let mut content = String::new();

        for submodule in &self.submodules {
            content.push_str(&format!("[submodule \"{}\"]\n", submodule.name));
            content.push_str(&format!("\tpath = {}\n", submodule.path));
            content.push_str(&format!("\turl = {}\n", submodule.url));

            if let Some(ref branch) = submodule.branch {
                content.push_str(&format!("\tbranch = {}\n", branch));
            }

            match submodule.update_strategy {
                SubmoduleUpdateStrategy::Rebase => content.push_str("\tupdate = rebase\n"),
                SubmoduleUpdateStrategy::Merge => content.push_str("\tupdate = merge\n"),
                SubmoduleUpdateStrategy::None => content.push_str("\tupdate = none\n"),
                SubmoduleUpdateStrategy::Checkout => {} // Default, don't write
            }

            content.push('\n');
        }

        content
    }
}

pub struct SubmoduleManager {
    repository_path: String,
}

impl SubmoduleManager {
    pub fn new(repository_path: String) -> Self {
        Self { repository_path }
    }

    /// Add a new submodule
    pub async fn add_submodule(
        &self,
        name: String,
        path: String,
        url: String,
        branch: Option<String>,
    ) -> GitResult<Submodule> {
        // Validate URL
        Url::parse(&url).map_err(|_| GitError::InvalidObjectType {
            object_type: "Invalid submodule URL".to_string(),
        })?;

        // Validate path doesn't conflict with existing files
        let submodule_path = format!("{}/{}", self.repository_path, path);
        if tokio::fs::metadata(&submodule_path).await.is_ok() {
            return Err(GitError::InvalidObjectType {
                object_type: "Submodule path already exists".to_string(),
            });
        }

        // Clone the submodule repository
        let commit_hash = self.clone_submodule(&url, &submodule_path, branch.as_deref()).await?;

        let submodule = Submodule {
            name,
            path,
            url,
            commit_hash,
            branch,
            update_strategy: SubmoduleUpdateStrategy::Checkout,
        };

        // Update .gitmodules file
        self.update_gitmodules_file(&submodule).await?;

        Ok(submodule)
    }

    async fn clone_submodule(
        &self,
        url: &str,
        path: &str,
        branch: Option<&str>,
    ) -> GitResult<String> {
        // Create directory
        tokio::fs::create_dir_all(path).await
            .map_err(|e| GitError::SerializationError(e.to_string()))?;

        // Clone repository (simplified - in real implementation, use libgit2 or git2 crate)
        let mut cmd = tokio::process::Command::new("git");
        cmd.args(&["clone", url, path]);

        if let Some(branch) = branch {
            cmd.args(&["-b", branch]);
        }

        let output = cmd.output().await
            .map_err(|e| GitError::SerializationError(format!("Git clone failed: {}", e)))?;

        if !output.status.success() {
            return Err(GitError::SerializationError(
                String::from_utf8_lossy(&output.stderr).to_string()
            ));
        }

        // Get current commit hash
        let mut cmd = tokio::process::Command::new("git");
        cmd.args(&["-C", path, "rev-parse", "HEAD"]);

        let output = cmd.output().await
            .map_err(|e| GitError::SerializationError(format!("Git rev-parse failed: {}", e)))?;

        if !output.status.success() {
            return Err(GitError::SerializationError(
                String::from_utf8_lossy(&output.stderr).to_string()
            ));
        }

        Ok(String::from_utf8_lossy(&output.stdout).trim().to_string())
    }

    async fn update_gitmodules_file(&self, new_submodule: &Submodule) -> GitResult<()> {
        let gitmodules_path = format!("{}/.gitmodules", self.repository_path);

        // Read existing .gitmodules file
        let mut gitmodules = if let Ok(content) = tokio::fs::read_to_string(&gitmodules_path).await {
            GitmodulesFile::parse(&content)?
        } else {
            GitmodulesFile { submodules: Vec::new() }
        };

        // Add or update submodule
        if let Some(existing) = gitmodules.submodules.iter_mut().find(|s| s.name == new_submodule.name) {
            *existing = new_submodule.clone();
        } else {
            gitmodules.submodules.push(new_submodule.clone());
        }

        // Write updated .gitmodules file
        tokio::fs::write(&gitmodules_path, gitmodules.serialize()).await
            .map_err(|e| GitError::SerializationError(e.to_string()))?;

        Ok(())
    }

    /// Update all submodules
    pub async fn update_submodules(&self) -> GitResult<Vec<String>> {
        let gitmodules_path = format!("{}/.gitmodules", self.repository_path);
        let content = tokio::fs::read_to_string(&gitmodules_path).await
            .map_err(|_| GitError::ObjectNotFound { hash: ".gitmodules".to_string() })?;

        let gitmodules = GitmodulesFile::parse(&content)?;
        let mut updated_submodules = Vec::new();

        for submodule in &gitmodules.submodules {
            let submodule_path = format!("{}/{}", self.repository_path, submodule.path);

            match submodule.update_strategy {
                SubmoduleUpdateStrategy::None => continue,
                _ => {
                    self.update_single_submodule(&submodule_path, &submodule.update_strategy).await?;
                    updated_submodules.push(submodule.name.clone());
                }
            }
        }

        Ok(updated_submodules)
    }

    async fn update_single_submodule(
        &self,
        path: &str,
        strategy: &SubmoduleUpdateStrategy,
    ) -> GitResult<()> {
        let mut cmd = tokio::process::Command::new("git");
        cmd.args(&["-C", path, "fetch", "origin"]);

        let output = cmd.output().await
            .map_err(|e| GitError::SerializationError(format!("Git fetch failed: {}", e)))?;

        if !output.status.success() {
            return Err(GitError::SerializationError(
                String::from_utf8_lossy(&output.stderr).to_string()
            ));
        }

        // Apply update strategy
        match strategy {
            SubmoduleUpdateStrategy::Checkout => {
                // Checkout specific commit (default behavior)
                // This would typically checkout the commit specified in the parent repository
            }
            SubmoduleUpdateStrategy::Rebase => {
                let mut cmd = tokio::process::Command::new("git");
                cmd.args(&["-C", path, "rebase", "origin/HEAD"]);
                cmd.output().await
                    .map_err(|e| GitError::SerializationError(format!("Git rebase failed: {}", e)))?;
            }
            SubmoduleUpdateStrategy::Merge => {
                let mut cmd = tokio::process::Command::new("git");
                cmd.args(&["-C", path, "merge", "origin/HEAD"]);
                cmd.output().await
                    .map_err(|e| GitError::SerializationError(format!("Git merge failed: {}", e)))?;
            }
            SubmoduleUpdateStrategy::None => {}
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_gitmodules_parsing() {
        let content = r#"
[submodule "lib/awesome"]
	path = lib/awesome
	url = https://github.com/example/awesome.git
	branch = main
	update = rebase

[submodule "vendor/utils"]
	path = vendor/utils
	url = https://github.com/example/utils.git
"#;

        let gitmodules = GitmodulesFile::parse(content).unwrap();

        assert_eq!(gitmodules.submodules.len(), 2);

        let first = &gitmodules.submodules[0];
        assert_eq!(first.name, "lib/awesome");
        assert_eq!(first.path, "lib/awesome");
        assert_eq!(first.url, "https://github.com/example/awesome.git");
        assert_eq!(first.branch, Some("main".to_string()));
        assert!(matches!(first.update_strategy, SubmoduleUpdateStrategy::Rebase));

        let second = &gitmodules.submodules[1];
        assert_eq!(second.name, "vendor/utils");
        assert!(matches!(second.update_strategy, SubmoduleUpdateStrategy::Checkout));
    }

    #[test]
    fn test_gitmodules_serialization() {
        let gitmodules = GitmodulesFile {
            submodules: vec![
                Submodule {
                    name: "test-lib".to_string(),
                    path: "lib/test".to_string(),
                    url: "https://github.com/example/test.git".to_string(),
                    commit_hash: "abc123".to_string(),
                    branch: Some("develop".to_string()),
                    update_strategy: SubmoduleUpdateStrategy::Merge,
                }
            ],
        };

        let content = gitmodules.serialize();

        assert!(content.contains("[submodule \"test-lib\"]"));
        assert!(content.contains("path = lib/test"));
        assert!(content.contains("url = https://github.com/example/test.git"));
        assert!(content.contains("branch = develop"));
        assert!(content.contains("update = merge"));
    }
}
```

## 🌳 Git Worktrees Implementation

### Understanding Worktrees

Git worktrees allow you to have multiple working directories associated with the same repository. This enables:
- **Parallel Development**: Work on multiple branches simultaneously
- **Testing**: Test different versions without switching branches
- **CI/CD**: Build different branches in parallel

```rust
// src/git/worktrees.rs
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use serde::{Deserialize, Serialize};
use crate::git::{GitResult, GitError};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Worktree {
    pub path: PathBuf,
    pub branch: String,
    pub commit_hash: String,
    pub is_bare: bool,
    pub is_detached: bool,
    pub locked: Option<String>, // Lock reason if locked
}

pub struct WorktreeManager {
    repository_path: PathBuf,
    worktrees: HashMap<PathBuf, Worktree>,
}

impl WorktreeManager {
    pub fn new(repository_path: PathBuf) -> Self {
        Self {
            repository_path,
            worktrees: HashMap::new(),
        }
    }

    /// Create a new worktree
    pub async fn add_worktree(
        &mut self,
        path: PathBuf,
        branch: String,
        create_branch: bool,
    ) -> GitResult<Worktree> {
        // Validate path doesn't exist
        if path.exists() {
            return Err(GitError::InvalidObjectType {
                object_type: "Worktree path already exists".to_string(),
            });
        }

        // Create worktree directory
        tokio::fs::create_dir_all(&path).await
            .map_err(|e| GitError::SerializationError(e.to_string()))?;

        // Create git worktree
        let mut cmd = tokio::process::Command::new("git");
        cmd.args(&["-C", &self.repository_path.to_string_lossy(), "worktree", "add"]);

        if create_branch {
            cmd.arg("-b");
        }

        cmd.arg(&path);
        cmd.arg(&branch);

        let output = cmd.output().await
            .map_err(|e| GitError::SerializationError(format!("Git worktree add failed: {}", e)))?;

        if !output.status.success() {
            // Clean up directory if git command failed
            let _ = tokio::fs::remove_dir_all(&path).await;
            return Err(GitError::SerializationError(
                String::from_utf8_lossy(&output.stderr).to_string()
            ));
        }

        // Get commit hash for the branch
        let commit_hash = self.get_commit_hash(&path, &branch).await?;

        let worktree = Worktree {
            path: path.clone(),
            branch,
            commit_hash,
            is_bare: false,
            is_detached: false,
            locked: None,
        };

        self.worktrees.insert(path, worktree.clone());

        Ok(worktree)
    }

    /// Remove a worktree
    pub async fn remove_worktree(&mut self, path: &Path, force: bool) -> GitResult<()> {
        if !self.worktrees.contains_key(path) {
            return Err(GitError::ObjectNotFound {
                hash: path.to_string_lossy().to_string(),
            });
        }

        let mut cmd = tokio::process::Command::new("git");
        cmd.args(&["-C", &self.repository_path.to_string_lossy(), "worktree", "remove"]);

        if force {
            cmd.arg("--force");
        }

        cmd.arg(path);

        let output = cmd.output().await
            .map_err(|e| GitError::SerializationError(format!("Git worktree remove failed: {}", e)))?;

        if !output.status.success() {
            return Err(GitError::SerializationError(
                String::from_utf8_lossy(&output.stderr).to_string()
            ));
        }

        self.worktrees.remove(path);

        Ok(())
    }

    /// List all worktrees
    pub async fn list_worktrees(&mut self) -> GitResult<Vec<Worktree>> {
        let mut cmd = tokio::process::Command::new("git");
        cmd.args(&["-C", &self.repository_path.to_string_lossy(), "worktree", "list", "--porcelain"]);

        let output = cmd.output().await
            .map_err(|e| GitError::SerializationError(format!("Git worktree list failed: {}", e)))?;

        if !output.status.success() {
            return Err(GitError::SerializationError(
                String::from_utf8_lossy(&output.stderr).to_string()
            ));
        }

        let output_str = String::from_utf8_lossy(&output.stdout);
        self.parse_worktree_list(&output_str)
    }

    fn parse_worktree_list(&mut self, output: &str) -> GitResult<Vec<Worktree>> {
        let mut worktrees = Vec::new();
        let mut current_worktree: Option<Worktree> = None;

        for line in output.lines() {
            if line.starts_with("worktree ") {
                // Save previous worktree
                if let Some(worktree) = current_worktree.take() {
                    worktrees.push(worktree);
                }

                // Start new worktree
                let path = PathBuf::from(line.strip_prefix("worktree ").unwrap());
                current_worktree = Some(Worktree {
                    path,
                    branch: String::new(),
                    commit_hash: String::new(),
                    is_bare: false,
                    is_detached: false,
                    locked: None,
                });
            } else if let Some(ref mut worktree) = current_worktree {
                if line.starts_with("HEAD ") {
                    worktree.commit_hash = line.strip_prefix("HEAD ").unwrap().to_string();
                } else if line.starts_with("branch ") {
                    worktree.branch = line.strip_prefix("branch ").unwrap().to_string();
                } else if line == "bare" {
                    worktree.is_bare = true;
                } else if line == "detached" {
                    worktree.is_detached = true;
                } else if line.starts_with("locked ") {
                    worktree.locked = Some(line.strip_prefix("locked ").unwrap().to_string());
                }
            }
        }

        // Save last worktree
        if let Some(worktree) = current_worktree {
            worktrees.push(worktree);
        }

        // Update internal state
        self.worktrees.clear();
        for worktree in &worktrees {
            self.worktrees.insert(worktree.path.clone(), worktree.clone());
        }

        Ok(worktrees)
    }

    /// Lock a worktree
    pub async fn lock_worktree(&mut self, path: &Path, reason: Option<String>) -> GitResult<()> {
        let mut cmd = tokio::process::Command::new("git");
        cmd.args(&["-C", &self.repository_path.to_string_lossy(), "worktree", "lock"]);

        if let Some(ref reason) = reason {
            cmd.args(&["--reason", reason]);
        }

        cmd.arg(path);

        let output = cmd.output().await
            .map_err(|e| GitError::SerializationError(format!("Git worktree lock failed: {}", e)))?;

        if !output.status.success() {
            return Err(GitError::SerializationError(
                String::from_utf8_lossy(&output.stderr).to_string()
            ));
        }

        // Update internal state
        if let Some(worktree) = self.worktrees.get_mut(path) {
            worktree.locked = reason;
        }

        Ok(())
    }

    /// Unlock a worktree
    pub async fn unlock_worktree(&mut self, path: &Path) -> GitResult<()> {
        let mut cmd = tokio::process::Command::new("git");
        cmd.args(&["-C", &self.repository_path.to_string_lossy(), "worktree", "unlock"]);
        cmd.arg(path);

        let output = cmd.output().await
            .map_err(|e| GitError::SerializationError(format!("Git worktree unlock failed: {}", e)))?;

        if !output.status.success() {
            return Err(GitError::SerializationError(
                String::from_utf8_lossy(&output.stderr).to_string()
            ));
        }

        // Update internal state
        if let Some(worktree) = self.worktrees.get_mut(path) {
            worktree.locked = None;
        }

        Ok(())
    }

    async fn get_commit_hash(&self, worktree_path: &Path, branch: &str) -> GitResult<String> {
        let mut cmd = tokio::process::Command::new("git");
        cmd.args(&["-C", &worktree_path.to_string_lossy(), "rev-parse", "HEAD"]);

        let output = cmd.output().await
            .map_err(|e| GitError::SerializationError(format!("Git rev-parse failed: {}", e)))?;

        if !output.status.success() {
            return Err(GitError::SerializationError(
                String::from_utf8_lossy(&output.stderr).to_string()
            ));
        }

        Ok(String::from_utf8_lossy(&output.stdout).trim().to_string())
    }

    /// Get worktree by path
    pub fn get_worktree(&self, path: &Path) -> Option<&Worktree> {
        self.worktrees.get(path)
    }

    /// Check if a path is a worktree
    pub fn is_worktree(&self, path: &Path) -> bool {
        self.worktrees.contains_key(path)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[test]
    fn test_worktree_parsing() {
        let output = r#"worktree /path/to/main
HEAD abc123def456
branch refs/heads/main

worktree /path/to/feature
HEAD def456abc123
branch refs/heads/feature-branch

worktree /path/to/detached
HEAD 123456789abc
detached

worktree /path/to/locked
HEAD 789abcdef123
branch refs/heads/locked-branch
locked maintenance in progress
"#;

        let temp_dir = TempDir::new().unwrap();
        let mut manager = WorktreeManager::new(temp_dir.path().to_path_buf());
        let worktrees = manager.parse_worktree_list(output).unwrap();

        assert_eq!(worktrees.len(), 4);

        // Check main worktree
        let main_wt = &worktrees[0];
        assert_eq!(main_wt.path, PathBuf::from("/path/to/main"));
        assert_eq!(main_wt.branch, "refs/heads/main");
        assert_eq!(main_wt.commit_hash, "abc123def456");
        assert!(!main_wt.is_detached);
        assert!(main_wt.locked.is_none());

        // Check detached worktree
        let detached_wt = &worktrees[2];
        assert!(detached_wt.is_detached);

        // Check locked worktree
        let locked_wt = &worktrees[3];
        assert_eq!(locked_wt.locked, Some("maintenance in progress".to_string()));
    }
}
```

## 🎯 Key Takeaways

### Advanced Git Features Summary

1. **Git LFS**: Efficient handling of large binary files with pointer files and separate storage
2. **Shallow Clones**: Reduced bandwidth and storage for CI/CD and quick development setup
3. **Git Hooks**: Automated workflows and quality enforcement at key Git events
4. **Submodules**: Manage dependencies and shared code across repositories
5. **Worktrees**: Parallel development and testing without branch switching overhead

### Performance Considerations

- **LFS Storage**: Use cloud storage (S3, Azure Blob) for scalability
- **Shallow Clone Optimization**: Cache shallow boundaries for faster subsequent operations
- **Hook Execution**: Implement timeouts and resource limits to prevent abuse
- **Submodule Updates**: Batch operations and parallel processing for large dependency trees
- **Worktree Management**: Clean up unused worktrees to prevent disk space issues

### Security Best Practices

- **LFS Access Control**: Implement proper authentication for LFS object storage
- **Hook Sandboxing**: Run hooks in isolated environments with limited permissions
- **Submodule Validation**: Verify submodule URLs and prevent malicious repositories
- **Path Validation**: Sanitize all file paths to prevent directory traversal attacks

Ready to continue with [Module 10: Code Review System](./module-10-code-review.md)?
