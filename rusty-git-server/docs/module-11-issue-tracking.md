# Module 11: Issue Tracking & Project Management

## 🎯 Learning Objectives

By the end of this module, you will:
- Build a comprehensive issue tracking system with labels and milestones
- Implement project boards with Kanban-style workflow management
- Create issue templates for standardized bug reports and feature requests
- Master cross-repository issue linking and dependencies
- Build time tracking and effort estimation features
- Understand agile project management integration

## 📋 Why Issue Tracking Matters

Issue tracking is the backbone of software project management:
- **Bug Management**: Systematic tracking and resolution of software defects
- **Feature Planning**: Organize and prioritize new feature development
- **Project Visibility**: Transparent progress tracking for stakeholders
- **Team Coordination**: Clear assignment and communication of work items
- **Historical Record**: Maintain searchable history of decisions and changes

### Issue Management Architecture

```mermaid
graph TB
    subgraph "Issue Lifecycle"
        CREATE[Create Issue]
        TRIAGE[Triage & Label]
        ASSIGN[Assign to Developer]
        PROGRESS[In Progress]
        REVIEW[Code Review]
        TESTING[Testing]
        CLOSED[Closed]
    end
    
    subgraph "Project Management"
        MILESTONE[Milestones]
        BOARD[Project Boards]
        SPRINT[Sprint Planning]
        BACKLOG[Product Backlog]
    end
    
    subgraph "Issue Types"
        BUG[Bug Report]
        FEATURE[Feature Request]
        TASK[Task]
        EPIC[Epic]
        STORY[User Story]
    end
    
    CREATE --> TRIAGE
    TRIAGE --> ASSIGN
    ASSIGN --> PROGRESS
    PROGRESS --> REVIEW
    REVIEW --> TESTING
    TESTING --> CLOSED
    
    MILESTONE --> BOARD
    BOARD --> SPRINT
    SPRINT --> BACKLOG
    
    BUG --> CREATE
    FEATURE --> CREATE
    TASK --> CREATE
    EPIC --> CREATE
    STORY --> CREATE
    
    style CREATE fill:#e8f5e8
    style CLOSED fill:#fff3e0
    style BOARD fill:#e1f5fe
```

## 📊 Issue Data Model

### Core Data Structures

```rust
// src/issues/mod.rs
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Issue {
    pub id: Uuid,
    pub repository_id: Uuid,
    pub number: i32,                    // Sequential issue number per repository
    pub title: String,
    pub body: Option<String>,
    pub author_id: Uuid,
    pub assignee_id: Option<Uuid>,
    pub milestone_id: Option<Uuid>,
    pub state: IssueState,
    pub issue_type: IssueType,
    pub priority: IssuePriority,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub closed_at: Option<DateTime<Utc>>,
    pub closed_by: Option<Uuid>,
    
    // Metrics
    pub comments_count: i32,
    pub reactions_count: i32,
    pub time_estimate_hours: Option<i32>,
    pub time_spent_hours: Option<i32>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "issue_state", rename_all = "lowercase")]
pub enum IssueState {
    Open,
    Closed,
    Resolved,      // Closed as resolved
    Duplicate,     // Closed as duplicate
    Invalid,       // Closed as invalid
    WontFix,       // Closed as won't fix
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "issue_type", rename_all = "lowercase")]
pub enum IssueType {
    Bug,
    Feature,
    Enhancement,
    Task,
    Epic,
    Story,
    Question,
    Documentation,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "issue_priority", rename_all = "lowercase")]
pub enum IssuePriority {
    Critical,
    High,
    Medium,
    Low,
    Trivial,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct IssueLabel {
    pub id: Uuid,
    pub repository_id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub color: String,              // Hex color code
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Milestone {
    pub id: Uuid,
    pub repository_id: Uuid,
    pub title: String,
    pub description: Option<String>,
    pub due_date: Option<DateTime<Utc>>,
    pub state: MilestoneState,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub closed_at: Option<DateTime<Utc>>,
    
    // Progress tracking
    pub open_issues: i32,
    pub closed_issues: i32,
    pub total_issues: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "milestone_state", rename_all = "lowercase")]
pub enum MilestoneState {
    Open,
    Closed,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct IssueComment {
    pub id: Uuid,
    pub issue_id: Uuid,
    pub author_id: Uuid,
    pub body: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub is_minimized: bool,
    pub minimized_reason: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct IssueReaction {
    pub id: Uuid,
    pub issue_id: Uuid,
    pub comment_id: Option<Uuid>,
    pub user_id: Uuid,
    pub reaction_type: ReactionType,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "reaction_type", rename_all = "lowercase")]
pub enum ReactionType {
    ThumbsUp,
    ThumbsDown,
    Laugh,
    Hooray,
    Confused,
    Heart,
    Rocket,
    Eyes,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct ProjectBoard {
    pub id: Uuid,
    pub repository_id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub is_template: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct BoardColumn {
    pub id: Uuid,
    pub board_id: Uuid,
    pub name: String,
    pub position: i32,
    pub automation_rules: Option<serde_json::Value>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct BoardCard {
    pub id: Uuid,
    pub column_id: Uuid,
    pub issue_id: Option<Uuid>,
    pub pull_request_id: Option<Uuid>,
    pub note: Option<String>,
    pub position: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// Request/Response DTOs
#[derive(Debug, Deserialize)]
pub struct CreateIssueRequest {
    pub title: String,
    pub body: Option<String>,
    pub issue_type: IssueType,
    pub priority: IssuePriority,
    pub assignee_id: Option<Uuid>,
    pub milestone_id: Option<Uuid>,
    pub labels: Vec<String>,
    pub time_estimate_hours: Option<i32>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateIssueRequest {
    pub title: Option<String>,
    pub body: Option<String>,
    pub state: Option<IssueState>,
    pub assignee_id: Option<Uuid>,
    pub milestone_id: Option<Uuid>,
    pub labels: Option<Vec<String>>,
    pub time_estimate_hours: Option<i32>,
}

#[derive(Debug, Serialize)]
pub struct IssueResponse {
    pub issue: Issue,
    pub author: UserInfo,
    pub assignee: Option<UserInfo>,
    pub milestone: Option<Milestone>,
    pub labels: Vec<IssueLabel>,
    pub comments: Vec<IssueCommentResponse>,
    pub reactions: HashMap<ReactionType, i32>,
    pub linked_issues: Vec<LinkedIssue>,
}

#[derive(Debug, Serialize)]
pub struct IssueCommentResponse {
    pub comment: IssueComment,
    pub author: UserInfo,
    pub reactions: HashMap<ReactionType, i32>,
}

#[derive(Debug, Serialize)]
pub struct LinkedIssue {
    pub issue_id: Uuid,
    pub issue_number: i32,
    pub title: String,
    pub state: IssueState,
    pub link_type: IssueLinkType,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum IssueLinkType {
    Blocks,
    BlockedBy,
    Related,
    Duplicate,
    Subtask,
    Parent,
}

#[derive(Debug, Serialize)]
pub struct UserInfo {
    pub id: Uuid,
    pub username: String,
    pub email: String,
    pub avatar_url: Option<String>,
}
```

### Database Schema

```sql
-- Issues table
CREATE TABLE issues (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    repository_id UUID NOT NULL REFERENCES repositories(id),
    number SERIAL NOT NULL,
    title VARCHAR(255) NOT NULL,
    body TEXT,
    author_id UUID NOT NULL REFERENCES users(id),
    assignee_id UUID REFERENCES users(id),
    milestone_id UUID REFERENCES milestones(id),
    state issue_state NOT NULL DEFAULT 'open',
    issue_type issue_type NOT NULL DEFAULT 'bug',
    priority issue_priority NOT NULL DEFAULT 'medium',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    closed_at TIMESTAMP WITH TIME ZONE,
    closed_by UUID REFERENCES users(id),
    
    -- Metrics
    comments_count INTEGER DEFAULT 0,
    reactions_count INTEGER DEFAULT 0,
    time_estimate_hours INTEGER,
    time_spent_hours INTEGER,
    
    UNIQUE(repository_id, number)
);

-- Issue labels table
CREATE TABLE issue_labels (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    repository_id UUID NOT NULL REFERENCES repositories(id),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    color VARCHAR(7) NOT NULL, -- Hex color
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(repository_id, name)
);

-- Issue-label associations
CREATE TABLE issue_label_associations (
    issue_id UUID NOT NULL REFERENCES issues(id) ON DELETE CASCADE,
    label_id UUID NOT NULL REFERENCES issue_labels(id) ON DELETE CASCADE,
    PRIMARY KEY (issue_id, label_id)
);

-- Milestones table
CREATE TABLE milestones (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    repository_id UUID NOT NULL REFERENCES repositories(id),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    due_date TIMESTAMP WITH TIME ZONE,
    state milestone_state NOT NULL DEFAULT 'open',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    closed_at TIMESTAMP WITH TIME ZONE,
    
    -- Progress tracking (denormalized for performance)
    open_issues INTEGER DEFAULT 0,
    closed_issues INTEGER DEFAULT 0,
    total_issues INTEGER DEFAULT 0,
    
    UNIQUE(repository_id, title)
);

-- Issue comments table
CREATE TABLE issue_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    issue_id UUID NOT NULL REFERENCES issues(id) ON DELETE CASCADE,
    author_id UUID NOT NULL REFERENCES users(id),
    body TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_minimized BOOLEAN DEFAULT FALSE,
    minimized_reason VARCHAR(100)
);

-- Issue reactions table
CREATE TABLE issue_reactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    issue_id UUID REFERENCES issues(id) ON DELETE CASCADE,
    comment_id UUID REFERENCES issue_comments(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id),
    reaction_type reaction_type NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(issue_id, comment_id, user_id, reaction_type),
    CHECK ((issue_id IS NOT NULL) OR (comment_id IS NOT NULL))
);

-- Issue links table (for dependencies, duplicates, etc.)
CREATE TABLE issue_links (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    source_issue_id UUID NOT NULL REFERENCES issues(id) ON DELETE CASCADE,
    target_issue_id UUID NOT NULL REFERENCES issues(id) ON DELETE CASCADE,
    link_type VARCHAR(20) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES users(id),
    
    UNIQUE(source_issue_id, target_issue_id, link_type),
    CHECK (source_issue_id != target_issue_id)
);

-- Project boards table
CREATE TABLE project_boards (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    repository_id UUID NOT NULL REFERENCES repositories(id),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    is_template BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(repository_id, name)
);

-- Board columns table
CREATE TABLE board_columns (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    board_id UUID NOT NULL REFERENCES project_boards(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    position INTEGER NOT NULL,
    automation_rules JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(board_id, position)
);

-- Board cards table
CREATE TABLE board_cards (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    column_id UUID NOT NULL REFERENCES board_columns(id) ON DELETE CASCADE,
    issue_id UUID REFERENCES issues(id) ON DELETE CASCADE,
    pull_request_id UUID REFERENCES pull_requests(id) ON DELETE CASCADE,
    note TEXT,
    position INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CHECK ((issue_id IS NOT NULL) OR (pull_request_id IS NOT NULL) OR (note IS NOT NULL)),
    UNIQUE(column_id, position)
);

-- Indexes for performance
CREATE INDEX idx_issues_repository ON issues(repository_id, state, created_at DESC);
CREATE INDEX idx_issues_assignee ON issues(assignee_id, state, created_at DESC);
CREATE INDEX idx_issues_milestone ON issues(milestone_id, state);
CREATE INDEX idx_issues_author ON issues(author_id, created_at DESC);
CREATE INDEX idx_issue_comments_issue ON issue_comments(issue_id, created_at);
CREATE INDEX idx_issue_reactions_issue ON issue_reactions(issue_id);
CREATE INDEX idx_issue_reactions_comment ON issue_reactions(comment_id);
CREATE INDEX idx_issue_links_source ON issue_links(source_issue_id);
CREATE INDEX idx_issue_links_target ON issue_links(target_issue_id);

-- Full-text search on issue titles and bodies
CREATE INDEX idx_issues_search ON issues USING gin(to_tsvector('english', title || ' ' || COALESCE(body, '')));
```

## 🔧 Issue Service Implementation

### Core Issue Management Service

```rust
// src/issues/service.rs
use sqlx::{PgPool, Row, Transaction, Postgres};
use uuid::Uuid;
use chrono::Utc;
use std::collections::HashMap;
use crate::issues::*;

pub struct IssueService {
    db: PgPool,
}

impl IssueService {
    pub fn new(db: PgPool) -> Self {
        Self { db }
    }

    /// Create a new issue
    pub async fn create_issue(
        &self,
        repository_id: Uuid,
        author_id: Uuid,
        request: CreateIssueRequest,
    ) -> Result<Issue, IssueError> {
        let mut tx = self.db.begin().await?;

        // Get next issue number for repository
        let issue_number = self.get_next_issue_number(repository_id, &mut tx).await?;

        // Create issue
        let issue = sqlx::query_as::<_, Issue>(
            r#"
            INSERT INTO issues (
                repository_id, number, title, body, author_id, assignee_id,
                milestone_id, issue_type, priority, time_estimate_hours
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            RETURNING *
            "#
        )
        .bind(repository_id)
        .bind(issue_number)
        .bind(&request.title)
        .bind(&request.body)
        .bind(author_id)
        .bind(request.assignee_id)
        .bind(request.milestone_id)
        .bind(request.issue_type)
        .bind(request.priority)
        .bind(request.time_estimate_hours)
        .fetch_one(&mut *tx)
        .await?;

        // Add labels
        for label_name in &request.labels {
            let label = self.get_or_create_label(repository_id, label_name, &mut tx).await?;
            self.add_issue_label(issue.id, label.id, &mut tx).await?;
        }

        // Update milestone issue count
        if let Some(milestone_id) = request.milestone_id {
            self.update_milestone_counts(milestone_id, &mut tx).await?;
        }

        tx.commit().await?;

        Ok(issue)
    }

    /// Update an existing issue
    pub async fn update_issue(
        &self,
        issue_id: Uuid,
        user_id: Uuid,
        request: UpdateIssueRequest,
    ) -> Result<Issue, IssueError> {
        let mut tx = self.db.begin().await?;

        // Get current issue
        let current_issue = self.get_issue_by_id(issue_id, &mut tx).await?;

        // Check permissions
        self.check_issue_permissions(user_id, &current_issue)?;

        // Build dynamic update query
        let mut query = sqlx::QueryBuilder::new("UPDATE issues SET updated_at = NOW()");
        let mut has_updates = false;

        if let Some(title) = &request.title {
            query.push(", title = ");
            query.push_bind(title);
            has_updates = true;
        }

        if let Some(body) = &request.body {
            query.push(", body = ");
            query.push_bind(body);
            has_updates = true;
        }

        if let Some(state) = &request.state {
            query.push(", state = ");
            query.push_bind(state);
            has_updates = true;

            // Set closed_at and closed_by if closing
            if matches!(state, IssueState::Closed | IssueState::Resolved | IssueState::Duplicate | IssueState::Invalid | IssueState::WontFix) {
                query.push(", closed_at = NOW(), closed_by = ");
                query.push_bind(user_id);
            } else if current_issue.closed_at.is_some() {
                // Reopening issue
                query.push(", closed_at = NULL, closed_by = NULL");
            }
        }

        if let Some(assignee_id) = request.assignee_id {
            query.push(", assignee_id = ");
            query.push_bind(assignee_id);
            has_updates = true;
        }

        if let Some(milestone_id) = request.milestone_id {
            query.push(", milestone_id = ");
            query.push_bind(milestone_id);
            has_updates = true;
        }

        if let Some(time_estimate) = request.time_estimate_hours {
            query.push(", time_estimate_hours = ");
            query.push_bind(time_estimate);
            has_updates = true;
        }

        if !has_updates && request.labels.is_none() {
            return Ok(current_issue);
        }

        let updated_issue = if has_updates {
            query.push(" WHERE id = ");
            query.push_bind(issue_id);
            query.push(" RETURNING *");

            query.build_query_as::<Issue>()
                .fetch_one(&mut *tx)
                .await?
        } else {
            current_issue
        };

        // Update labels if provided
        if let Some(labels) = &request.labels {
            self.update_issue_labels(issue_id, labels, &mut tx).await?;
        }

        // Update milestone counts if milestone changed
        if request.milestone_id.is_some() {
            if let Some(old_milestone) = current_issue.milestone_id {
                self.update_milestone_counts(old_milestone, &mut tx).await?;
            }
            if let Some(new_milestone) = request.milestone_id {
                self.update_milestone_counts(new_milestone, &mut tx).await?;
            }
        }

        tx.commit().await?;

        Ok(updated_issue)
    }

    /// Get issue with full details
    pub async fn get_issue_details(
        &self,
        repository_id: Uuid,
        issue_number: i32,
    ) -> Result<IssueResponse, IssueError> {
        let issue = self.get_issue_by_number(repository_id, issue_number).await?;

        // Get author info
        let author = self.get_user_info(issue.author_id).await?;

        // Get assignee info
        let assignee = if let Some(assignee_id) = issue.assignee_id {
            Some(self.get_user_info(assignee_id).await?)
        } else {
            None
        };

        // Get milestone
        let milestone = if let Some(milestone_id) = issue.milestone_id {
            Some(self.get_milestone(milestone_id).await?)
        } else {
            None
        };

        // Get labels
        let labels = self.get_issue_labels(issue.id).await?;

        // Get comments
        let comments = self.get_issue_comments(issue.id).await?;

        // Get reactions
        let reactions = self.get_issue_reactions(issue.id).await?;

        // Get linked issues
        let linked_issues = self.get_linked_issues(issue.id).await?;

        Ok(IssueResponse {
            issue,
            author,
            assignee,
            milestone,
            labels,
            comments,
            reactions,
            linked_issues,
        })
    }

    /// Add comment to issue
    pub async fn add_comment(
        &self,
        issue_id: Uuid,
        author_id: Uuid,
        body: String,
    ) -> Result<IssueComment, IssueError> {
        let mut tx = self.db.begin().await?;

        // Create comment
        let comment = sqlx::query_as::<_, IssueComment>(
            r#"
            INSERT INTO issue_comments (issue_id, author_id, body)
            VALUES ($1, $2, $3)
            RETURNING *
            "#
        )
        .bind(issue_id)
        .bind(author_id)
        .bind(&body)
        .fetch_one(&mut *tx)
        .await?;

        // Update issue comment count
        sqlx::query(
            "UPDATE issues SET comments_count = comments_count + 1, updated_at = NOW() WHERE id = $1"
        )
        .bind(issue_id)
        .execute(&mut *tx)
        .await?;

        tx.commit().await?;

        Ok(comment)
    }

    /// Add reaction to issue or comment
    pub async fn add_reaction(
        &self,
        user_id: Uuid,
        issue_id: Option<Uuid>,
        comment_id: Option<Uuid>,
        reaction_type: ReactionType,
    ) -> Result<IssueReaction, IssueError> {
        let reaction = sqlx::query_as::<_, IssueReaction>(
            r#"
            INSERT INTO issue_reactions (issue_id, comment_id, user_id, reaction_type)
            VALUES ($1, $2, $3, $4)
            ON CONFLICT (issue_id, comment_id, user_id, reaction_type) DO NOTHING
            RETURNING *
            "#
        )
        .bind(issue_id)
        .bind(comment_id)
        .bind(user_id)
        .bind(reaction_type)
        .fetch_one(&self.db)
        .await?;

        // Update reaction count
        if issue_id.is_some() {
            sqlx::query(
                "UPDATE issues SET reactions_count = reactions_count + 1 WHERE id = $1"
            )
            .bind(issue_id)
            .execute(&self.db)
            .await?;
        }

        Ok(reaction)
    }

    /// Link two issues
    pub async fn link_issues(
        &self,
        source_issue_id: Uuid,
        target_issue_id: Uuid,
        link_type: IssueLinkType,
        user_id: Uuid,
    ) -> Result<(), IssueError> {
        let link_type_str = match link_type {
            IssueLinkType::Blocks => "blocks",
            IssueLinkType::BlockedBy => "blocked_by",
            IssueLinkType::Related => "related",
            IssueLinkType::Duplicate => "duplicate",
            IssueLinkType::Subtask => "subtask",
            IssueLinkType::Parent => "parent",
        };

        sqlx::query(
            r#"
            INSERT INTO issue_links (source_issue_id, target_issue_id, link_type, created_by)
            VALUES ($1, $2, $3, $4)
            ON CONFLICT (source_issue_id, target_issue_id, link_type) DO NOTHING
            "#
        )
        .bind(source_issue_id)
        .bind(target_issue_id)
        .bind(link_type_str)
        .bind(user_id)
        .execute(&self.db)
        .await?;

        Ok(())
    }

    /// Search issues with filters
    pub async fn search_issues(
        &self,
        repository_id: Uuid,
        filters: IssueFilters,
        pagination: Pagination,
    ) -> Result<IssueSearchResult, IssueError> {
        let mut query = sqlx::QueryBuilder::new(
            "SELECT i.*, u.username as author_username FROM issues i JOIN users u ON i.author_id = u.id WHERE i.repository_id = "
        );
        query.push_bind(repository_id);

        // Apply filters
        if let Some(state) = &filters.state {
            query.push(" AND i.state = ");
            query.push_bind(state);
        }

        if let Some(assignee_id) = filters.assignee_id {
            query.push(" AND i.assignee_id = ");
            query.push_bind(assignee_id);
        }

        if let Some(milestone_id) = filters.milestone_id {
            query.push(" AND i.milestone_id = ");
            query.push_bind(milestone_id);
        }

        if let Some(issue_type) = &filters.issue_type {
            query.push(" AND i.issue_type = ");
            query.push_bind(issue_type);
        }

        if let Some(search_text) = &filters.search {
            query.push(" AND to_tsvector('english', i.title || ' ' || COALESCE(i.body, '')) @@ plainto_tsquery('english', ");
            query.push_bind(search_text);
            query.push(")");
        }

        // Apply sorting
        match filters.sort.as_deref().unwrap_or("created") {
            "created" => query.push(" ORDER BY i.created_at DESC"),
            "updated" => query.push(" ORDER BY i.updated_at DESC"),
            "comments" => query.push(" ORDER BY i.comments_count DESC"),
            "reactions" => query.push(" ORDER BY i.reactions_count DESC"),
            _ => query.push(" ORDER BY i.created_at DESC"),
        };

        // Apply pagination
        query.push(" LIMIT ");
        query.push_bind(pagination.limit);
        query.push(" OFFSET ");
        query.push_bind(pagination.offset);

        let issues = query.build_query_as::<IssueListItem>()
            .fetch_all(&self.db)
            .await?;

        // Get total count
        let total_count = self.count_issues(repository_id, &filters).await?;

        Ok(IssueSearchResult {
            issues,
            total_count,
            page: pagination.page,
            per_page: pagination.limit,
        })
    }

    // Helper methods
    async fn get_next_issue_number(
        &self,
        repository_id: Uuid,
        tx: &mut Transaction<'_, Postgres>,
    ) -> Result<i32, IssueError> {
        let next_number: Option<i32> = sqlx::query_scalar(
            "SELECT COALESCE(MAX(number), 0) + 1 FROM issues WHERE repository_id = $1"
        )
        .bind(repository_id)
        .fetch_one(&mut **tx)
        .await?;

        Ok(next_number.unwrap_or(1))
    }

    async fn get_issue_by_id(
        &self,
        issue_id: Uuid,
        tx: &mut Transaction<'_, Postgres>,
    ) -> Result<Issue, IssueError> {
        let issue = sqlx::query_as::<_, Issue>(
            "SELECT * FROM issues WHERE id = $1"
        )
        .bind(issue_id)
        .fetch_optional(&mut **tx)
        .await?
        .ok_or(IssueError::IssueNotFound)?;

        Ok(issue)
    }

    async fn get_issue_by_number(
        &self,
        repository_id: Uuid,
        issue_number: i32,
    ) -> Result<Issue, IssueError> {
        let issue = sqlx::query_as::<_, Issue>(
            "SELECT * FROM issues WHERE repository_id = $1 AND number = $2"
        )
        .bind(repository_id)
        .bind(issue_number)
        .fetch_optional(&self.db)
        .await?
        .ok_or(IssueError::IssueNotFound)?;

        Ok(issue)
    }

    fn check_issue_permissions(&self, user_id: Uuid, issue: &Issue) -> Result<(), IssueError> {
        // Simplified permission check - in real implementation would check repository permissions
        Ok(())
    }

    async fn get_or_create_label(
        &self,
        repository_id: Uuid,
        label_name: &str,
        tx: &mut Transaction<'_, Postgres>,
    ) -> Result<IssueLabel, IssueError> {
        // Try to get existing label
        if let Some(label) = sqlx::query_as::<_, IssueLabel>(
            "SELECT * FROM issue_labels WHERE repository_id = $1 AND name = $2"
        )
        .bind(repository_id)
        .bind(label_name)
        .fetch_optional(&mut **tx)
        .await? {
            return Ok(label);
        }

        // Create new label with random color
        let color = self.generate_label_color(label_name);
        let label = sqlx::query_as::<_, IssueLabel>(
            r#"
            INSERT INTO issue_labels (repository_id, name, color)
            VALUES ($1, $2, $3)
            RETURNING *
            "#
        )
        .bind(repository_id)
        .bind(label_name)
        .bind(&color)
        .fetch_one(&mut **tx)
        .await?;

        Ok(label)
    }

    fn generate_label_color(&self, label_name: &str) -> String {
        // Generate consistent color based on label name
        let hash = label_name.chars()
            .fold(0u32, |acc, c| acc.wrapping_mul(31).wrapping_add(c as u32));

        let hue = hash % 360;
        format!("#{:06x}", ((hue as u32) << 16) | 0x8080)
    }

    async fn add_issue_label(
        &self,
        issue_id: Uuid,
        label_id: Uuid,
        tx: &mut Transaction<'_, Postgres>,
    ) -> Result<(), IssueError> {
        sqlx::query(
            "INSERT INTO issue_label_associations (issue_id, label_id) VALUES ($1, $2) ON CONFLICT DO NOTHING"
        )
        .bind(issue_id)
        .bind(label_id)
        .execute(&mut **tx)
        .await?;

        Ok(())
    }

    async fn update_milestone_counts(
        &self,
        milestone_id: Uuid,
        tx: &mut Transaction<'_, Postgres>,
    ) -> Result<(), IssueError> {
        sqlx::query(
            r#"
            UPDATE milestones SET
                open_issues = (SELECT COUNT(*) FROM issues WHERE milestone_id = $1 AND state = 'open'),
                closed_issues = (SELECT COUNT(*) FROM issues WHERE milestone_id = $1 AND state != 'open'),
                total_issues = (SELECT COUNT(*) FROM issues WHERE milestone_id = $1),
                updated_at = NOW()
            WHERE id = $1
            "#
        )
        .bind(milestone_id)
        .execute(&mut **tx)
        .await?;

        Ok(())
    }

    // Additional helper methods would be implemented here...
    async fn get_user_info(&self, user_id: Uuid) -> Result<UserInfo, IssueError> {
        // Implementation would fetch user information
        Ok(UserInfo {
            id: user_id,
            username: "user".to_string(),
            email: "<EMAIL>".to_string(),
            avatar_url: None,
        })
    }

    async fn get_milestone(&self, milestone_id: Uuid) -> Result<Milestone, IssueError> {
        // Implementation would fetch milestone
        Ok(Milestone {
            id: milestone_id,
            repository_id: Uuid::new_v4(),
            title: "Milestone".to_string(),
            description: None,
            due_date: None,
            state: MilestoneState::Open,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            closed_at: None,
            open_issues: 0,
            closed_issues: 0,
            total_issues: 0,
        })
    }

    async fn get_issue_labels(&self, issue_id: Uuid) -> Result<Vec<IssueLabel>, IssueError> {
        // Implementation would fetch issue labels
        Ok(Vec::new())
    }

    async fn get_issue_comments(&self, issue_id: Uuid) -> Result<Vec<IssueCommentResponse>, IssueError> {
        // Implementation would fetch comments with reactions
        Ok(Vec::new())
    }

    async fn get_issue_reactions(&self, issue_id: Uuid) -> Result<HashMap<ReactionType, i32>, IssueError> {
        // Implementation would fetch reaction counts
        Ok(HashMap::new())
    }

    async fn get_linked_issues(&self, issue_id: Uuid) -> Result<Vec<LinkedIssue>, IssueError> {
        // Implementation would fetch linked issues
        Ok(Vec::new())
    }

    async fn update_issue_labels(
        &self,
        issue_id: Uuid,
        labels: &[String],
        tx: &mut Transaction<'_, Postgres>,
    ) -> Result<(), IssueError> {
        // Remove existing labels
        sqlx::query("DELETE FROM issue_label_associations WHERE issue_id = $1")
            .bind(issue_id)
            .execute(&mut **tx)
            .await?;

        // Add new labels
        for label_name in labels {
            let label = self.get_or_create_label(Uuid::new_v4(), label_name, tx).await?; // Would get repo_id properly
            self.add_issue_label(issue_id, label.id, tx).await?;
        }

        Ok(())
    }

    async fn count_issues(&self, repository_id: Uuid, filters: &IssueFilters) -> Result<i64, IssueError> {
        // Implementation would count issues with filters
        Ok(0)
    }
}

// Supporting types
#[derive(Debug)]
pub struct IssueFilters {
    pub state: Option<IssueState>,
    pub assignee_id: Option<Uuid>,
    pub milestone_id: Option<Uuid>,
    pub issue_type: Option<IssueType>,
    pub labels: Option<Vec<String>>,
    pub search: Option<String>,
    pub sort: Option<String>,
}

#[derive(Debug)]
pub struct Pagination {
    pub page: i32,
    pub limit: i32,
    pub offset: i32,
}

#[derive(Debug, Serialize)]
pub struct IssueSearchResult {
    pub issues: Vec<IssueListItem>,
    pub total_count: i64,
    pub page: i32,
    pub per_page: i32,
}

#[derive(Debug, Serialize, FromRow)]
pub struct IssueListItem {
    pub id: Uuid,
    pub number: i32,
    pub title: String,
    pub state: IssueState,
    pub issue_type: IssueType,
    pub priority: IssuePriority,
    pub author_username: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub comments_count: i32,
    pub reactions_count: i32,
}

#[derive(Debug, thiserror::Error)]
pub enum IssueError {
    #[error("Issue not found")]
    IssueNotFound,
    #[error("Unauthorized")]
    Unauthorized,
    #[error("Database error: {0}")]
    DatabaseError(#[from] sqlx::Error),
}
```

## 📋 Project Boards Implementation

### Kanban-Style Project Management

```rust
// src/issues/boards.rs
use sqlx::{PgPool, Transaction, Postgres};
use uuid::Uuid;
use serde::{Deserialize, Serialize};
use crate::issues::*;

#[derive(Debug, Serialize)]
pub struct BoardResponse {
    pub board: ProjectBoard,
    pub columns: Vec<ColumnResponse>,
    pub total_cards: i32,
}

#[derive(Debug, Serialize)]
pub struct ColumnResponse {
    pub column: BoardColumn,
    pub cards: Vec<CardResponse>,
}

#[derive(Debug, Serialize)]
pub struct CardResponse {
    pub card: BoardCard,
    pub issue: Option<IssueListItem>,
    pub pull_request: Option<PullRequestListItem>,
}

#[derive(Debug, Deserialize)]
pub struct CreateBoardRequest {
    pub name: String,
    pub description: Option<String>,
    pub template: Option<BoardTemplate>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateBoardRequest {
    pub name: Option<String>,
    pub description: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct CreateColumnRequest {
    pub name: String,
    pub automation_rules: Option<ColumnAutomation>,
}

#[derive(Debug, Deserialize)]
pub struct MoveCardRequest {
    pub column_id: Uuid,
    pub position: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BoardTemplate {
    Basic,           // To Do, In Progress, Done
    Development,     // Backlog, To Do, In Progress, Review, Testing, Done
    Kanban,         // Backlog, Ready, In Progress, Review, Done
    Scrum,          // Product Backlog, Sprint Backlog, In Progress, Review, Done
    BugTriage,      // New, Triaged, In Progress, Testing, Closed
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ColumnAutomation {
    pub auto_move_on_pr_open: bool,
    pub auto_move_on_pr_merged: bool,
    pub auto_move_on_issue_closed: bool,
    pub auto_assign_labels: Vec<String>,
}

pub struct BoardService {
    db: PgPool,
}

impl BoardService {
    pub fn new(db: PgPool) -> Self {
        Self { db }
    }

    /// Create a new project board
    pub async fn create_board(
        &self,
        repository_id: Uuid,
        user_id: Uuid,
        request: CreateBoardRequest,
    ) -> Result<ProjectBoard, BoardError> {
        let mut tx = self.db.begin().await?;

        // Create board
        let board = sqlx::query_as::<_, ProjectBoard>(
            r#"
            INSERT INTO project_boards (repository_id, name, description)
            VALUES ($1, $2, $3)
            RETURNING *
            "#
        )
        .bind(repository_id)
        .bind(&request.name)
        .bind(&request.description)
        .fetch_one(&mut *tx)
        .await?;

        // Create default columns based on template
        if let Some(template) = request.template {
            self.create_template_columns(board.id, template, &mut tx).await?;
        } else {
            // Create basic columns
            self.create_basic_columns(board.id, &mut tx).await?;
        }

        tx.commit().await?;

        Ok(board)
    }

    /// Get board with all columns and cards
    pub async fn get_board_details(
        &self,
        board_id: Uuid,
    ) -> Result<BoardResponse, BoardError> {
        let board = self.get_board(board_id).await?;

        // Get columns with cards
        let columns = sqlx::query_as::<_, BoardColumn>(
            "SELECT * FROM board_columns WHERE board_id = $1 ORDER BY position"
        )
        .bind(board_id)
        .fetch_all(&self.db)
        .await?;

        let mut column_responses = Vec::new();
        let mut total_cards = 0;

        for column in columns {
            let cards = self.get_column_cards(column.id).await?;
            total_cards += cards.len() as i32;

            column_responses.push(ColumnResponse {
                column,
                cards,
            });
        }

        Ok(BoardResponse {
            board,
            columns: column_responses,
            total_cards,
        })
    }

    /// Add card to board
    pub async fn add_card(
        &self,
        column_id: Uuid,
        issue_id: Option<Uuid>,
        pull_request_id: Option<Uuid>,
        note: Option<String>,
        user_id: Uuid,
    ) -> Result<BoardCard, BoardError> {
        let mut tx = self.db.begin().await?;

        // Get next position in column
        let position = self.get_next_card_position(column_id, &mut tx).await?;

        // Create card
        let card = sqlx::query_as::<_, BoardCard>(
            r#"
            INSERT INTO board_cards (column_id, issue_id, pull_request_id, note, position)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING *
            "#
        )
        .bind(column_id)
        .bind(issue_id)
        .bind(pull_request_id)
        .bind(&note)
        .bind(position)
        .fetch_one(&mut *tx)
        .await?;

        tx.commit().await?;

        Ok(card)
    }

    /// Move card to different column/position
    pub async fn move_card(
        &self,
        card_id: Uuid,
        request: MoveCardRequest,
        user_id: Uuid,
    ) -> Result<BoardCard, BoardError> {
        let mut tx = self.db.begin().await?;

        // Get current card
        let current_card = self.get_card(card_id, &mut tx).await?;

        // If moving to same column, just update position
        if current_card.column_id == request.column_id {
            self.reorder_cards_in_column(request.column_id, card_id, request.position, &mut tx).await?;
        } else {
            // Moving to different column
            self.move_card_between_columns(
                card_id,
                current_card.column_id,
                request.column_id,
                request.position,
                &mut tx
            ).await?;

            // Apply automation rules
            self.apply_column_automation(request.column_id, &current_card, &mut tx).await?;
        }

        // Update card
        let updated_card = sqlx::query_as::<_, BoardCard>(
            r#"
            UPDATE board_cards
            SET column_id = $1, position = $2, updated_at = NOW()
            WHERE id = $3
            RETURNING *
            "#
        )
        .bind(request.column_id)
        .bind(request.position)
        .bind(card_id)
        .fetch_one(&mut *tx)
        .await?;

        tx.commit().await?;

        Ok(updated_card)
    }

    /// Create column automation rules
    async fn apply_column_automation(
        &self,
        column_id: Uuid,
        card: &BoardCard,
        tx: &mut Transaction<'_, Postgres>,
    ) -> Result<(), BoardError> {
        // Get column automation rules
        let column = sqlx::query_as::<_, BoardColumn>(
            "SELECT * FROM board_columns WHERE id = $1"
        )
        .bind(column_id)
        .fetch_one(&mut **tx)
        .await?;

        if let Some(automation_json) = column.automation_rules {
            if let Ok(automation) = serde_json::from_value::<ColumnAutomation>(automation_json) {
                // Apply auto-assign labels
                if !automation.auto_assign_labels.is_empty() {
                    if let Some(issue_id) = card.issue_id {
                        self.auto_assign_labels(issue_id, &automation.auto_assign_labels, tx).await?;
                    }
                }

                // Other automation rules would be applied here
            }
        }

        Ok(())
    }

    async fn auto_assign_labels(
        &self,
        issue_id: Uuid,
        labels: &[String],
        tx: &mut Transaction<'_, Postgres>,
    ) -> Result<(), BoardError> {
        // Implementation would add labels to the issue
        Ok(())
    }

    async fn create_template_columns(
        &self,
        board_id: Uuid,
        template: BoardTemplate,
        tx: &mut Transaction<'_, Postgres>,
    ) -> Result<(), BoardError> {
        let columns = match template {
            BoardTemplate::Basic => vec![
                ("To Do", None),
                ("In Progress", Some(ColumnAutomation {
                    auto_move_on_pr_open: false,
                    auto_move_on_pr_merged: false,
                    auto_move_on_issue_closed: false,
                    auto_assign_labels: vec!["in-progress".to_string()],
                })),
                ("Done", Some(ColumnAutomation {
                    auto_move_on_pr_merged: true,
                    auto_move_on_issue_closed: true,
                    auto_move_on_pr_open: false,
                    auto_assign_labels: vec!["done".to_string()],
                })),
            ],
            BoardTemplate::Development => vec![
                ("Backlog", None),
                ("To Do", None),
                ("In Progress", Some(ColumnAutomation {
                    auto_move_on_pr_open: false,
                    auto_move_on_pr_merged: false,
                    auto_move_on_issue_closed: false,
                    auto_assign_labels: vec!["in-progress".to_string()],
                })),
                ("Review", Some(ColumnAutomation {
                    auto_move_on_pr_open: true,
                    auto_move_on_pr_merged: false,
                    auto_move_on_issue_closed: false,
                    auto_assign_labels: vec!["review".to_string()],
                })),
                ("Testing", None),
                ("Done", Some(ColumnAutomation {
                    auto_move_on_pr_merged: true,
                    auto_move_on_issue_closed: true,
                    auto_move_on_pr_open: false,
                    auto_assign_labels: vec!["done".to_string()],
                })),
            ],
            BoardTemplate::BugTriage => vec![
                ("New", None),
                ("Triaged", Some(ColumnAutomation {
                    auto_move_on_pr_open: false,
                    auto_move_on_pr_merged: false,
                    auto_move_on_issue_closed: false,
                    auto_assign_labels: vec!["triaged".to_string()],
                })),
                ("In Progress", Some(ColumnAutomation {
                    auto_move_on_pr_open: false,
                    auto_move_on_pr_merged: false,
                    auto_move_on_issue_closed: false,
                    auto_assign_labels: vec!["in-progress".to_string()],
                })),
                ("Testing", None),
                ("Closed", Some(ColumnAutomation {
                    auto_move_on_pr_merged: true,
                    auto_move_on_issue_closed: true,
                    auto_move_on_pr_open: false,
                    auto_assign_labels: vec!["closed".to_string()],
                })),
            ],
            _ => vec![
                ("To Do", None),
                ("In Progress", None),
                ("Done", None),
            ],
        };

        for (position, (name, automation)) in columns.into_iter().enumerate() {
            let automation_json = automation.map(|a| serde_json::to_value(a).unwrap());

            sqlx::query(
                r#"
                INSERT INTO board_columns (board_id, name, position, automation_rules)
                VALUES ($1, $2, $3, $4)
                "#
            )
            .bind(board_id)
            .bind(name)
            .bind(position as i32)
            .bind(automation_json)
            .execute(&mut **tx)
            .await?;
        }

        Ok(())
    }

    async fn create_basic_columns(
        &self,
        board_id: Uuid,
        tx: &mut Transaction<'_, Postgres>,
    ) -> Result<(), BoardError> {
        self.create_template_columns(board_id, BoardTemplate::Basic, tx).await
    }

    async fn get_board(&self, board_id: Uuid) -> Result<ProjectBoard, BoardError> {
        let board = sqlx::query_as::<_, ProjectBoard>(
            "SELECT * FROM project_boards WHERE id = $1"
        )
        .bind(board_id)
        .fetch_optional(&self.db)
        .await?
        .ok_or(BoardError::BoardNotFound)?;

        Ok(board)
    }

    async fn get_column_cards(&self, column_id: Uuid) -> Result<Vec<CardResponse>, BoardError> {
        let cards = sqlx::query_as::<_, BoardCard>(
            "SELECT * FROM board_cards WHERE column_id = $1 ORDER BY position"
        )
        .bind(column_id)
        .fetch_all(&self.db)
        .await?;

        let mut card_responses = Vec::new();

        for card in cards {
            let issue = if let Some(issue_id) = card.issue_id {
                self.get_issue_summary(issue_id).await.ok()
            } else {
                None
            };

            let pull_request = if let Some(pr_id) = card.pull_request_id {
                self.get_pr_summary(pr_id).await.ok()
            } else {
                None
            };

            card_responses.push(CardResponse {
                card,
                issue,
                pull_request,
            });
        }

        Ok(card_responses)
    }

    async fn get_next_card_position(
        &self,
        column_id: Uuid,
        tx: &mut Transaction<'_, Postgres>,
    ) -> Result<i32, BoardError> {
        let max_position: Option<i32> = sqlx::query_scalar(
            "SELECT MAX(position) FROM board_cards WHERE column_id = $1"
        )
        .bind(column_id)
        .fetch_one(&mut **tx)
        .await?;

        Ok(max_position.unwrap_or(-1) + 1)
    }

    async fn get_card(
        &self,
        card_id: Uuid,
        tx: &mut Transaction<'_, Postgres>,
    ) -> Result<BoardCard, BoardError> {
        let card = sqlx::query_as::<_, BoardCard>(
            "SELECT * FROM board_cards WHERE id = $1"
        )
        .bind(card_id)
        .fetch_optional(&mut **tx)
        .await?
        .ok_or(BoardError::CardNotFound)?;

        Ok(card)
    }

    async fn reorder_cards_in_column(
        &self,
        column_id: Uuid,
        card_id: Uuid,
        new_position: i32,
        tx: &mut Transaction<'_, Postgres>,
    ) -> Result<(), BoardError> {
        // Shift other cards to make room
        sqlx::query(
            r#"
            UPDATE board_cards
            SET position = position + 1
            WHERE column_id = $1 AND position >= $2 AND id != $3
            "#
        )
        .bind(column_id)
        .bind(new_position)
        .bind(card_id)
        .execute(&mut **tx)
        .await?;

        Ok(())
    }

    async fn move_card_between_columns(
        &self,
        card_id: Uuid,
        old_column_id: Uuid,
        new_column_id: Uuid,
        new_position: i32,
        tx: &mut Transaction<'_, Postgres>,
    ) -> Result<(), BoardError> {
        // Shift cards in old column to fill gap
        sqlx::query(
            "UPDATE board_cards SET position = position - 1 WHERE column_id = $1 AND position > (SELECT position FROM board_cards WHERE id = $2)"
        )
        .bind(old_column_id)
        .bind(card_id)
        .execute(&mut **tx)
        .await?;

        // Make room in new column
        sqlx::query(
            "UPDATE board_cards SET position = position + 1 WHERE column_id = $1 AND position >= $2"
        )
        .bind(new_column_id)
        .bind(new_position)
        .execute(&mut **tx)
        .await?;

        Ok(())
    }

    async fn get_issue_summary(&self, issue_id: Uuid) -> Result<IssueListItem, BoardError> {
        // Implementation would fetch issue summary
        Ok(IssueListItem {
            id: issue_id,
            number: 1,
            title: "Issue".to_string(),
            state: IssueState::Open,
            issue_type: IssueType::Bug,
            priority: IssuePriority::Medium,
            author_username: "user".to_string(),
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
            comments_count: 0,
            reactions_count: 0,
        })
    }

    async fn get_pr_summary(&self, pr_id: Uuid) -> Result<PullRequestListItem, BoardError> {
        // Implementation would fetch PR summary
        Ok(PullRequestListItem {
            id: pr_id.to_string(),
            number: 1,
            title: "PR".to_string(),
            author: crate::review::UserInfo {
                username: "user".to_string(),
                avatarUrl: None,
            },
            state: "open".to_string(),
            isDraft: false,
            createdAt: chrono::Utc::now(),
            updatedAt: chrono::Utc::now(),
            reviewsCount: 0,
            commentsCount: 0,
            additions: 0,
            deletions: 0,
            labels: Vec::new(),
        })
    }
}

// Placeholder for PR list item from review module
#[derive(Debug, Serialize)]
pub struct PullRequestListItem {
    pub id: String,
    pub number: i32,
    pub title: String,
    pub author: crate::review::UserInfo,
    pub state: String,
    pub isDraft: bool,
    pub createdAt: chrono::DateTime<chrono::Utc>,
    pub updatedAt: chrono::DateTime<chrono::Utc>,
    pub reviewsCount: i32,
    pub commentsCount: i32,
    pub additions: i32,
    pub deletions: i32,
    pub labels: Vec<String>,
}

#[derive(Debug, thiserror::Error)]
pub enum BoardError {
    #[error("Board not found")]
    BoardNotFound,
    #[error("Card not found")]
    CardNotFound,
    #[error("Column not found")]
    ColumnNotFound,
    #[error("Database error: {0}")]
    DatabaseError(#[from] sqlx::Error),
}
```

## 🎯 Milestone Management

### Milestone Service Implementation

```rust
// src/issues/milestones.rs
use sqlx::PgPool;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use crate::issues::*;

#[derive(Debug, Deserialize)]
pub struct CreateMilestoneRequest {
    pub title: String,
    pub description: Option<String>,
    pub due_date: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateMilestoneRequest {
    pub title: Option<String>,
    pub description: Option<String>,
    pub due_date: Option<DateTime<Utc>>,
    pub state: Option<MilestoneState>,
}

#[derive(Debug, Serialize)]
pub struct MilestoneResponse {
    pub milestone: Milestone,
    pub progress_percentage: f64,
    pub recent_issues: Vec<IssueListItem>,
    pub is_overdue: bool,
    pub days_remaining: Option<i32>,
}

pub struct MilestoneService {
    db: PgPool,
}

impl MilestoneService {
    pub fn new(db: PgPool) -> Self {
        Self { db }
    }

    /// Create a new milestone
    pub async fn create_milestone(
        &self,
        repository_id: Uuid,
        user_id: Uuid,
        request: CreateMilestoneRequest,
    ) -> Result<Milestone, MilestoneError> {
        let milestone = sqlx::query_as::<_, Milestone>(
            r#"
            INSERT INTO milestones (repository_id, title, description, due_date)
            VALUES ($1, $2, $3, $4)
            RETURNING *
            "#
        )
        .bind(repository_id)
        .bind(&request.title)
        .bind(&request.description)
        .bind(request.due_date)
        .fetch_one(&self.db)
        .await?;

        Ok(milestone)
    }

    /// Update milestone
    pub async fn update_milestone(
        &self,
        milestone_id: Uuid,
        user_id: Uuid,
        request: UpdateMilestoneRequest,
    ) -> Result<Milestone, MilestoneError> {
        let mut query = sqlx::QueryBuilder::new("UPDATE milestones SET updated_at = NOW()");
        let mut has_updates = false;

        if let Some(title) = &request.title {
            query.push(", title = ");
            query.push_bind(title);
            has_updates = true;
        }

        if let Some(description) = &request.description {
            query.push(", description = ");
            query.push_bind(description);
            has_updates = true;
        }

        if let Some(due_date) = request.due_date {
            query.push(", due_date = ");
            query.push_bind(due_date);
            has_updates = true;
        }

        if let Some(state) = &request.state {
            query.push(", state = ");
            query.push_bind(state);
            has_updates = true;

            if matches!(state, MilestoneState::Closed) {
                query.push(", closed_at = NOW()");
            } else {
                query.push(", closed_at = NULL");
            }
        }

        if !has_updates {
            return self.get_milestone(milestone_id).await;
        }

        query.push(" WHERE id = ");
        query.push_bind(milestone_id);
        query.push(" RETURNING *");

        let milestone = query
            .build_query_as::<Milestone>()
            .fetch_one(&self.db)
            .await?;

        Ok(milestone)
    }

    /// Get milestone with details
    pub async fn get_milestone_details(
        &self,
        milestone_id: Uuid,
    ) -> Result<MilestoneResponse, MilestoneError> {
        let milestone = self.get_milestone(milestone_id).await?;

        // Calculate progress
        let progress_percentage = if milestone.total_issues > 0 {
            (milestone.closed_issues as f64 / milestone.total_issues as f64) * 100.0
        } else {
            0.0
        };

        // Get recent issues
        let recent_issues = self.get_milestone_recent_issues(milestone_id).await?;

        // Check if overdue
        let is_overdue = milestone.due_date
            .map(|due| due < Utc::now() && milestone.state == MilestoneState::Open)
            .unwrap_or(false);

        // Calculate days remaining
        let days_remaining = milestone.due_date.map(|due| {
            let diff = due.signed_duration_since(Utc::now());
            diff.num_days() as i32
        });

        Ok(MilestoneResponse {
            milestone,
            progress_percentage,
            recent_issues,
            is_overdue,
            days_remaining,
        })
    }

    /// Get all milestones for repository
    pub async fn get_repository_milestones(
        &self,
        repository_id: Uuid,
        state_filter: Option<MilestoneState>,
    ) -> Result<Vec<MilestoneResponse>, MilestoneError> {
        let mut query = sqlx::QueryBuilder::new(
            "SELECT * FROM milestones WHERE repository_id = "
        );
        query.push_bind(repository_id);

        if let Some(state) = state_filter {
            query.push(" AND state = ");
            query.push_bind(state);
        }

        query.push(" ORDER BY due_date ASC NULLS LAST, created_at DESC");

        let milestones = query
            .build_query_as::<Milestone>()
            .fetch_all(&self.db)
            .await?;

        let mut responses = Vec::new();
        for milestone in milestones {
            let response = self.get_milestone_details(milestone.id).await?;
            responses.push(response);
        }

        Ok(responses)
    }

    /// Get milestone burndown data
    pub async fn get_milestone_burndown(
        &self,
        milestone_id: Uuid,
    ) -> Result<Vec<BurndownPoint>, MilestoneError> {
        let milestone = self.get_milestone(milestone_id).await?;

        // Get daily issue closure data
        let burndown_data = sqlx::query_as::<_, BurndownPoint>(
            r#"
            WITH RECURSIVE date_series AS (
                SELECT $2::date as date
                UNION ALL
                SELECT date + INTERVAL '1 day'
                FROM date_series
                WHERE date < COALESCE($3::date, CURRENT_DATE)
            ),
            daily_closures AS (
                SELECT
                    DATE(closed_at) as closure_date,
                    COUNT(*) as closed_count
                FROM issues
                WHERE milestone_id = $1 AND closed_at IS NOT NULL
                GROUP BY DATE(closed_at)
            )
            SELECT
                ds.date,
                COALESCE(SUM(dc.closed_count) OVER (ORDER BY ds.date), 0) as cumulative_closed,
                $4 - COALESCE(SUM(dc.closed_count) OVER (ORDER BY ds.date), 0) as remaining
            FROM date_series ds
            LEFT JOIN daily_closures dc ON ds.date = dc.closure_date
            ORDER BY ds.date
            "#
        )
        .bind(milestone_id)
        .bind(milestone.created_at.date_naive())
        .bind(milestone.due_date.map(|d| d.date_naive()))
        .bind(milestone.total_issues)
        .fetch_all(&self.db)
        .await?;

        Ok(burndown_data)
    }

    async fn get_milestone(&self, milestone_id: Uuid) -> Result<Milestone, MilestoneError> {
        let milestone = sqlx::query_as::<_, Milestone>(
            "SELECT * FROM milestones WHERE id = $1"
        )
        .bind(milestone_id)
        .fetch_optional(&self.db)
        .await?
        .ok_or(MilestoneError::MilestoneNotFound)?;

        Ok(milestone)
    }

    async fn get_milestone_recent_issues(
        &self,
        milestone_id: Uuid,
    ) -> Result<Vec<IssueListItem>, MilestoneError> {
        let issues = sqlx::query_as::<_, IssueListItem>(
            r#"
            SELECT
                i.id, i.number, i.title, i.state, i.issue_type, i.priority,
                u.username as author_username, i.created_at, i.updated_at,
                i.comments_count, i.reactions_count
            FROM issues i
            JOIN users u ON i.author_id = u.id
            WHERE i.milestone_id = $1
            ORDER BY i.updated_at DESC
            LIMIT 10
            "#
        )
        .bind(milestone_id)
        .fetch_all(&self.db)
        .await?;

        Ok(issues)
    }
}

#[derive(Debug, Serialize, sqlx::FromRow)]
pub struct BurndownPoint {
    pub date: chrono::NaiveDate,
    pub cumulative_closed: i64,
    pub remaining: i64,
}

#[derive(Debug, thiserror::Error)]
pub enum MilestoneError {
    #[error("Milestone not found")]
    MilestoneNotFound,
    #[error("Database error: {0}")]
    DatabaseError(#[from] sqlx::Error),
}
```

## 🎨 Angular Issue Management Components

### Issue List Component

```typescript
// components/issue-list/issue-list.component.ts
import { Component, OnInit, Input } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Observable, BehaviorSubject, combineLatest } from 'rxjs';
import { map, startWith, debounceTime, distinctUntilChanged } from 'rxjs/operators';

export interface IssueListItem {
  id: string;
  number: number;
  title: string;
  state: 'open' | 'closed' | 'resolved';
  type: 'bug' | 'feature' | 'enhancement' | 'task';
  priority: 'critical' | 'high' | 'medium' | 'low';
  author: {
    username: string;
    avatarUrl?: string;
  };
  assignee?: {
    username: string;
    avatarUrl?: string;
  };
  labels: Array<{
    name: string;
    color: string;
  }>;
  milestone?: {
    title: string;
    dueDate?: Date;
  };
  createdAt: Date;
  updatedAt: Date;
  commentsCount: number;
  reactionsCount: number;
}

@Component({
  selector: 'app-issue-list',
  template: `
    <div class="issue-list">
      <!-- Filters -->
      <div class="filters">
        <mat-form-field appearance="outline">
          <mat-label>Search issues</mat-label>
          <input matInput [formControl]="searchControl" placeholder="Search by title, author, or label">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>State</mat-label>
          <mat-select [formControl]="stateControl" multiple>
            <mat-option value="open">Open</mat-option>
            <mat-option value="closed">Closed</mat-option>
            <mat-option value="resolved">Resolved</mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Type</mat-label>
          <mat-select [formControl]="typeControl" multiple>
            <mat-option value="bug">Bug</mat-option>
            <mat-option value="feature">Feature</mat-option>
            <mat-option value="enhancement">Enhancement</mat-option>
            <mat-option value="task">Task</mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Priority</mat-label>
          <mat-select [formControl]="priorityControl" multiple>
            <mat-option value="critical">Critical</mat-option>
            <mat-option value="high">High</mat-option>
            <mat-option value="medium">Medium</mat-option>
            <mat-option value="low">Low</mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Sort by</mat-label>
          <mat-select [formControl]="sortControl">
            <mat-option value="created">Recently created</mat-option>
            <mat-option value="updated">Recently updated</mat-option>
            <mat-option value="comments">Most commented</mat-option>
            <mat-option value="reactions">Most reactions</mat-option>
          </mat-select>
        </mat-form-field>

        <button mat-raised-button color="primary" (click)="createIssue()">
          <mat-icon>add</mat-icon>
          New Issue
        </button>
      </div>

      <!-- Issue List -->
      <div class="issues" *ngIf="filteredIssues$ | async as issues">
        <mat-card *ngFor="let issue of issues" class="issue-card" (click)="openIssue(issue)">
          <mat-card-header>
            <div mat-card-avatar>
              <img [src]="issue.author.avatarUrl || '/assets/default-avatar.png'"
                   [alt]="issue.author.username">
            </div>

            <mat-card-title>
              <div class="issue-title">
                <mat-icon [class]="'type-' + issue.type">{{ getTypeIcon(issue.type) }}</mat-icon>
                <span class="issue-number">#{{ issue.number }}</span>
                <span class="title">{{ issue.title }}</span>
              </div>
            </mat-card-title>

            <mat-card-subtitle>
              <div class="issue-meta">
                <span class="author">by {{ issue.author.username }}</span>
                <span class="date">{{ issue.createdAt | timeAgo }}</span>
                <mat-chip [class]="'priority-' + issue.priority">{{ issue.priority }}</mat-chip>
              </div>
            </mat-card-subtitle>
          </mat-card-header>

          <mat-card-content>
            <div class="issue-details">
              <!-- Labels -->
              <mat-chip-list *ngIf="issue.labels.length > 0">
                <mat-chip *ngFor="let label of issue.labels"
                         [style.background-color]="label.color">
                  {{ label.name }}
                </mat-chip>
              </mat-chip-list>

              <!-- Milestone -->
              <div *ngIf="issue.milestone" class="milestone">
                <mat-icon>flag</mat-icon>
                <span>{{ issue.milestone.title }}</span>
                <span *ngIf="issue.milestone.dueDate" class="due-date">
                  due {{ issue.milestone.dueDate | date:'shortDate' }}
                </span>
              </div>

              <!-- Assignee -->
              <div *ngIf="issue.assignee" class="assignee">
                <img [src]="issue.assignee.avatarUrl || '/assets/default-avatar.png'"
                     [alt]="issue.assignee.username" class="avatar-small">
                <span>assigned to {{ issue.assignee.username }}</span>
              </div>
            </div>
          </mat-card-content>

          <mat-card-actions align="end">
            <div class="issue-stats">
              <div class="stat" *ngIf="issue.commentsCount > 0">
                <mat-icon>comment</mat-icon>
                <span>{{ issue.commentsCount }}</span>
              </div>
              <div class="stat" *ngIf="issue.reactionsCount > 0">
                <mat-icon>thumb_up</mat-icon>
                <span>{{ issue.reactionsCount }}</span>
              </div>
            </div>

            <div class="issue-state">
              <mat-icon [class]="'state-' + issue.state">
                {{ getStateIcon(issue.state) }}
              </mat-icon>
            </div>
          </mat-card-actions>
        </mat-card>
      </div>

      <!-- Empty State -->
      <div *ngIf="(filteredIssues$ | async)?.length === 0" class="no-issues">
        <mat-icon>bug_report</mat-icon>
        <h3>No issues found</h3>
        <p>Try adjusting your filters or create a new issue.</p>
        <button mat-raised-button color="primary" (click)="createIssue()">
          Create Issue
        </button>
      </div>
    </div>
  `,
  styleUrls: ['./issue-list.component.scss']
})
export class IssueListComponent implements OnInit {
  @Input() repositoryId!: string;

  searchControl = new FormControl('');
  stateControl = new FormControl(['open']);
  typeControl = new FormControl([]);
  priorityControl = new FormControl([]);
  sortControl = new FormControl('updated');

  issues$ = new BehaviorSubject<IssueListItem[]>([]);

  filteredIssues$ = combineLatest([
    this.issues$,
    this.searchControl.valueChanges.pipe(
      startWith(''),
      debounceTime(300),
      distinctUntilChanged()
    ),
    this.stateControl.valueChanges.pipe(startWith(['open'])),
    this.typeControl.valueChanges.pipe(startWith([])),
    this.priorityControl.valueChanges.pipe(startWith([])),
    this.sortControl.valueChanges.pipe(startWith('updated'))
  ]).pipe(
    map(([issues, search, states, types, priorities, sort]) => {
      let filtered = issues;

      // Apply search filter
      if (search) {
        const searchLower = search.toLowerCase();
        filtered = filtered.filter(issue =>
          issue.title.toLowerCase().includes(searchLower) ||
          issue.author.username.toLowerCase().includes(searchLower) ||
          issue.labels.some(label => label.name.toLowerCase().includes(searchLower))
        );
      }

      // Apply state filter
      if (states && states.length > 0) {
        filtered = filtered.filter(issue => states.includes(issue.state));
      }

      // Apply type filter
      if (types && types.length > 0) {
        filtered = filtered.filter(issue => types.includes(issue.type));
      }

      // Apply priority filter
      if (priorities && priorities.length > 0) {
        filtered = filtered.filter(issue => priorities.includes(issue.priority));
      }

      // Apply sorting
      filtered.sort((a, b) => {
        switch (sort) {
          case 'created':
            return b.createdAt.getTime() - a.createdAt.getTime();
          case 'updated':
            return b.updatedAt.getTime() - a.updatedAt.getTime();
          case 'comments':
            return b.commentsCount - a.commentsCount;
          case 'reactions':
            return b.reactionsCount - a.reactionsCount;
          default:
            return 0;
        }
      });

      return filtered;
    })
  );

  constructor(
    private router: Router,
    private issueService: IssueService
  ) {}

  ngOnInit(): void {
    this.loadIssues();
  }

  private loadIssues(): void {
    this.issueService.getIssues(this.repositoryId)
      .subscribe(issues => this.issues$.next(issues));
  }

  openIssue(issue: IssueListItem): void {
    this.router.navigate(['/repositories', this.repositoryId, 'issues', issue.number]);
  }

  createIssue(): void {
    this.router.navigate(['/repositories', this.repositoryId, 'issues', 'new']);
  }

  getTypeIcon(type: string): string {
    switch (type) {
      case 'bug': return 'bug_report';
      case 'feature': return 'star';
      case 'enhancement': return 'trending_up';
      case 'task': return 'assignment';
      default: return 'help';
    }
  }

  getStateIcon(state: string): string {
    switch (state) {
      case 'open': return 'radio_button_unchecked';
      case 'closed': return 'cancel';
      case 'resolved': return 'check_circle';
      default: return 'help';
    }
  }
}
```

## 🎯 Key Takeaways

### Issue Tracking Benefits

1. **Organized Workflow**: Systematic tracking of bugs, features, and tasks
2. **Team Collaboration**: Clear assignment and communication channels
3. **Project Visibility**: Transparent progress tracking with milestones
4. **Historical Record**: Searchable history of decisions and changes
5. **Agile Integration**: Support for sprints, backlogs, and burndown charts

### Advanced Features Implemented

- **Flexible Issue Types**: Support for bugs, features, tasks, epics, and stories
- **Project Boards**: Kanban-style workflow management with automation
- **Milestone Tracking**: Progress monitoring with burndown charts
- **Issue Linking**: Dependencies, duplicates, and relationships
- **Time Tracking**: Estimation and actual time spent
- **Advanced Search**: Full-text search with multiple filters

### Performance Optimizations

- **Database Indexing**: Optimized queries for issue lists and search
- **Denormalized Counters**: Fast access to comment and reaction counts
- **Pagination**: Handle large numbers of issues efficiently
- **Caching**: Cache frequently accessed data like labels and milestones

### Security Considerations

- **Permission Checks**: Verify user access to repositories and issues
- **Input Validation**: Sanitize all user inputs and comments
- **Rate Limiting**: Prevent spam issue creation and comments
- **Audit Logging**: Track all issue modifications for compliance

Ready to continue with [Module 12: Integrated CI/CD for Hosted Repositories](./module-12-cicd-integration.md)?
