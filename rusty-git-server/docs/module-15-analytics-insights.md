# Module 15: Advanced Analytics & Insights

## 🎯 Learning Objectives

By the end of this module, you will:
- Build comprehensive code quality metrics and technical debt analysis
- Implement machine learning-powered insights and recommendations
- Create advanced dependency analysis and security risk assessment
- Master performance profiling and optimization recommendations
- Build team productivity analytics and developer velocity metrics
- Understand predictive analytics for project health and maintenance

## 📊 Why Advanced Analytics Matter

Modern software development requires data-driven insights:
- **Code Quality**: Identify technical debt and maintainability issues
- **Security Risk**: Proactive vulnerability and risk assessment
- **Team Performance**: Optimize developer productivity and collaboration
- **Project Health**: Predict maintenance needs and potential issues
- **Business Intelligence**: Connect development metrics to business outcomes
- **Continuous Improvement**: Data-driven development process optimization

### Analytics Architecture Overview

```mermaid
graph TB
    subgraph "Data Sources"
        GIT[Git History]
        CODE[Source Code]
        ISSUES[Issues & PRs]
        CI[CI/CD Metrics]
        DEPS[Dependencies]
        SECURITY[Security Scans]
    end
    
    subgraph "Data Processing"
        COLLECTOR[Data Collector]
        PARSER[Code Parser]
        ANALYZER[Static Analyzer]
        ML[ML Pipeline]
    end
    
    subgraph "Analytics Engine"
        QUALITY[Code Quality]
        DEBT[Technical Debt]
        SECURITY_RISK[Security Risk]
        PERFORMANCE[Performance]
        TEAM[Team Analytics]
        PREDICT[Predictive Models]
    end
    
    subgraph "Insights & Reporting"
        DASHBOARD[Analytics Dashboard]
        REPORTS[Automated Reports]
        ALERTS[Smart Alerts]
        RECOMMENDATIONS[AI Recommendations]
    end
    
    subgraph "Machine Learning"
        MODELS[ML Models]
        TRAINING[Model Training]
        INFERENCE[Real-time Inference]
        FEEDBACK[Feedback Loop]
    end
    
    GIT --> COLLECTOR
    CODE --> PARSER
    ISSUES --> COLLECTOR
    CI --> COLLECTOR
    DEPS --> ANALYZER
    SECURITY --> ANALYZER
    
    COLLECTOR --> QUALITY
    PARSER --> DEBT
    ANALYZER --> SECURITY_RISK
    COLLECTOR --> PERFORMANCE
    COLLECTOR --> TEAM
    
    QUALITY --> ML
    DEBT --> ML
    SECURITY_RISK --> ML
    PERFORMANCE --> ML
    TEAM --> ML
    
    ML --> PREDICT
    PREDICT --> DASHBOARD
    QUALITY --> REPORTS
    DEBT --> ALERTS
    SECURITY_RISK --> RECOMMENDATIONS
    
    ML --> MODELS
    MODELS --> TRAINING
    TRAINING --> INFERENCE
    INFERENCE --> FEEDBACK
    FEEDBACK --> MODELS
    
    style COLLECTOR fill:#e8f5e8
    style ML fill:#e1f5fe
    style PREDICT fill:#fff3e0
    style RECOMMENDATIONS fill:#ffebee
```

## 🔍 Code Quality & Technical Debt Analysis

### Advanced Code Metrics System

```rust
// src/analytics/code_quality.rs
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use sqlx::PgPool;
use std::collections::HashMap;
use tree_sitter::{Language, Parser, Query, QueryCursor};

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct CodeQualityMetrics {
    pub id: Uuid,
    pub repository_id: Uuid,
    pub commit_hash: String,
    pub file_path: String,
    pub language: String,
    pub lines_of_code: i32,
    pub cyclomatic_complexity: i32,
    pub cognitive_complexity: i32,
    pub maintainability_index: f64,
    pub technical_debt_minutes: i32,
    pub code_smells: i32,
    pub duplicated_lines: i32,
    pub test_coverage: f64,
    pub analyzed_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct TechnicalDebtItem {
    pub id: Uuid,
    pub repository_id: Uuid,
    pub file_path: String,
    pub line_number: i32,
    pub debt_type: DebtType,
    pub severity: DebtSeverity,
    pub title: String,
    pub description: String,
    pub effort_minutes: i32,
    pub interest_rate: f64, // How much the debt grows over time
    pub created_at: DateTime<Utc>,
    pub resolved_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "debt_type", rename_all = "lowercase")]
pub enum DebtType {
    CodeSmell,
    Duplication,
    ComplexMethod,
    LongClass,
    LongMethod,
    LargeClass,
    DeadCode,
    MissingTests,
    OutdatedDependency,
    SecurityVulnerability,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "debt_severity", rename_all = "lowercase")]
pub enum DebtSeverity {
    Critical,
    High,
    Medium,
    Low,
    Info,
}

pub struct CodeQualityAnalyzer {
    db: PgPool,
    parsers: HashMap<String, Parser>,
}

impl CodeQualityAnalyzer {
    pub fn new(db: PgPool) -> Self {
        let mut parsers = HashMap::new();
        
        // Initialize parsers for different languages
        if let Ok(mut parser) = Parser::new() {
            if parser.set_language(tree_sitter_rust::language()).is_ok() {
                parsers.insert("rust".to_string(), parser);
            }
        }
        
        if let Ok(mut parser) = Parser::new() {
            if parser.set_language(tree_sitter_javascript::language()).is_ok() {
                parsers.insert("javascript".to_string(), parser);
            }
        }
        
        if let Ok(mut parser) = Parser::new() {
            if parser.set_language(tree_sitter_python::language()).is_ok() {
                parsers.insert("python".to_string(), parser);
            }
        }
        
        Self { db, parsers }
    }
    
    /// Analyze code quality for a repository
    pub async fn analyze_repository(
        &mut self,
        repository_id: Uuid,
        commit_hash: String,
        file_paths: Vec<String>,
    ) -> Result<Vec<CodeQualityMetrics>, AnalyticsError> {
        let mut metrics = Vec::new();
        
        for file_path in file_paths {
            if let Ok(file_metrics) = self.analyze_file(
                repository_id,
                &commit_hash,
                &file_path,
            ).await {
                metrics.push(file_metrics);
            }
        }
        
        // Store metrics in database
        self.store_metrics(&metrics).await?;
        
        Ok(metrics)
    }
    
    async fn analyze_file(
        &mut self,
        repository_id: Uuid,
        commit_hash: &str,
        file_path: &str,
    ) -> Result<CodeQualityMetrics, AnalyticsError> {
        let language = self.detect_language(file_path);
        let source_code = self.read_file_content(repository_id, commit_hash, file_path).await?;
        
        let lines_of_code = self.count_lines_of_code(&source_code, &language);
        let cyclomatic_complexity = self.calculate_cyclomatic_complexity(&source_code, &language)?;
        let cognitive_complexity = self.calculate_cognitive_complexity(&source_code, &language)?;
        let maintainability_index = self.calculate_maintainability_index(
            lines_of_code,
            cyclomatic_complexity,
            &source_code,
        );
        let technical_debt_minutes = self.calculate_technical_debt(&source_code, &language)?;
        let code_smells = self.detect_code_smells(&source_code, &language)?;
        let duplicated_lines = self.detect_duplicated_lines(&source_code)?;
        let test_coverage = self.get_test_coverage(repository_id, file_path).await?;
        
        Ok(CodeQualityMetrics {
            id: Uuid::new_v4(),
            repository_id,
            commit_hash: commit_hash.to_string(),
            file_path: file_path.to_string(),
            language,
            lines_of_code,
            cyclomatic_complexity,
            cognitive_complexity,
            maintainability_index,
            technical_debt_minutes,
            code_smells,
            duplicated_lines,
            test_coverage,
            analyzed_at: Utc::now(),
        })
    }
    
    fn detect_language(&self, file_path: &str) -> String {
        match std::path::Path::new(file_path).extension().and_then(|s| s.to_str()) {
            Some("rs") => "rust".to_string(),
            Some("js") | Some("ts") => "javascript".to_string(),
            Some("py") => "python".to_string(),
            Some("java") => "java".to_string(),
            Some("cpp") | Some("cc") | Some("cxx") => "cpp".to_string(),
            Some("c") => "c".to_string(),
            Some("go") => "go".to_string(),
            Some("rb") => "ruby".to_string(),
            Some("php") => "php".to_string(),
            Some("cs") => "csharp".to_string(),
            _ => "unknown".to_string(),
        }
    }
    
    fn count_lines_of_code(&self, source_code: &str, language: &str) -> i32 {
        let lines: Vec<&str> = source_code.lines().collect();
        let mut loc = 0;
        
        for line in lines {
            let trimmed = line.trim();
            if !trimmed.is_empty() && !self.is_comment_line(trimmed, language) {
                loc += 1;
            }
        }
        
        loc
    }
    
    fn is_comment_line(&self, line: &str, language: &str) -> bool {
        match language {
            "rust" | "javascript" | "java" | "cpp" | "c" | "go" | "csharp" => {
                line.starts_with("//") || line.starts_with("/*") || line.starts_with("*")
            }
            "python" | "ruby" => line.starts_with("#"),
            "php" => line.starts_with("//") || line.starts_with("#") || line.starts_with("/*"),
            _ => false,
        }
    }
    
    fn calculate_cyclomatic_complexity(&mut self, source_code: &str, language: &str) -> Result<i32, AnalyticsError> {
        if let Some(parser) = self.parsers.get_mut(language) {
            if let Some(tree) = parser.parse(source_code, None) {
                let query = self.get_complexity_query(language)?;
                let mut cursor = QueryCursor::new();
                let matches = cursor.matches(&query, tree.root_node(), source_code.as_bytes());
                
                let mut complexity = 1; // Base complexity
                for _match in matches {
                    complexity += 1;
                }
                
                return Ok(complexity);
            }
        }
        
        // Fallback: simple keyword counting
        self.calculate_complexity_fallback(source_code, language)
    }
    
    fn calculate_cognitive_complexity(&mut self, source_code: &str, language: &str) -> Result<i32, AnalyticsError> {
        // Cognitive complexity considers nesting and logical operators
        if let Some(parser) = self.parsers.get_mut(language) {
            if let Some(tree) = parser.parse(source_code, None) {
                return Ok(self.calculate_cognitive_complexity_from_tree(tree.root_node(), 0));
            }
        }
        
        // Fallback implementation
        Ok(self.calculate_complexity_fallback(source_code, language)?)
    }
    
    fn calculate_cognitive_complexity_from_tree(&self, node: tree_sitter::Node, nesting_level: i32) -> i32 {
        let mut complexity = 0;
        
        match node.kind() {
            "if_statement" | "while_statement" | "for_statement" | "match_expression" => {
                complexity += 1 + nesting_level;
            }
            "else_clause" | "catch_clause" => {
                complexity += 1;
            }
            "binary_expression" => {
                // Check for logical operators
                if let Some(operator) = node.child_by_field_name("operator") {
                    if matches!(operator.kind(), "&&" | "||") {
                        complexity += 1;
                    }
                }
            }
            _ => {}
        }
        
        // Recursively analyze child nodes
        let mut cursor = node.walk();
        for child in node.children(&mut cursor) {
            let child_nesting = if matches!(node.kind(), "if_statement" | "while_statement" | "for_statement") {
                nesting_level + 1
            } else {
                nesting_level
            };
            complexity += self.calculate_cognitive_complexity_from_tree(child, child_nesting);
        }
        
        complexity
    }
    
    fn calculate_complexity_fallback(&self, source_code: &str, language: &str) -> Result<i32, AnalyticsError> {
        let keywords = match language {
            "rust" => vec!["if", "while", "for", "loop", "match", "&&", "||"],
            "javascript" => vec!["if", "while", "for", "switch", "case", "&&", "||", "?"],
            "python" => vec!["if", "while", "for", "elif", "and", "or"],
            "java" => vec!["if", "while", "for", "switch", "case", "&&", "||", "?"],
            _ => vec!["if", "while", "for", "switch", "case"],
        };
        
        let mut complexity = 1; // Base complexity
        for keyword in keywords {
            complexity += source_code.matches(keyword).count() as i32;
        }
        
        Ok(complexity)
    }
    
    fn calculate_maintainability_index(&self, loc: i32, complexity: i32, source_code: &str) -> f64 {
        // Microsoft's Maintainability Index formula (simplified)
        let halstead_volume = self.calculate_halstead_volume(source_code);
        let mi = 171.0 - 5.2 * (halstead_volume / 1000.0).ln() - 0.23 * complexity as f64 - 16.2 * (loc as f64).ln();
        mi.max(0.0).min(100.0)
    }
    
    fn calculate_halstead_volume(&self, source_code: &str) -> f64 {
        // Simplified Halstead volume calculation
        let operators = source_code.matches(|c: char| "+-*/=<>!&|".contains(c)).count();
        let operands = source_code.split_whitespace().count();
        let vocabulary = operators + operands;
        let length = operators + operands;
        
        if vocabulary > 0 {
            length as f64 * (vocabulary as f64).log2()
        } else {
            0.0
        }
    }
    
    fn calculate_technical_debt(&self, source_code: &str, language: &str) -> Result<i32, AnalyticsError> {
        let mut debt_minutes = 0;
        
        // Long methods (>50 lines)
        let methods = self.count_methods(source_code, language);
        let long_methods = methods.iter().filter(|&&lines| lines > 50).count();
        debt_minutes += long_methods as i32 * 30; // 30 minutes per long method
        
        // High complexity methods
        let high_complexity_methods = methods.iter().filter(|&&complexity| complexity > 10).count();
        debt_minutes += high_complexity_methods as i32 * 45; // 45 minutes per complex method
        
        // Code duplication
        let duplication_percentage = self.calculate_duplication_percentage(source_code);
        debt_minutes += (duplication_percentage * 2.0) as i32; // 2 minutes per % of duplication
        
        Ok(debt_minutes)
    }
    
    fn count_methods(&self, source_code: &str, language: &str) -> Vec<i32> {
        // Simplified method counting - would use AST in real implementation
        let method_keywords = match language {
            "rust" => vec!["fn "],
            "javascript" => vec!["function ", "=> "],
            "python" => vec!["def "],
            "java" => vec!["public ", "private ", "protected "],
            _ => vec!["function"],
        };
        
        let mut methods = Vec::new();
        for keyword in method_keywords {
            let count = source_code.matches(keyword).count();
            methods.extend(vec![20; count]); // Assume 20 lines per method
        }
        
        methods
    }
    
    fn detect_code_smells(&self, source_code: &str, language: &str) -> Result<i32, AnalyticsError> {
        let mut smells = 0;
        
        // Long parameter lists
        smells += source_code.matches("(").filter(|_| {
            // Simplified: count commas in parameter lists
            source_code.matches(",").count() > 5
        }).count() as i32;
        
        // Magic numbers
        let magic_number_regex = regex::Regex::new(r"\b\d{2,}\b").unwrap();
        smells += magic_number_regex.find_iter(source_code).count() as i32;
        
        // TODO comments
        smells += source_code.matches("TODO").count() as i32;
        smells += source_code.matches("FIXME").count() as i32;
        smells += source_code.matches("HACK").count() as i32;
        
        Ok(smells)
    }
    
    fn detect_duplicated_lines(&self, source_code: &str) -> Result<i32, AnalyticsError> {
        let lines: Vec<&str> = source_code.lines()
            .map(|line| line.trim())
            .filter(|line| !line.is_empty())
            .collect();
        
        let mut duplicated = 0;
        let mut seen = std::collections::HashSet::new();
        
        for line in lines {
            if !seen.insert(line) {
                duplicated += 1;
            }
        }
        
        Ok(duplicated)
    }
    
    fn calculate_duplication_percentage(&self, source_code: &str) -> f64 {
        let total_lines = source_code.lines().count();
        if total_lines == 0 {
            return 0.0;
        }
        
        let duplicated_lines = self.detect_duplicated_lines(source_code).unwrap_or(0);
        (duplicated_lines as f64 / total_lines as f64) * 100.0
    }
    
    fn get_complexity_query(&self, language: &str) -> Result<Query, AnalyticsError> {
        let query_string = match language {
            "rust" => r#"
                (if_expression) @if
                (while_expression) @while
                (for_expression) @for
                (loop_expression) @loop
                (match_expression) @match
            "#,
            "javascript" => r#"
                (if_statement) @if
                (while_statement) @while
                (for_statement) @for
                (switch_statement) @switch
            "#,
            _ => return Err(AnalyticsError::UnsupportedLanguage(language.to_string())),
        };
        
        let language_obj = match language {
            "rust" => tree_sitter_rust::language(),
            "javascript" => tree_sitter_javascript::language(),
            _ => return Err(AnalyticsError::UnsupportedLanguage(language.to_string())),
        };
        
        Query::new(language_obj, query_string)
            .map_err(|e| AnalyticsError::QueryError(e.to_string()))
    }
    
    async fn read_file_content(
        &self,
        repository_id: Uuid,
        commit_hash: &str,
        file_path: &str,
    ) -> Result<String, AnalyticsError> {
        // Implementation would read file from Git repository
        // For now, return placeholder
        Ok("// Placeholder file content".to_string())
    }
    
    async fn get_test_coverage(
        &self,
        repository_id: Uuid,
        file_path: &str,
    ) -> Result<f64, AnalyticsError> {
        // Implementation would get test coverage from coverage reports
        Ok(75.0) // Placeholder
    }
    
    async fn store_metrics(&self, metrics: &[CodeQualityMetrics]) -> Result<(), AnalyticsError> {
        for metric in metrics {
            sqlx::query(
                r#"
                INSERT INTO code_quality_metrics (
                    id, repository_id, commit_hash, file_path, language,
                    lines_of_code, cyclomatic_complexity, cognitive_complexity,
                    maintainability_index, technical_debt_minutes, code_smells,
                    duplicated_lines, test_coverage
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                ON CONFLICT (repository_id, commit_hash, file_path) 
                DO UPDATE SET
                    lines_of_code = EXCLUDED.lines_of_code,
                    cyclomatic_complexity = EXCLUDED.cyclomatic_complexity,
                    cognitive_complexity = EXCLUDED.cognitive_complexity,
                    maintainability_index = EXCLUDED.maintainability_index,
                    technical_debt_minutes = EXCLUDED.technical_debt_minutes,
                    code_smells = EXCLUDED.code_smells,
                    duplicated_lines = EXCLUDED.duplicated_lines,
                    test_coverage = EXCLUDED.test_coverage,
                    analyzed_at = NOW()
                "#
            )
            .bind(metric.id)
            .bind(metric.repository_id)
            .bind(&metric.commit_hash)
            .bind(&metric.file_path)
            .bind(&metric.language)
            .bind(metric.lines_of_code)
            .bind(metric.cyclomatic_complexity)
            .bind(metric.cognitive_complexity)
            .bind(metric.maintainability_index)
            .bind(metric.technical_debt_minutes)
            .bind(metric.code_smells)
            .bind(metric.duplicated_lines)
            .bind(metric.test_coverage)
            .execute(&self.db)
            .await?;
        }
        
        Ok(())
    }
}

#[derive(Debug, thiserror::Error)]
pub enum AnalyticsError {
    #[error("Unsupported language: {0}")]
    UnsupportedLanguage(String),
    #[error("Query error: {0}")]
    QueryError(String),
    #[error("Database error: {0}")]
    DatabaseError(#[from] sqlx::Error),
    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
}
```

## 🤖 Machine Learning Insights Engine

### Predictive Analytics System

```rust
// src/analytics/ml_insights.rs
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc, Duration};
use sqlx::PgPool;
use std::collections::HashMap;
use candle_core::{Tensor, Device, DType};
use candle_nn::{Linear, Module, VarBuilder};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MLInsight {
    pub id: Uuid,
    pub repository_id: Uuid,
    pub insight_type: InsightType,
    pub confidence: f64,
    pub title: String,
    pub description: String,
    pub recommendation: String,
    pub impact_score: f64,
    pub evidence: serde_json::Value,
    pub created_at: DateTime<Utc>,
    pub expires_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum InsightType {
    BugPrediction,
    MaintenanceRisk,
    PerformanceBottleneck,
    SecurityVulnerability,
    TeamProductivity,
    CodeQualityTrend,
    DependencyRisk,
    TestCoverageGap,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PredictionModel {
    pub model_type: ModelType,
    pub version: String,
    pub accuracy: f64,
    pub last_trained: DateTime<Utc>,
    pub features: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ModelType {
    BugPrediction,
    ChurnPrediction,
    MaintenanceEffort,
    SecurityRisk,
    PerformanceRegression,
}

pub struct MLInsightsEngine {
    db: PgPool,
    models: HashMap<ModelType, PredictionModel>,
    device: Device,
}

impl MLInsightsEngine {
    pub fn new(db: PgPool) -> Self {
        let device = Device::Cpu; // Use CPU for simplicity, could be GPU

        Self {
            db,
            models: HashMap::new(),
            device,
        }
    }

    /// Generate insights for a repository
    pub async fn generate_insights(
        &self,
        repository_id: Uuid,
    ) -> Result<Vec<MLInsight>, MLError> {
        let mut insights = Vec::new();

        // Bug prediction
        if let Ok(bug_insights) = self.predict_bugs(repository_id).await {
            insights.extend(bug_insights);
        }

        // Maintenance risk assessment
        if let Ok(maintenance_insights) = self.assess_maintenance_risk(repository_id).await {
            insights.extend(maintenance_insights);
        }

        // Performance bottleneck detection
        if let Ok(performance_insights) = self.detect_performance_issues(repository_id).await {
            insights.extend(performance_insights);
        }

        // Security vulnerability prediction
        if let Ok(security_insights) = self.predict_security_risks(repository_id).await {
            insights.extend(security_insights);
        }

        // Store insights
        self.store_insights(&insights).await?;

        Ok(insights)
    }

    async fn predict_bugs(&self, repository_id: Uuid) -> Result<Vec<MLInsight>, MLError> {
        // Collect features for bug prediction
        let features = self.collect_bug_prediction_features(repository_id).await?;

        // Run prediction model
        let bug_probability = self.run_bug_prediction_model(&features)?;

        let mut insights = Vec::new();

        for (file_path, probability) in bug_probability {
            if probability > 0.7 {
                insights.push(MLInsight {
                    id: Uuid::new_v4(),
                    repository_id,
                    insight_type: InsightType::BugPrediction,
                    confidence: probability,
                    title: format!("High bug risk detected in {}", file_path),
                    description: format!(
                        "Machine learning analysis indicates a {:.1}% probability of bugs in this file based on complexity, change frequency, and historical patterns.",
                        probability * 100.0
                    ),
                    recommendation: "Consider adding more unit tests, reducing complexity, or conducting a code review.".to_string(),
                    impact_score: probability * 10.0,
                    evidence: serde_json::json!({
                        "file_path": file_path,
                        "probability": probability,
                        "factors": ["high_complexity", "frequent_changes", "low_test_coverage"]
                    }),
                    created_at: Utc::now(),
                    expires_at: Some(Utc::now() + Duration::days(30)),
                });
            }
        }

        Ok(insights)
    }

    async fn assess_maintenance_risk(&self, repository_id: Uuid) -> Result<Vec<MLInsight>, MLError> {
        let maintenance_data = self.collect_maintenance_data(repository_id).await?;

        let risk_score = self.calculate_maintenance_risk(&maintenance_data)?;

        let mut insights = Vec::new();

        if risk_score > 0.6 {
            insights.push(MLInsight {
                id: Uuid::new_v4(),
                repository_id,
                insight_type: InsightType::MaintenanceRisk,
                confidence: 0.85,
                title: "High maintenance burden predicted".to_string(),
                description: format!(
                    "Analysis suggests this repository will require {:.0}% more maintenance effort than average due to technical debt accumulation.",
                    (risk_score - 0.5) * 200.0
                ),
                recommendation: "Focus on refactoring high-complexity modules and improving test coverage.".to_string(),
                impact_score: risk_score * 10.0,
                evidence: serde_json::json!({
                    "risk_score": risk_score,
                    "technical_debt_hours": maintenance_data.technical_debt_hours,
                    "code_churn": maintenance_data.code_churn,
                    "test_coverage": maintenance_data.test_coverage
                }),
                created_at: Utc::now(),
                expires_at: Some(Utc::now() + Duration::days(90)),
            });
        }

        Ok(insights)
    }

    async fn detect_performance_issues(&self, repository_id: Uuid) -> Result<Vec<MLInsight>, MLError> {
        let performance_data = self.collect_performance_data(repository_id).await?;

        let bottlenecks = self.identify_performance_bottlenecks(&performance_data)?;

        let mut insights = Vec::new();

        for bottleneck in bottlenecks {
            insights.push(MLInsight {
                id: Uuid::new_v4(),
                repository_id,
                insight_type: InsightType::PerformanceBottleneck,
                confidence: bottleneck.confidence,
                title: format!("Performance bottleneck detected: {}", bottleneck.location),
                description: bottleneck.description,
                recommendation: bottleneck.recommendation,
                impact_score: bottleneck.impact_score,
                evidence: serde_json::json!({
                    "location": bottleneck.location,
                    "metric": bottleneck.metric,
                    "threshold_exceeded": bottleneck.threshold_exceeded
                }),
                created_at: Utc::now(),
                expires_at: Some(Utc::now() + Duration::days(14)),
            });
        }

        Ok(insights)
    }

    async fn predict_security_risks(&self, repository_id: Uuid) -> Result<Vec<MLInsight>, MLError> {
        let security_features = self.collect_security_features(repository_id).await?;

        let risk_predictions = self.run_security_risk_model(&security_features)?;

        let mut insights = Vec::new();

        for prediction in risk_predictions {
            if prediction.risk_score > 0.5 {
                insights.push(MLInsight {
                    id: Uuid::new_v4(),
                    repository_id,
                    insight_type: InsightType::SecurityVulnerability,
                    confidence: prediction.confidence,
                    title: format!("Security risk detected: {}", prediction.risk_type),
                    description: prediction.description,
                    recommendation: prediction.recommendation,
                    impact_score: prediction.risk_score * 10.0,
                    evidence: serde_json::json!({
                        "risk_type": prediction.risk_type,
                        "risk_score": prediction.risk_score,
                        "affected_files": prediction.affected_files
                    }),
                    created_at: Utc::now(),
                    expires_at: Some(Utc::now() + Duration::days(7)),
                });
            }
        }

        Ok(insights)
    }

    fn run_bug_prediction_model(&self, features: &BugPredictionFeatures) -> Result<HashMap<String, f64>, MLError> {
        // Simplified ML model - in practice would use trained neural network
        let mut predictions = HashMap::new();

        for (file_path, file_features) in &features.file_features {
            let mut score = 0.0;

            // Complexity factor
            score += (file_features.cyclomatic_complexity as f64 / 20.0).min(1.0) * 0.3;

            // Change frequency factor
            score += (file_features.change_frequency / 10.0).min(1.0) * 0.25;

            // Test coverage factor (inverse)
            score += (1.0 - file_features.test_coverage / 100.0) * 0.2;

            // Historical bug factor
            score += (file_features.historical_bugs as f64 / 5.0).min(1.0) * 0.25;

            predictions.insert(file_path.clone(), score);
        }

        Ok(predictions)
    }

    fn calculate_maintenance_risk(&self, data: &MaintenanceData) -> Result<f64, MLError> {
        let mut risk_score = 0.0;

        // Technical debt factor
        risk_score += (data.technical_debt_hours as f64 / 1000.0).min(1.0) * 0.4;

        // Code churn factor
        risk_score += (data.code_churn / 50.0).min(1.0) * 0.3;

        // Test coverage factor (inverse)
        risk_score += (1.0 - data.test_coverage / 100.0) * 0.2;

        // Dependency age factor
        risk_score += (data.outdated_dependencies as f64 / 20.0).min(1.0) * 0.1;

        Ok(risk_score)
    }

    async fn collect_bug_prediction_features(&self, repository_id: Uuid) -> Result<BugPredictionFeatures, MLError> {
        // Implementation would collect features from database
        Ok(BugPredictionFeatures {
            file_features: HashMap::new(),
        })
    }

    async fn collect_maintenance_data(&self, repository_id: Uuid) -> Result<MaintenanceData, MLError> {
        // Implementation would collect maintenance metrics
        Ok(MaintenanceData {
            technical_debt_hours: 100,
            code_churn: 25.0,
            test_coverage: 75.0,
            outdated_dependencies: 5,
        })
    }

    async fn collect_performance_data(&self, repository_id: Uuid) -> Result<PerformanceData, MLError> {
        // Implementation would collect performance metrics
        Ok(PerformanceData {
            build_times: vec![120.0, 135.0, 140.0],
            test_times: vec![45.0, 50.0, 55.0],
            memory_usage: vec![512.0, 520.0, 530.0],
        })
    }

    async fn collect_security_features(&self, repository_id: Uuid) -> Result<SecurityFeatures, MLError> {
        // Implementation would collect security-related features
        Ok(SecurityFeatures {
            dependency_vulnerabilities: 3,
            code_patterns: vec!["sql_injection_risk".to_string()],
            authentication_issues: 1,
        })
    }

    fn identify_performance_bottlenecks(&self, data: &PerformanceData) -> Result<Vec<PerformanceBottleneck>, MLError> {
        let mut bottlenecks = Vec::new();

        // Check build time trend
        if let Some(latest_build_time) = data.build_times.last() {
            if *latest_build_time > 180.0 { // 3 minutes threshold
                bottlenecks.push(PerformanceBottleneck {
                    location: "Build Process".to_string(),
                    description: format!("Build time has increased to {:.1} seconds, exceeding recommended threshold.", latest_build_time),
                    recommendation: "Consider optimizing dependencies, enabling incremental builds, or parallelizing build steps.".to_string(),
                    confidence: 0.9,
                    impact_score: 7.0,
                    metric: "build_time".to_string(),
                    threshold_exceeded: *latest_build_time,
                });
            }
        }

        Ok(bottlenecks)
    }

    fn run_security_risk_model(&self, features: &SecurityFeatures) -> Result<Vec<SecurityRiskPrediction>, MLError> {
        let mut predictions = Vec::new();

        if features.dependency_vulnerabilities > 0 {
            predictions.push(SecurityRiskPrediction {
                risk_type: "Dependency Vulnerabilities".to_string(),
                risk_score: (features.dependency_vulnerabilities as f64 / 10.0).min(1.0),
                confidence: 0.95,
                description: format!("Found {} known vulnerabilities in dependencies.", features.dependency_vulnerabilities),
                recommendation: "Update vulnerable dependencies to their latest secure versions.".to_string(),
                affected_files: vec!["package.json".to_string(), "Cargo.toml".to_string()],
            });
        }

        Ok(predictions)
    }

    async fn store_insights(&self, insights: &[MLInsight]) -> Result<(), MLError> {
        for insight in insights {
            sqlx::query(
                r#"
                INSERT INTO ml_insights (
                    id, repository_id, insight_type, confidence, title, description,
                    recommendation, impact_score, evidence, expires_at
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                "#
            )
            .bind(insight.id)
            .bind(insight.repository_id)
            .bind(&insight.insight_type)
            .bind(insight.confidence)
            .bind(&insight.title)
            .bind(&insight.description)
            .bind(&insight.recommendation)
            .bind(insight.impact_score)
            .bind(&insight.evidence)
            .bind(insight.expires_at)
            .execute(&self.db)
            .await?;
        }

        Ok(())
    }
}

// Supporting data structures
#[derive(Debug)]
pub struct BugPredictionFeatures {
    pub file_features: HashMap<String, FileFeatures>,
}

#[derive(Debug)]
pub struct FileFeatures {
    pub cyclomatic_complexity: i32,
    pub change_frequency: f64,
    pub test_coverage: f64,
    pub historical_bugs: i32,
}

#[derive(Debug)]
pub struct MaintenanceData {
    pub technical_debt_hours: i32,
    pub code_churn: f64,
    pub test_coverage: f64,
    pub outdated_dependencies: i32,
}

#[derive(Debug)]
pub struct PerformanceData {
    pub build_times: Vec<f64>,
    pub test_times: Vec<f64>,
    pub memory_usage: Vec<f64>,
}

#[derive(Debug)]
pub struct SecurityFeatures {
    pub dependency_vulnerabilities: i32,
    pub code_patterns: Vec<String>,
    pub authentication_issues: i32,
}

#[derive(Debug)]
pub struct PerformanceBottleneck {
    pub location: String,
    pub description: String,
    pub recommendation: String,
    pub confidence: f64,
    pub impact_score: f64,
    pub metric: String,
    pub threshold_exceeded: f64,
}

#[derive(Debug)]
pub struct SecurityRiskPrediction {
    pub risk_type: String,
    pub risk_score: f64,
    pub confidence: f64,
    pub description: String,
    pub recommendation: String,
    pub affected_files: Vec<String>,
}

#[derive(Debug, thiserror::Error)]
pub enum MLError {
    #[error("Model not found: {0}")]
    ModelNotFound(String),
    #[error("Prediction error: {0}")]
    PredictionError(String),
    #[error("Feature extraction error: {0}")]
    FeatureExtractionError(String),
    #[error("Database error: {0}")]
    DatabaseError(#[from] sqlx::Error),
}
```

## 👥 Team Productivity Analytics

### Developer Velocity & Performance Metrics

```rust
// src/analytics/team_analytics.rs
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc, Duration};
use sqlx::PgPool;
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct DeveloperMetrics {
    pub id: Uuid,
    pub user_id: Uuid,
    pub repository_id: Uuid,
    pub period_start: DateTime<Utc>,
    pub period_end: DateTime<Utc>,

    // Productivity metrics
    pub commits_count: i32,
    pub lines_added: i32,
    pub lines_deleted: i32,
    pub files_changed: i32,
    pub pull_requests_created: i32,
    pub pull_requests_reviewed: i32,
    pub issues_created: i32,
    pub issues_resolved: i32,

    // Quality metrics
    pub code_review_participation: f64,
    pub average_pr_size: f64,
    pub bug_introduction_rate: f64,
    pub test_coverage_contribution: f64,

    // Collaboration metrics
    pub review_turnaround_hours: f64,
    pub collaboration_score: f64,
    pub mentoring_activities: i32,

    // Velocity metrics
    pub velocity_score: f64,
    pub consistency_score: f64,
    pub impact_score: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TeamProductivityReport {
    pub team_id: Uuid,
    pub period_start: DateTime<Utc>,
    pub period_end: DateTime<Utc>,
    pub team_metrics: TeamMetrics,
    pub individual_metrics: Vec<DeveloperMetrics>,
    pub productivity_trends: Vec<ProductivityTrend>,
    pub collaboration_insights: Vec<CollaborationInsight>,
    pub recommendations: Vec<ProductivityRecommendation>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TeamMetrics {
    pub total_commits: i32,
    pub total_prs: i32,
    pub average_pr_merge_time: f64,
    pub code_review_coverage: f64,
    pub bug_resolution_time: f64,
    pub deployment_frequency: f64,
    pub lead_time: f64,
    pub mttr: f64, // Mean Time To Recovery
    pub change_failure_rate: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProductivityTrend {
    pub metric_name: String,
    pub trend_direction: TrendDirection,
    pub change_percentage: f64,
    pub significance: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TrendDirection {
    Increasing,
    Decreasing,
    Stable,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CollaborationInsight {
    pub insight_type: CollaborationInsightType,
    pub description: String,
    pub affected_users: Vec<Uuid>,
    pub impact_score: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CollaborationInsightType {
    HighCollaboration,
    LowCollaboration,
    KnowledgeSilo,
    ReviewBottleneck,
    MentoringOpportunity,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProductivityRecommendation {
    pub recommendation_type: RecommendationType,
    pub title: String,
    pub description: String,
    pub priority: RecommendationPriority,
    pub estimated_impact: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RecommendationType {
    ProcessImprovement,
    ToolAdoption,
    TrainingNeeded,
    WorkloadBalancing,
    CollaborationEnhancement,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RecommendationPriority {
    High,
    Medium,
    Low,
}

pub struct TeamAnalyticsEngine {
    db: PgPool,
}

impl TeamAnalyticsEngine {
    pub fn new(db: PgPool) -> Self {
        Self { db }
    }

    /// Generate comprehensive team productivity report
    pub async fn generate_team_report(
        &self,
        team_id: Uuid,
        period_days: i32,
    ) -> Result<TeamProductivityReport, TeamAnalyticsError> {
        let period_end = Utc::now();
        let period_start = period_end - Duration::days(period_days as i64);

        // Get team members
        let team_members = self.get_team_members(team_id).await?;

        // Calculate individual metrics
        let mut individual_metrics = Vec::new();
        for member_id in &team_members {
            let metrics = self.calculate_developer_metrics(
                *member_id,
                team_id,
                period_start,
                period_end,
            ).await?;
            individual_metrics.push(metrics);
        }

        // Calculate team-level metrics
        let team_metrics = self.calculate_team_metrics(&individual_metrics, period_start, period_end).await?;

        // Analyze productivity trends
        let productivity_trends = self.analyze_productivity_trends(team_id, period_start, period_end).await?;

        // Generate collaboration insights
        let collaboration_insights = self.analyze_collaboration_patterns(&individual_metrics).await?;

        // Generate recommendations
        let recommendations = self.generate_recommendations(&team_metrics, &individual_metrics, &collaboration_insights).await?;

        Ok(TeamProductivityReport {
            team_id,
            period_start,
            period_end,
            team_metrics,
            individual_metrics,
            productivity_trends,
            collaboration_insights,
            recommendations,
        })
    }

    async fn calculate_developer_metrics(
        &self,
        user_id: Uuid,
        repository_id: Uuid,
        period_start: DateTime<Utc>,
        period_end: DateTime<Utc>,
    ) -> Result<DeveloperMetrics, TeamAnalyticsError> {
        // Get commit statistics
        let commit_stats = self.get_commit_statistics(user_id, repository_id, period_start, period_end).await?;

        // Get pull request statistics
        let pr_stats = self.get_pr_statistics(user_id, repository_id, period_start, period_end).await?;

        // Get issue statistics
        let issue_stats = self.get_issue_statistics(user_id, repository_id, period_start, period_end).await?;

        // Calculate quality metrics
        let quality_metrics = self.calculate_quality_metrics(user_id, repository_id, period_start, period_end).await?;

        // Calculate collaboration metrics
        let collaboration_metrics = self.calculate_collaboration_metrics(user_id, repository_id, period_start, period_end).await?;

        // Calculate velocity and impact scores
        let velocity_score = self.calculate_velocity_score(&commit_stats, &pr_stats);
        let consistency_score = self.calculate_consistency_score(user_id, repository_id, period_start, period_end).await?;
        let impact_score = self.calculate_impact_score(&commit_stats, &quality_metrics);

        Ok(DeveloperMetrics {
            id: Uuid::new_v4(),
            user_id,
            repository_id,
            period_start,
            period_end,
            commits_count: commit_stats.count,
            lines_added: commit_stats.lines_added,
            lines_deleted: commit_stats.lines_deleted,
            files_changed: commit_stats.files_changed,
            pull_requests_created: pr_stats.created,
            pull_requests_reviewed: pr_stats.reviewed,
            issues_created: issue_stats.created,
            issues_resolved: issue_stats.resolved,
            code_review_participation: quality_metrics.review_participation,
            average_pr_size: pr_stats.average_size,
            bug_introduction_rate: quality_metrics.bug_rate,
            test_coverage_contribution: quality_metrics.test_coverage,
            review_turnaround_hours: collaboration_metrics.review_turnaround,
            collaboration_score: collaboration_metrics.collaboration_score,
            mentoring_activities: collaboration_metrics.mentoring_count,
            velocity_score,
            consistency_score,
            impact_score,
        })
    }

    async fn calculate_team_metrics(
        &self,
        individual_metrics: &[DeveloperMetrics],
        period_start: DateTime<Utc>,
        period_end: DateTime<Utc>,
    ) -> Result<TeamMetrics, TeamAnalyticsError> {
        let total_commits: i32 = individual_metrics.iter().map(|m| m.commits_count).sum();
        let total_prs: i32 = individual_metrics.iter().map(|m| m.pull_requests_created).sum();

        let average_pr_merge_time = self.calculate_average_pr_merge_time(period_start, period_end).await?;
        let code_review_coverage = self.calculate_code_review_coverage(period_start, period_end).await?;
        let bug_resolution_time = self.calculate_bug_resolution_time(period_start, period_end).await?;

        // DORA metrics
        let deployment_frequency = self.calculate_deployment_frequency(period_start, period_end).await?;
        let lead_time = self.calculate_lead_time(period_start, period_end).await?;
        let mttr = self.calculate_mttr(period_start, period_end).await?;
        let change_failure_rate = self.calculate_change_failure_rate(period_start, period_end).await?;

        Ok(TeamMetrics {
            total_commits,
            total_prs,
            average_pr_merge_time,
            code_review_coverage,
            bug_resolution_time,
            deployment_frequency,
            lead_time,
            mttr,
            change_failure_rate,
        })
    }

    async fn analyze_collaboration_patterns(
        &self,
        individual_metrics: &[DeveloperMetrics],
    ) -> Result<Vec<CollaborationInsight>, TeamAnalyticsError> {
        let mut insights = Vec::new();

        // Identify knowledge silos
        let knowledge_silos = self.identify_knowledge_silos(individual_metrics).await?;
        for silo in knowledge_silos {
            insights.push(CollaborationInsight {
                insight_type: CollaborationInsightType::KnowledgeSilo,
                description: format!("Knowledge silo detected: {} has exclusive expertise in certain areas", silo.expert_name),
                affected_users: silo.affected_users,
                impact_score: silo.risk_score,
            });
        }

        // Identify review bottlenecks
        let review_bottlenecks = self.identify_review_bottlenecks(individual_metrics).await?;
        for bottleneck in review_bottlenecks {
            insights.push(CollaborationInsight {
                insight_type: CollaborationInsightType::ReviewBottleneck,
                description: format!("Review bottleneck: {} is overwhelmed with review requests", bottleneck.reviewer_name),
                affected_users: vec![bottleneck.reviewer_id],
                impact_score: bottleneck.impact_score,
            });
        }

        // Identify mentoring opportunities
        let mentoring_opportunities = self.identify_mentoring_opportunities(individual_metrics).await?;
        for opportunity in mentoring_opportunities {
            insights.push(CollaborationInsight {
                insight_type: CollaborationInsightType::MentoringOpportunity,
                description: format!("Mentoring opportunity: {} could benefit from guidance on {}", opportunity.mentee_name, opportunity.area),
                affected_users: vec![opportunity.mentor_id, opportunity.mentee_id],
                impact_score: opportunity.potential_impact,
            });
        }

        Ok(insights)
    }

    async fn generate_recommendations(
        &self,
        team_metrics: &TeamMetrics,
        individual_metrics: &[DeveloperMetrics],
        collaboration_insights: &[CollaborationInsight],
    ) -> Result<Vec<ProductivityRecommendation>, TeamAnalyticsError> {
        let mut recommendations = Vec::new();

        // Code review recommendations
        if team_metrics.code_review_coverage < 0.8 {
            recommendations.push(ProductivityRecommendation {
                recommendation_type: RecommendationType::ProcessImprovement,
                title: "Improve Code Review Coverage".to_string(),
                description: format!(
                    "Current code review coverage is {:.1}%. Implement mandatory reviews for all changes to improve code quality.",
                    team_metrics.code_review_coverage * 100.0
                ),
                priority: RecommendationPriority::High,
                estimated_impact: 8.5,
            });
        }

        // Deployment frequency recommendations
        if team_metrics.deployment_frequency < 1.0 {
            recommendations.push(ProductivityRecommendation {
                recommendation_type: RecommendationType::ProcessImprovement,
                title: "Increase Deployment Frequency".to_string(),
                description: "Consider implementing continuous deployment to reduce lead time and improve feedback loops.".to_string(),
                priority: RecommendationPriority::Medium,
                estimated_impact: 7.0,
            });
        }

        // Workload balancing recommendations
        let workload_variance = self.calculate_workload_variance(individual_metrics);
        if workload_variance > 0.3 {
            recommendations.push(ProductivityRecommendation {
                recommendation_type: RecommendationType::WorkloadBalancing,
                title: "Balance Team Workload".to_string(),
                description: "Significant workload imbalance detected. Consider redistributing tasks more evenly across team members.".to_string(),
                priority: RecommendationPriority::Medium,
                estimated_impact: 6.5,
            });
        }

        // Collaboration recommendations based on insights
        for insight in collaboration_insights {
            match insight.insight_type {
                CollaborationInsightType::KnowledgeSilo => {
                    recommendations.push(ProductivityRecommendation {
                        recommendation_type: RecommendationType::CollaborationEnhancement,
                        title: "Address Knowledge Silos".to_string(),
                        description: "Implement knowledge sharing sessions and pair programming to reduce knowledge silos.".to_string(),
                        priority: RecommendationPriority::High,
                        estimated_impact: insight.impact_score,
                    });
                }
                CollaborationInsightType::ReviewBottleneck => {
                    recommendations.push(ProductivityRecommendation {
                        recommendation_type: RecommendationType::WorkloadBalancing,
                        title: "Distribute Review Load".to_string(),
                        description: "Distribute code review responsibilities more evenly to prevent bottlenecks.".to_string(),
                        priority: RecommendationPriority::Medium,
                        estimated_impact: insight.impact_score,
                    });
                }
                _ => {}
            }
        }

        Ok(recommendations)
    }

    // Helper methods for calculations
    fn calculate_velocity_score(&self, commit_stats: &CommitStats, pr_stats: &PrStats) -> f64 {
        let commit_velocity = (commit_stats.count as f64 / 30.0).min(1.0); // Normalize to 30 days
        let pr_velocity = (pr_stats.created as f64 / 10.0).min(1.0); // Normalize to 10 PRs
        let size_factor = (1.0 - (pr_stats.average_size / 500.0).min(1.0)) * 0.5 + 0.5; // Prefer smaller PRs

        (commit_velocity * 0.4 + pr_velocity * 0.4 + size_factor * 0.2) * 10.0
    }

    fn calculate_impact_score(&self, commit_stats: &CommitStats, quality_metrics: &QualityMetrics) -> f64 {
        let code_impact = ((commit_stats.lines_added + commit_stats.lines_deleted) as f64 / 1000.0).min(1.0);
        let quality_factor = (1.0 - quality_metrics.bug_rate) * quality_metrics.test_coverage / 100.0;

        (code_impact * 0.6 + quality_factor * 0.4) * 10.0
    }

    fn calculate_workload_variance(&self, metrics: &[DeveloperMetrics]) -> f64 {
        if metrics.is_empty() {
            return 0.0;
        }

        let velocities: Vec<f64> = metrics.iter().map(|m| m.velocity_score).collect();
        let mean = velocities.iter().sum::<f64>() / velocities.len() as f64;
        let variance = velocities.iter()
            .map(|v| (v - mean).powi(2))
            .sum::<f64>() / velocities.len() as f64;

        variance.sqrt() / mean
    }

    // Placeholder implementations for data collection methods
    async fn get_team_members(&self, team_id: Uuid) -> Result<Vec<Uuid>, TeamAnalyticsError> {
        Ok(vec![Uuid::new_v4(), Uuid::new_v4()]) // Placeholder
    }

    async fn get_commit_statistics(&self, user_id: Uuid, repository_id: Uuid, start: DateTime<Utc>, end: DateTime<Utc>) -> Result<CommitStats, TeamAnalyticsError> {
        Ok(CommitStats { count: 25, lines_added: 1500, lines_deleted: 800, files_changed: 45 })
    }

    async fn get_pr_statistics(&self, user_id: Uuid, repository_id: Uuid, start: DateTime<Utc>, end: DateTime<Utc>) -> Result<PrStats, TeamAnalyticsError> {
        Ok(PrStats { created: 8, reviewed: 12, average_size: 150.0 })
    }

    async fn get_issue_statistics(&self, user_id: Uuid, repository_id: Uuid, start: DateTime<Utc>, end: DateTime<Utc>) -> Result<IssueStats, TeamAnalyticsError> {
        Ok(IssueStats { created: 5, resolved: 7 })
    }

    async fn calculate_quality_metrics(&self, user_id: Uuid, repository_id: Uuid, start: DateTime<Utc>, end: DateTime<Utc>) -> Result<QualityMetrics, TeamAnalyticsError> {
        Ok(QualityMetrics { review_participation: 0.85, bug_rate: 0.05, test_coverage: 78.0 })
    }

    async fn calculate_collaboration_metrics(&self, user_id: Uuid, repository_id: Uuid, start: DateTime<Utc>, end: DateTime<Utc>) -> Result<CollaborationMetrics, TeamAnalyticsError> {
        Ok(CollaborationMetrics { review_turnaround: 4.5, collaboration_score: 7.8, mentoring_count: 3 })
    }

    async fn calculate_consistency_score(&self, user_id: Uuid, repository_id: Uuid, start: DateTime<Utc>, end: DateTime<Utc>) -> Result<f64, TeamAnalyticsError> {
        Ok(8.2) // Placeholder
    }

    // Additional placeholder methods...
    async fn calculate_average_pr_merge_time(&self, start: DateTime<Utc>, end: DateTime<Utc>) -> Result<f64, TeamAnalyticsError> { Ok(24.5) }
    async fn calculate_code_review_coverage(&self, start: DateTime<Utc>, end: DateTime<Utc>) -> Result<f64, TeamAnalyticsError> { Ok(0.82) }
    async fn calculate_bug_resolution_time(&self, start: DateTime<Utc>, end: DateTime<Utc>) -> Result<f64, TeamAnalyticsError> { Ok(48.0) }
    async fn calculate_deployment_frequency(&self, start: DateTime<Utc>, end: DateTime<Utc>) -> Result<f64, TeamAnalyticsError> { Ok(2.5) }
    async fn calculate_lead_time(&self, start: DateTime<Utc>, end: DateTime<Utc>) -> Result<f64, TeamAnalyticsError> { Ok(72.0) }
    async fn calculate_mttr(&self, start: DateTime<Utc>, end: DateTime<Utc>) -> Result<f64, TeamAnalyticsError> { Ok(4.2) }
    async fn calculate_change_failure_rate(&self, start: DateTime<Utc>, end: DateTime<Utc>) -> Result<f64, TeamAnalyticsError> { Ok(0.08) }
    async fn analyze_productivity_trends(&self, team_id: Uuid, start: DateTime<Utc>, end: DateTime<Utc>) -> Result<Vec<ProductivityTrend>, TeamAnalyticsError> { Ok(Vec::new()) }
    async fn identify_knowledge_silos(&self, metrics: &[DeveloperMetrics]) -> Result<Vec<KnowledgeSilo>, TeamAnalyticsError> { Ok(Vec::new()) }
    async fn identify_review_bottlenecks(&self, metrics: &[DeveloperMetrics]) -> Result<Vec<ReviewBottleneck>, TeamAnalyticsError> { Ok(Vec::new()) }
    async fn identify_mentoring_opportunities(&self, metrics: &[DeveloperMetrics]) -> Result<Vec<MentoringOpportunity>, TeamAnalyticsError> { Ok(Vec::new()) }
}

// Supporting data structures
#[derive(Debug)]
pub struct CommitStats { pub count: i32, pub lines_added: i32, pub lines_deleted: i32, pub files_changed: i32 }
#[derive(Debug)]
pub struct PrStats { pub created: i32, pub reviewed: i32, pub average_size: f64 }
#[derive(Debug)]
pub struct IssueStats { pub created: i32, pub resolved: i32 }
#[derive(Debug)]
pub struct QualityMetrics { pub review_participation: f64, pub bug_rate: f64, pub test_coverage: f64 }
#[derive(Debug)]
pub struct CollaborationMetrics { pub review_turnaround: f64, pub collaboration_score: f64, pub mentoring_count: i32 }
#[derive(Debug)]
pub struct KnowledgeSilo { pub expert_name: String, pub affected_users: Vec<Uuid>, pub risk_score: f64 }
#[derive(Debug)]
pub struct ReviewBottleneck { pub reviewer_name: String, pub reviewer_id: Uuid, pub impact_score: f64 }
#[derive(Debug)]
pub struct MentoringOpportunity { pub mentor_id: Uuid, pub mentee_id: Uuid, pub mentee_name: String, pub area: String, pub potential_impact: f64 }

#[derive(Debug, thiserror::Error)]
pub enum TeamAnalyticsError {
    #[error("Team not found")]
    TeamNotFound,
    #[error("Insufficient data")]
    InsufficientData,
    #[error("Database error: {0}")]
    DatabaseError(#[from] sqlx::Error),
}
```

## 🔗 Advanced Dependency Analysis

### Dependency Risk Assessment System

```rust
// src/analytics/dependency_analysis.rs
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc, Duration};
use sqlx::PgPool;
use std::collections::{HashMap, HashSet};
use semver::Version;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyAnalysisReport {
    pub repository_id: Uuid,
    pub analyzed_at: DateTime<Utc>,
    pub dependency_tree: DependencyTree,
    pub risk_assessment: RiskAssessment,
    pub security_vulnerabilities: Vec<DependencyVulnerability>,
    pub license_compliance: LicenseCompliance,
    pub update_recommendations: Vec<UpdateRecommendation>,
    pub dependency_insights: Vec<DependencyInsight>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyTree {
    pub root_dependencies: Vec<Dependency>,
    pub total_dependencies: i32,
    pub direct_dependencies: i32,
    pub transitive_dependencies: i32,
    pub circular_dependencies: Vec<CircularDependency>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Dependency {
    pub name: String,
    pub version: String,
    pub package_type: String,
    pub is_direct: bool,
    pub is_dev_dependency: bool,
    pub license: Option<String>,
    pub last_updated: Option<DateTime<Utc>>,
    pub vulnerabilities: Vec<String>,
    pub children: Vec<Dependency>,
    pub risk_score: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskAssessment {
    pub overall_risk_score: f64,
    pub security_risk: f64,
    pub maintenance_risk: f64,
    pub license_risk: f64,
    pub outdated_dependencies: i32,
    pub high_risk_dependencies: Vec<String>,
    pub risk_factors: Vec<RiskFactor>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskFactor {
    pub factor_type: RiskFactorType,
    pub description: String,
    pub impact_score: f64,
    pub affected_dependencies: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RiskFactorType {
    SecurityVulnerability,
    OutdatedVersion,
    LicenseIncompatibility,
    MaintenanceIssue,
    PerformanceImpact,
    CircularDependency,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyVulnerability {
    pub cve_id: String,
    pub severity: String,
    pub affected_dependency: String,
    pub affected_versions: String,
    pub fixed_version: Option<String>,
    pub description: String,
    pub cvss_score: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LicenseCompliance {
    pub compliant: bool,
    pub license_conflicts: Vec<LicenseConflict>,
    pub unknown_licenses: Vec<String>,
    pub license_distribution: HashMap<String, i32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LicenseConflict {
    pub dependency1: String,
    pub license1: String,
    pub dependency2: String,
    pub license2: String,
    pub conflict_type: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateRecommendation {
    pub dependency_name: String,
    pub current_version: String,
    pub recommended_version: String,
    pub update_type: UpdateType,
    pub priority: UpdatePriority,
    pub benefits: Vec<String>,
    pub risks: Vec<String>,
    pub breaking_changes: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum UpdateType {
    Security,
    Feature,
    BugFix,
    Performance,
    Maintenance,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum UpdatePriority {
    Critical,
    High,
    Medium,
    Low,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyInsight {
    pub insight_type: DependencyInsightType,
    pub title: String,
    pub description: String,
    pub impact_score: f64,
    pub affected_dependencies: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DependencyInsightType {
    HeavyDependency,
    UnusedDependency,
    DuplicateDependency,
    AlternativeAvailable,
    MaintenanceIssue,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CircularDependency {
    pub cycle: Vec<String>,
    pub severity: String,
}

pub struct DependencyAnalyzer {
    db: PgPool,
    vulnerability_db: VulnerabilityDatabase,
}

impl DependencyAnalyzer {
    pub fn new(db: PgPool) -> Self {
        let vulnerability_db = VulnerabilityDatabase::new();

        Self {
            db,
            vulnerability_db,
        }
    }

    /// Perform comprehensive dependency analysis
    pub async fn analyze_dependencies(
        &self,
        repository_id: Uuid,
    ) -> Result<DependencyAnalysisReport, DependencyAnalysisError> {
        // Build dependency tree
        let dependency_tree = self.build_dependency_tree(repository_id).await?;

        // Assess risks
        let risk_assessment = self.assess_risks(&dependency_tree).await?;

        // Check for security vulnerabilities
        let security_vulnerabilities = self.check_vulnerabilities(&dependency_tree).await?;

        // Analyze license compliance
        let license_compliance = self.analyze_license_compliance(&dependency_tree).await?;

        // Generate update recommendations
        let update_recommendations = self.generate_update_recommendations(&dependency_tree, &security_vulnerabilities).await?;

        // Generate insights
        let dependency_insights = self.generate_dependency_insights(&dependency_tree).await?;

        let report = DependencyAnalysisReport {
            repository_id,
            analyzed_at: Utc::now(),
            dependency_tree,
            risk_assessment,
            security_vulnerabilities,
            license_compliance,
            update_recommendations,
            dependency_insights,
        };

        // Store report
        self.store_analysis_report(&report).await?;

        Ok(report)
    }

    async fn build_dependency_tree(&self, repository_id: Uuid) -> Result<DependencyTree, DependencyAnalysisError> {
        // Get package files (package.json, Cargo.toml, requirements.txt, etc.)
        let package_files = self.get_package_files(repository_id).await?;

        let mut root_dependencies = Vec::new();
        let mut all_dependencies = HashSet::new();
        let mut direct_count = 0;

        for package_file in package_files {
            let deps = self.parse_package_file(&package_file).await?;

            for mut dep in deps {
                if dep.is_direct {
                    direct_count += 1;
                }

                // Recursively resolve transitive dependencies
                self.resolve_transitive_dependencies(&mut dep, &mut all_dependencies).await?;
                root_dependencies.push(dep);
            }
        }

        // Detect circular dependencies
        let circular_dependencies = self.detect_circular_dependencies(&root_dependencies);

        Ok(DependencyTree {
            root_dependencies,
            total_dependencies: all_dependencies.len() as i32,
            direct_dependencies: direct_count,
            transitive_dependencies: all_dependencies.len() as i32 - direct_count,
            circular_dependencies,
        })
    }

    async fn assess_risks(&self, tree: &DependencyTree) -> Result<RiskAssessment, DependencyAnalysisError> {
        let mut security_risk = 0.0;
        let mut maintenance_risk = 0.0;
        let mut license_risk = 0.0;
        let mut outdated_count = 0;
        let mut high_risk_deps = Vec::new();
        let mut risk_factors = Vec::new();

        // Analyze each dependency
        for dep in &tree.root_dependencies {
            self.assess_dependency_risk(dep, &mut security_risk, &mut maintenance_risk, &mut license_risk, &mut outdated_count, &mut high_risk_deps, &mut risk_factors).await?;
        }

        // Normalize risk scores
        let total_deps = tree.total_dependencies as f64;
        security_risk /= total_deps;
        maintenance_risk /= total_deps;
        license_risk /= total_deps;

        let overall_risk_score = (security_risk * 0.4 + maintenance_risk * 0.4 + license_risk * 0.2).min(10.0);

        Ok(RiskAssessment {
            overall_risk_score,
            security_risk,
            maintenance_risk,
            license_risk,
            outdated_dependencies: outdated_count,
            high_risk_dependencies: high_risk_deps,
            risk_factors,
        })
    }

    async fn check_vulnerabilities(&self, tree: &DependencyTree) -> Result<Vec<DependencyVulnerability>, DependencyAnalysisError> {
        let mut vulnerabilities = Vec::new();

        for dep in &tree.root_dependencies {
            let dep_vulns = self.check_dependency_vulnerabilities(dep).await?;
            vulnerabilities.extend(dep_vulns);
        }

        Ok(vulnerabilities)
    }

    async fn analyze_license_compliance(&self, tree: &DependencyTree) -> Result<LicenseCompliance, DependencyAnalysisError> {
        let mut license_distribution = HashMap::new();
        let mut unknown_licenses = Vec::new();
        let mut license_conflicts = Vec::new();

        // Collect all licenses
        let mut all_licenses = Vec::new();
        for dep in &tree.root_dependencies {
            self.collect_licenses(dep, &mut all_licenses, &mut license_distribution, &mut unknown_licenses);
        }

        // Check for license conflicts
        license_conflicts = self.detect_license_conflicts(&all_licenses);

        let compliant = license_conflicts.is_empty() && unknown_licenses.is_empty();

        Ok(LicenseCompliance {
            compliant,
            license_conflicts,
            unknown_licenses,
            license_distribution,
        })
    }

    async fn generate_update_recommendations(
        &self,
        tree: &DependencyTree,
        vulnerabilities: &[DependencyVulnerability],
    ) -> Result<Vec<UpdateRecommendation>, DependencyAnalysisError> {
        let mut recommendations = Vec::new();

        // Security updates (highest priority)
        for vuln in vulnerabilities {
            if let Some(fixed_version) = &vuln.fixed_version {
                recommendations.push(UpdateRecommendation {
                    dependency_name: vuln.affected_dependency.clone(),
                    current_version: "current".to_string(), // Would get actual version
                    recommended_version: fixed_version.clone(),
                    update_type: UpdateType::Security,
                    priority: match vuln.severity.as_str() {
                        "CRITICAL" => UpdatePriority::Critical,
                        "HIGH" => UpdatePriority::High,
                        _ => UpdatePriority::Medium,
                    },
                    benefits: vec![format!("Fixes {}", vuln.cve_id)],
                    risks: vec!["Potential breaking changes".to_string()],
                    breaking_changes: false, // Would analyze actual changes
                });
            }
        }

        // Outdated dependencies
        for dep in &tree.root_dependencies {
            if let Some(rec) = self.check_for_updates(dep).await? {
                recommendations.push(rec);
            }
        }

        Ok(recommendations)
    }

    async fn generate_dependency_insights(&self, tree: &DependencyTree) -> Result<Vec<DependencyInsight>, DependencyAnalysisError> {
        let mut insights = Vec::new();

        // Detect heavy dependencies
        let heavy_deps = self.detect_heavy_dependencies(tree);
        for dep_name in heavy_deps {
            insights.push(DependencyInsight {
                insight_type: DependencyInsightType::HeavyDependency,
                title: format!("Heavy dependency detected: {}", dep_name),
                description: format!("{} brings in many transitive dependencies, increasing bundle size.", dep_name),
                impact_score: 7.0,
                affected_dependencies: vec![dep_name],
            });
        }

        // Detect unused dependencies
        let unused_deps = self.detect_unused_dependencies(tree).await?;
        for dep_name in unused_deps {
            insights.push(DependencyInsight {
                insight_type: DependencyInsightType::UnusedDependency,
                title: format!("Unused dependency: {}", dep_name),
                description: format!("{} appears to be unused and can be safely removed.", dep_name),
                impact_score: 5.0,
                affected_dependencies: vec![dep_name],
            });
        }

        // Detect duplicate dependencies
        let duplicate_deps = self.detect_duplicate_dependencies(tree);
        for (dep_name, versions) in duplicate_deps {
            insights.push(DependencyInsight {
                insight_type: DependencyInsightType::DuplicateDependency,
                title: format!("Duplicate dependency: {}", dep_name),
                description: format!("{} is included multiple times with versions: {}", dep_name, versions.join(", ")),
                impact_score: 6.0,
                affected_dependencies: vec![dep_name],
            });
        }

        Ok(insights)
    }

    // Helper methods
    async fn resolve_transitive_dependencies(
        &self,
        dep: &mut Dependency,
        all_deps: &mut HashSet<String>,
    ) -> Result<(), DependencyAnalysisError> {
        all_deps.insert(format!("{}@{}", dep.name, dep.version));

        // In a real implementation, this would fetch transitive dependencies
        // from package registries or lock files
        Ok(())
    }

    fn detect_circular_dependencies(&self, dependencies: &[Dependency]) -> Vec<CircularDependency> {
        // Implementation would use graph algorithms to detect cycles
        Vec::new()
    }

    async fn assess_dependency_risk(
        &self,
        dep: &Dependency,
        security_risk: &mut f64,
        maintenance_risk: &mut f64,
        license_risk: &mut f64,
        outdated_count: &mut i32,
        high_risk_deps: &mut Vec<String>,
        risk_factors: &mut Vec<RiskFactor>,
    ) -> Result<(), DependencyAnalysisError> {
        // Security risk from vulnerabilities
        *security_risk += dep.vulnerabilities.len() as f64;

        // Maintenance risk from age
        if let Some(last_updated) = dep.last_updated {
            let age_days = (Utc::now() - last_updated).num_days();
            if age_days > 365 {
                *maintenance_risk += 1.0;
                *outdated_count += 1;
            }
        }

        // License risk
        if dep.license.is_none() {
            *license_risk += 0.5;
        }

        // High risk threshold
        if dep.risk_score > 7.0 {
            high_risk_deps.push(dep.name.clone());
        }

        Ok(())
    }

    async fn check_dependency_vulnerabilities(&self, dep: &Dependency) -> Result<Vec<DependencyVulnerability>, DependencyAnalysisError> {
        // Implementation would check against vulnerability databases
        Ok(Vec::new())
    }

    fn collect_licenses(
        &self,
        dep: &Dependency,
        all_licenses: &mut Vec<(String, String)>,
        distribution: &mut HashMap<String, i32>,
        unknown: &mut Vec<String>,
    ) {
        if let Some(license) = &dep.license {
            all_licenses.push((dep.name.clone(), license.clone()));
            *distribution.entry(license.clone()).or_insert(0) += 1;
        } else {
            unknown.push(dep.name.clone());
        }

        for child in &dep.children {
            self.collect_licenses(child, all_licenses, distribution, unknown);
        }
    }

    fn detect_license_conflicts(&self, licenses: &[(String, String)]) -> Vec<LicenseConflict> {
        // Implementation would check for incompatible license combinations
        Vec::new()
    }

    async fn check_for_updates(&self, dep: &Dependency) -> Result<Option<UpdateRecommendation>, DependencyAnalysisError> {
        // Implementation would check package registries for newer versions
        Ok(None)
    }

    fn detect_heavy_dependencies(&self, tree: &DependencyTree) -> Vec<String> {
        // Implementation would identify dependencies with many transitive deps
        Vec::new()
    }

    async fn detect_unused_dependencies(&self, tree: &DependencyTree) -> Result<Vec<String>, DependencyAnalysisError> {
        // Implementation would analyze code usage
        Ok(Vec::new())
    }

    fn detect_duplicate_dependencies(&self, tree: &DependencyTree) -> HashMap<String, Vec<String>> {
        // Implementation would find same package with different versions
        HashMap::new()
    }

    async fn get_package_files(&self, repository_id: Uuid) -> Result<Vec<PackageFile>, DependencyAnalysisError> {
        // Implementation would find package files in repository
        Ok(Vec::new())
    }

    async fn parse_package_file(&self, file: &PackageFile) -> Result<Vec<Dependency>, DependencyAnalysisError> {
        // Implementation would parse different package file formats
        Ok(Vec::new())
    }

    async fn store_analysis_report(&self, report: &DependencyAnalysisReport) -> Result<(), DependencyAnalysisError> {
        // Implementation would store report in database
        Ok(())
    }
}

#[derive(Debug)]
pub struct VulnerabilityDatabase {
    // Implementation would connect to CVE databases, GitHub Advisory, etc.
}

impl VulnerabilityDatabase {
    pub fn new() -> Self {
        Self {}
    }
}

#[derive(Debug)]
pub struct PackageFile {
    pub path: String,
    pub content: String,
    pub package_type: String,
}

#[derive(Debug, thiserror::Error)]
pub enum DependencyAnalysisError {
    #[error("Package file not found")]
    PackageFileNotFound,
    #[error("Parse error: {0}")]
    ParseError(String),
    #[error("Network error: {0}")]
    NetworkError(String),
    #[error("Database error: {0}")]
    DatabaseError(#[from] sqlx::Error),
}
```

## 🎯 Key Takeaways

### Advanced Analytics Benefits

1. **Proactive Issue Detection**: ML-powered prediction of bugs and maintenance issues
2. **Data-Driven Decisions**: Evidence-based insights for development process improvements
3. **Team Optimization**: Identify productivity bottlenecks and collaboration opportunities
4. **Risk Management**: Comprehensive security and dependency risk assessment
5. **Continuous Improvement**: Automated recommendations for code quality and team performance

### Advanced Features Implemented

- **Code Quality Metrics**: Cyclomatic complexity, technical debt, maintainability index
- **Machine Learning Insights**: Predictive models for bugs, security risks, and maintenance
- **Team Productivity Analytics**: DORA metrics, velocity tracking, collaboration analysis
- **Dependency Analysis**: Security vulnerabilities, license compliance, update recommendations
- **Performance Profiling**: Bottleneck detection and optimization suggestions
- **Predictive Analytics**: Trend analysis and future risk assessment

### Performance Considerations

- **Async Processing**: Non-blocking analysis and report generation
- **Incremental Analysis**: Only analyze changed files and dependencies
- **Caching Strategy**: Cache analysis results and ML model predictions
- **Batch Processing**: Process multiple repositories efficiently
- **Database Optimization**: Efficient storage and querying of analytics data

### Security & Privacy

- **Data Protection**: Secure handling of code metrics and team data
- **Access Controls**: Role-based access to analytics insights
- **Anonymization**: Option to anonymize individual developer metrics
- **Audit Logging**: Track access to sensitive analytics data
- **Compliance**: GDPR-compliant data processing and storage

Ready to continue with [Module 16: Mobile & Desktop Applications](./module-16-mobile-desktop.md)?
