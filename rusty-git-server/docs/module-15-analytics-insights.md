# Module 15: Advanced Analytics & Insights

## 🎯 Learning Objectives

By the end of this module, you will:
- Build comprehensive code quality metrics and technical debt analysis
- Implement machine learning-powered insights and recommendations
- Create advanced dependency analysis and security risk assessment
- Master performance profiling and optimization recommendations
- Build team productivity analytics and developer velocity metrics
- Understand predictive analytics for project health and maintenance

## 📊 Why Advanced Analytics Matter

Modern software development requires data-driven insights:
- **Code Quality**: Identify technical debt and maintainability issues
- **Security Risk**: Proactive vulnerability and risk assessment
- **Team Performance**: Optimize developer productivity and collaboration
- **Project Health**: Predict maintenance needs and potential issues
- **Business Intelligence**: Connect development metrics to business outcomes
- **Continuous Improvement**: Data-driven development process optimization

### Analytics Architecture Overview

```mermaid
graph TB
    subgraph "Data Sources"
        GIT[Git History]
        CODE[Source Code]
        ISSUES[Issues & PRs]
        CI[CI/CD Metrics]
        DEPS[Dependencies]
        SECURITY[Security Scans]
    end
    
    subgraph "Data Processing"
        COLLECTOR[Data Collector]
        PARSER[Code Parser]
        ANALYZER[Static Analyzer]
        ML[ML Pipeline]
    end
    
    subgraph "Analytics Engine"
        QUALITY[Code Quality]
        DEBT[Technical Debt]
        SECURITY_RISK[Security Risk]
        PERFORMANCE[Performance]
        TEAM[Team Analytics]
        PREDICT[Predictive Models]
    end
    
    subgraph "Insights & Reporting"
        DASHBOARD[Analytics Dashboard]
        REPORTS[Automated Reports]
        ALERTS[Smart Alerts]
        RECOMMENDATIONS[AI Recommendations]
    end
    
    subgraph "Machine Learning"
        MODELS[ML Models]
        TRAINING[Model Training]
        INFERENCE[Real-time Inference]
        FEEDBACK[Feedback Loop]
    end
    
    GIT --> COLLECTOR
    CODE --> PARSER
    ISSUES --> COLLECTOR
    CI --> COLLECTOR
    DEPS --> ANALYZER
    SECURITY --> ANALYZER
    
    COLLECTOR --> QUALITY
    PARSER --> DEBT
    ANALYZER --> SECURITY_RISK
    COLLECTOR --> PERFORMANCE
    COLLECTOR --> TEAM
    
    QUALITY --> ML
    DEBT --> ML
    SECURITY_RISK --> ML
    PERFORMANCE --> ML
    TEAM --> ML
    
    ML --> PREDICT
    PREDICT --> DASHBOARD
    QUALITY --> REPORTS
    DEBT --> ALERTS
    SECURITY_RISK --> RECOMMENDATIONS
    
    ML --> MODELS
    MODELS --> TRAINING
    TRAINING --> INFERENCE
    INFERENCE --> FEEDBACK
    FEEDBACK --> MODELS
    
    style COLLECTOR fill:#e8f5e8
    style ML fill:#e1f5fe
    style PREDICT fill:#fff3e0
    style RECOMMENDATIONS fill:#ffebee
```

## 🔍 Code Quality & Technical Debt Analysis

### Advanced Code Metrics System

```rust
// src/analytics/code_quality.rs
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use sqlx::PgPool;
use std::collections::HashMap;
use tree_sitter::{Language, Parser, Query, QueryCursor};

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct CodeQualityMetrics {
    pub id: Uuid,
    pub repository_id: Uuid,
    pub commit_hash: String,
    pub file_path: String,
    pub language: String,
    pub lines_of_code: i32,
    pub cyclomatic_complexity: i32,
    pub cognitive_complexity: i32,
    pub maintainability_index: f64,
    pub technical_debt_minutes: i32,
    pub code_smells: i32,
    pub duplicated_lines: i32,
    pub test_coverage: f64,
    pub analyzed_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct TechnicalDebtItem {
    pub id: Uuid,
    pub repository_id: Uuid,
    pub file_path: String,
    pub line_number: i32,
    pub debt_type: DebtType,
    pub severity: DebtSeverity,
    pub title: String,
    pub description: String,
    pub effort_minutes: i32,
    pub interest_rate: f64, // How much the debt grows over time
    pub created_at: DateTime<Utc>,
    pub resolved_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "debt_type", rename_all = "lowercase")]
pub enum DebtType {
    CodeSmell,
    Duplication,
    ComplexMethod,
    LongClass,
    LongMethod,
    LargeClass,
    DeadCode,
    MissingTests,
    OutdatedDependency,
    SecurityVulnerability,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "debt_severity", rename_all = "lowercase")]
pub enum DebtSeverity {
    Critical,
    High,
    Medium,
    Low,
    Info,
}

pub struct CodeQualityAnalyzer {
    db: PgPool,
    parsers: HashMap<String, Parser>,
}

impl CodeQualityAnalyzer {
    pub fn new(db: PgPool) -> Self {
        let mut parsers = HashMap::new();
        
        // Initialize parsers for different languages
        if let Ok(mut parser) = Parser::new() {
            if parser.set_language(tree_sitter_rust::language()).is_ok() {
                parsers.insert("rust".to_string(), parser);
            }
        }
        
        if let Ok(mut parser) = Parser::new() {
            if parser.set_language(tree_sitter_javascript::language()).is_ok() {
                parsers.insert("javascript".to_string(), parser);
            }
        }
        
        if let Ok(mut parser) = Parser::new() {
            if parser.set_language(tree_sitter_python::language()).is_ok() {
                parsers.insert("python".to_string(), parser);
            }
        }
        
        Self { db, parsers }
    }
    
    /// Analyze code quality for a repository
    pub async fn analyze_repository(
        &mut self,
        repository_id: Uuid,
        commit_hash: String,
        file_paths: Vec<String>,
    ) -> Result<Vec<CodeQualityMetrics>, AnalyticsError> {
        let mut metrics = Vec::new();
        
        for file_path in file_paths {
            if let Ok(file_metrics) = self.analyze_file(
                repository_id,
                &commit_hash,
                &file_path,
            ).await {
                metrics.push(file_metrics);
            }
        }
        
        // Store metrics in database
        self.store_metrics(&metrics).await?;
        
        Ok(metrics)
    }
    
    async fn analyze_file(
        &mut self,
        repository_id: Uuid,
        commit_hash: &str,
        file_path: &str,
    ) -> Result<CodeQualityMetrics, AnalyticsError> {
        let language = self.detect_language(file_path);
        let source_code = self.read_file_content(repository_id, commit_hash, file_path).await?;
        
        let lines_of_code = self.count_lines_of_code(&source_code, &language);
        let cyclomatic_complexity = self.calculate_cyclomatic_complexity(&source_code, &language)?;
        let cognitive_complexity = self.calculate_cognitive_complexity(&source_code, &language)?;
        let maintainability_index = self.calculate_maintainability_index(
            lines_of_code,
            cyclomatic_complexity,
            &source_code,
        );
        let technical_debt_minutes = self.calculate_technical_debt(&source_code, &language)?;
        let code_smells = self.detect_code_smells(&source_code, &language)?;
        let duplicated_lines = self.detect_duplicated_lines(&source_code)?;
        let test_coverage = self.get_test_coverage(repository_id, file_path).await?;
        
        Ok(CodeQualityMetrics {
            id: Uuid::new_v4(),
            repository_id,
            commit_hash: commit_hash.to_string(),
            file_path: file_path.to_string(),
            language,
            lines_of_code,
            cyclomatic_complexity,
            cognitive_complexity,
            maintainability_index,
            technical_debt_minutes,
            code_smells,
            duplicated_lines,
            test_coverage,
            analyzed_at: Utc::now(),
        })
    }
    
    fn detect_language(&self, file_path: &str) -> String {
        match std::path::Path::new(file_path).extension().and_then(|s| s.to_str()) {
            Some("rs") => "rust".to_string(),
            Some("js") | Some("ts") => "javascript".to_string(),
            Some("py") => "python".to_string(),
            Some("java") => "java".to_string(),
            Some("cpp") | Some("cc") | Some("cxx") => "cpp".to_string(),
            Some("c") => "c".to_string(),
            Some("go") => "go".to_string(),
            Some("rb") => "ruby".to_string(),
            Some("php") => "php".to_string(),
            Some("cs") => "csharp".to_string(),
            _ => "unknown".to_string(),
        }
    }
    
    fn count_lines_of_code(&self, source_code: &str, language: &str) -> i32 {
        let lines: Vec<&str> = source_code.lines().collect();
        let mut loc = 0;
        
        for line in lines {
            let trimmed = line.trim();
            if !trimmed.is_empty() && !self.is_comment_line(trimmed, language) {
                loc += 1;
            }
        }
        
        loc
    }
    
    fn is_comment_line(&self, line: &str, language: &str) -> bool {
        match language {
            "rust" | "javascript" | "java" | "cpp" | "c" | "go" | "csharp" => {
                line.starts_with("//") || line.starts_with("/*") || line.starts_with("*")
            }
            "python" | "ruby" => line.starts_with("#"),
            "php" => line.starts_with("//") || line.starts_with("#") || line.starts_with("/*"),
            _ => false,
        }
    }
    
    fn calculate_cyclomatic_complexity(&mut self, source_code: &str, language: &str) -> Result<i32, AnalyticsError> {
        if let Some(parser) = self.parsers.get_mut(language) {
            if let Some(tree) = parser.parse(source_code, None) {
                let query = self.get_complexity_query(language)?;
                let mut cursor = QueryCursor::new();
                let matches = cursor.matches(&query, tree.root_node(), source_code.as_bytes());
                
                let mut complexity = 1; // Base complexity
                for _match in matches {
                    complexity += 1;
                }
                
                return Ok(complexity);
            }
        }
        
        // Fallback: simple keyword counting
        self.calculate_complexity_fallback(source_code, language)
    }
    
    fn calculate_cognitive_complexity(&mut self, source_code: &str, language: &str) -> Result<i32, AnalyticsError> {
        // Cognitive complexity considers nesting and logical operators
        if let Some(parser) = self.parsers.get_mut(language) {
            if let Some(tree) = parser.parse(source_code, None) {
                return Ok(self.calculate_cognitive_complexity_from_tree(tree.root_node(), 0));
            }
        }
        
        // Fallback implementation
        Ok(self.calculate_complexity_fallback(source_code, language)?)
    }
    
    fn calculate_cognitive_complexity_from_tree(&self, node: tree_sitter::Node, nesting_level: i32) -> i32 {
        let mut complexity = 0;
        
        match node.kind() {
            "if_statement" | "while_statement" | "for_statement" | "match_expression" => {
                complexity += 1 + nesting_level;
            }
            "else_clause" | "catch_clause" => {
                complexity += 1;
            }
            "binary_expression" => {
                // Check for logical operators
                if let Some(operator) = node.child_by_field_name("operator") {
                    if matches!(operator.kind(), "&&" | "||") {
                        complexity += 1;
                    }
                }
            }
            _ => {}
        }
        
        // Recursively analyze child nodes
        let mut cursor = node.walk();
        for child in node.children(&mut cursor) {
            let child_nesting = if matches!(node.kind(), "if_statement" | "while_statement" | "for_statement") {
                nesting_level + 1
            } else {
                nesting_level
            };
            complexity += self.calculate_cognitive_complexity_from_tree(child, child_nesting);
        }
        
        complexity
    }
    
    fn calculate_complexity_fallback(&self, source_code: &str, language: &str) -> Result<i32, AnalyticsError> {
        let keywords = match language {
            "rust" => vec!["if", "while", "for", "loop", "match", "&&", "||"],
            "javascript" => vec!["if", "while", "for", "switch", "case", "&&", "||", "?"],
            "python" => vec!["if", "while", "for", "elif", "and", "or"],
            "java" => vec!["if", "while", "for", "switch", "case", "&&", "||", "?"],
            _ => vec!["if", "while", "for", "switch", "case"],
        };
        
        let mut complexity = 1; // Base complexity
        for keyword in keywords {
            complexity += source_code.matches(keyword).count() as i32;
        }
        
        Ok(complexity)
    }
    
    fn calculate_maintainability_index(&self, loc: i32, complexity: i32, source_code: &str) -> f64 {
        // Microsoft's Maintainability Index formula (simplified)
        let halstead_volume = self.calculate_halstead_volume(source_code);
        let mi = 171.0 - 5.2 * (halstead_volume / 1000.0).ln() - 0.23 * complexity as f64 - 16.2 * (loc as f64).ln();
        mi.max(0.0).min(100.0)
    }
    
    fn calculate_halstead_volume(&self, source_code: &str) -> f64 {
        // Simplified Halstead volume calculation
        let operators = source_code.matches(|c: char| "+-*/=<>!&|".contains(c)).count();
        let operands = source_code.split_whitespace().count();
        let vocabulary = operators + operands;
        let length = operators + operands;
        
        if vocabulary > 0 {
            length as f64 * (vocabulary as f64).log2()
        } else {
            0.0
        }
    }
    
    fn calculate_technical_debt(&self, source_code: &str, language: &str) -> Result<i32, AnalyticsError> {
        let mut debt_minutes = 0;
        
        // Long methods (>50 lines)
        let methods = self.count_methods(source_code, language);
        let long_methods = methods.iter().filter(|&&lines| lines > 50).count();
        debt_minutes += long_methods as i32 * 30; // 30 minutes per long method
        
        // High complexity methods
        let high_complexity_methods = methods.iter().filter(|&&complexity| complexity > 10).count();
        debt_minutes += high_complexity_methods as i32 * 45; // 45 minutes per complex method
        
        // Code duplication
        let duplication_percentage = self.calculate_duplication_percentage(source_code);
        debt_minutes += (duplication_percentage * 2.0) as i32; // 2 minutes per % of duplication
        
        Ok(debt_minutes)
    }
    
    fn count_methods(&self, source_code: &str, language: &str) -> Vec<i32> {
        // Simplified method counting - would use AST in real implementation
        let method_keywords = match language {
            "rust" => vec!["fn "],
            "javascript" => vec!["function ", "=> "],
            "python" => vec!["def "],
            "java" => vec!["public ", "private ", "protected "],
            _ => vec!["function"],
        };
        
        let mut methods = Vec::new();
        for keyword in method_keywords {
            let count = source_code.matches(keyword).count();
            methods.extend(vec![20; count]); // Assume 20 lines per method
        }
        
        methods
    }
    
    fn detect_code_smells(&self, source_code: &str, language: &str) -> Result<i32, AnalyticsError> {
        let mut smells = 0;
        
        // Long parameter lists
        smells += source_code.matches("(").filter(|_| {
            // Simplified: count commas in parameter lists
            source_code.matches(",").count() > 5
        }).count() as i32;
        
        // Magic numbers
        let magic_number_regex = regex::Regex::new(r"\b\d{2,}\b").unwrap();
        smells += magic_number_regex.find_iter(source_code).count() as i32;
        
        // TODO comments
        smells += source_code.matches("TODO").count() as i32;
        smells += source_code.matches("FIXME").count() as i32;
        smells += source_code.matches("HACK").count() as i32;
        
        Ok(smells)
    }
    
    fn detect_duplicated_lines(&self, source_code: &str) -> Result<i32, AnalyticsError> {
        let lines: Vec<&str> = source_code.lines()
            .map(|line| line.trim())
            .filter(|line| !line.is_empty())
            .collect();
        
        let mut duplicated = 0;
        let mut seen = std::collections::HashSet::new();
        
        for line in lines {
            if !seen.insert(line) {
                duplicated += 1;
            }
        }
        
        Ok(duplicated)
    }
    
    fn calculate_duplication_percentage(&self, source_code: &str) -> f64 {
        let total_lines = source_code.lines().count();
        if total_lines == 0 {
            return 0.0;
        }
        
        let duplicated_lines = self.detect_duplicated_lines(source_code).unwrap_or(0);
        (duplicated_lines as f64 / total_lines as f64) * 100.0
    }
    
    fn get_complexity_query(&self, language: &str) -> Result<Query, AnalyticsError> {
        let query_string = match language {
            "rust" => r#"
                (if_expression) @if
                (while_expression) @while
                (for_expression) @for
                (loop_expression) @loop
                (match_expression) @match
            "#,
            "javascript" => r#"
                (if_statement) @if
                (while_statement) @while
                (for_statement) @for
                (switch_statement) @switch
            "#,
            _ => return Err(AnalyticsError::UnsupportedLanguage(language.to_string())),
        };
        
        let language_obj = match language {
            "rust" => tree_sitter_rust::language(),
            "javascript" => tree_sitter_javascript::language(),
            _ => return Err(AnalyticsError::UnsupportedLanguage(language.to_string())),
        };
        
        Query::new(language_obj, query_string)
            .map_err(|e| AnalyticsError::QueryError(e.to_string()))
    }
    
    async fn read_file_content(
        &self,
        repository_id: Uuid,
        commit_hash: &str,
        file_path: &str,
    ) -> Result<String, AnalyticsError> {
        // Implementation would read file from Git repository
        // For now, return placeholder
        Ok("// Placeholder file content".to_string())
    }
    
    async fn get_test_coverage(
        &self,
        repository_id: Uuid,
        file_path: &str,
    ) -> Result<f64, AnalyticsError> {
        // Implementation would get test coverage from coverage reports
        Ok(75.0) // Placeholder
    }
    
    async fn store_metrics(&self, metrics: &[CodeQualityMetrics]) -> Result<(), AnalyticsError> {
        for metric in metrics {
            sqlx::query(
                r#"
                INSERT INTO code_quality_metrics (
                    id, repository_id, commit_hash, file_path, language,
                    lines_of_code, cyclomatic_complexity, cognitive_complexity,
                    maintainability_index, technical_debt_minutes, code_smells,
                    duplicated_lines, test_coverage
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                ON CONFLICT (repository_id, commit_hash, file_path) 
                DO UPDATE SET
                    lines_of_code = EXCLUDED.lines_of_code,
                    cyclomatic_complexity = EXCLUDED.cyclomatic_complexity,
                    cognitive_complexity = EXCLUDED.cognitive_complexity,
                    maintainability_index = EXCLUDED.maintainability_index,
                    technical_debt_minutes = EXCLUDED.technical_debt_minutes,
                    code_smells = EXCLUDED.code_smells,
                    duplicated_lines = EXCLUDED.duplicated_lines,
                    test_coverage = EXCLUDED.test_coverage,
                    analyzed_at = NOW()
                "#
            )
            .bind(metric.id)
            .bind(metric.repository_id)
            .bind(&metric.commit_hash)
            .bind(&metric.file_path)
            .bind(&metric.language)
            .bind(metric.lines_of_code)
            .bind(metric.cyclomatic_complexity)
            .bind(metric.cognitive_complexity)
            .bind(metric.maintainability_index)
            .bind(metric.technical_debt_minutes)
            .bind(metric.code_smells)
            .bind(metric.duplicated_lines)
            .bind(metric.test_coverage)
            .execute(&self.db)
            .await?;
        }
        
        Ok(())
    }
}

#[derive(Debug, thiserror::Error)]
pub enum AnalyticsError {
    #[error("Unsupported language: {0}")]
    UnsupportedLanguage(String),
    #[error("Query error: {0}")]
    QueryError(String),
    #[error("Database error: {0}")]
    DatabaseError(#[from] sqlx::Error),
    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
}
```

Ready to continue with machine learning insights and team productivity analytics?
