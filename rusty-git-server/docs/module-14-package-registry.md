# Module 14: Package Registry & Artifact Management

## 🎯 Learning Objectives

By the end of this module, you will:
- Build a multi-language package registry supporting Docker, NPM, Maven, NuGet, PyPI, and Cargo
- Implement secure package publishing and consumption workflows
- Create package vulnerability scanning and license compliance
- Master package versioning, metadata management, and dependency resolution
- Build package search and discovery features
- Understand package registry federation and mirroring

## 📦 Why Package Registries Matter

Modern development relies heavily on package ecosystems:
- **Dependency Management**: Centralized storage and distribution of libraries
- **Version Control**: Semantic versioning and dependency resolution
- **Security**: Vulnerability scanning and secure package distribution
- **Compliance**: License tracking and policy enforcement
- **Performance**: Caching and CDN distribution for fast downloads
- **Private Packages**: Internal library sharing within organizations

### Package Registry Architecture

```mermaid
graph TB
    subgraph "Package Types"
        DOCKER[Docker Images]
        NPM[NPM Packages]
        MAVEN[Maven Artifacts]
        NUGET[NuGet Packages]
        PYPI[Python Packages]
        CARGO[Rust Crates]
        GENERIC[Generic Files]
    end
    
    subgraph "Registry Core"
        UPLOAD[Upload Service]
        METADATA[Metadata Service]
        STORAGE[Storage Engine]
        INDEX[Search Index]
    end
    
    subgraph "Security & Compliance"
        SCAN[Vulnerability Scanner]
        LICENSE[License Checker]
        POLICY[Policy Engine]
        SIGN[Package Signing]
    end
    
    subgraph "Distribution"
        CDN[Content Delivery Network]
        MIRROR[Registry Mirroring]
        CACHE[Package Cache]
        PROXY[Upstream Proxy]
    end
    
    subgraph "Management"
        VERSIONS[Version Management]
        DEPS[Dependency Resolution]
        STATS[Download Statistics]
        CLEANUP[Cleanup Service]
    end
    
    DOCKER --> UPLOAD
    NPM --> UPLOAD
    MAVEN --> UPLOAD
    NUGET --> UPLOAD
    PYPI --> UPLOAD
    CARGO --> UPLOAD
    GENERIC --> UPLOAD
    
    UPLOAD --> METADATA
    UPLOAD --> STORAGE
    METADATA --> INDEX
    
    UPLOAD --> SCAN
    SCAN --> LICENSE
    LICENSE --> POLICY
    POLICY --> SIGN
    
    STORAGE --> CDN
    CDN --> MIRROR
    MIRROR --> CACHE
    CACHE --> PROXY
    
    METADATA --> VERSIONS
    VERSIONS --> DEPS
    DEPS --> STATS
    STATS --> CLEANUP
    
    style UPLOAD fill:#e8f5e8
    style SCAN fill:#ffebee
    style CDN fill:#e1f5fe
    style VERSIONS fill:#fff3e0
```

## 🏗️ Core Package Registry System

### Universal Package Model

```rust
// src/registry/mod.rs
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Package {
    pub id: Uuid,
    pub repository_id: Uuid,
    pub package_type: PackageType,
    pub namespace: Option<String>,    // Organization/scope for scoped packages
    pub name: String,
    pub description: Option<String>,
    pub homepage: Option<String>,
    pub repository_url: Option<String>,
    pub license: Option<String>,
    pub keywords: Vec<String>,
    pub maintainers: Vec<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub download_count: i64,
    pub latest_version: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "package_type", rename_all = "lowercase")]
pub enum PackageType {
    Docker,
    Npm,
    Maven,
    Nuget,
    Pypi,
    Cargo,
    Generic,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct PackageVersion {
    pub id: Uuid,
    pub package_id: Uuid,
    pub version: String,
    pub description: Option<String>,
    pub changelog: Option<String>,
    pub file_path: String,
    pub file_size: i64,
    pub file_hash: String,
    pub checksum_sha256: String,
    pub metadata: serde_json::Value,
    pub dependencies: serde_json::Value,
    pub is_prerelease: bool,
    pub is_yanked: bool,
    pub published_by: Uuid,
    pub published_at: DateTime<Utc>,
    pub download_count: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct PackageDependency {
    pub id: Uuid,
    pub package_version_id: Uuid,
    pub dependency_name: String,
    pub dependency_namespace: Option<String>,
    pub version_constraint: String,
    pub dependency_type: DependencyType,
    pub is_optional: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "dependency_type", rename_all = "lowercase")]
pub enum DependencyType {
    Runtime,
    Development,
    Peer,
    Optional,
    Build,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct PackageDownload {
    pub id: Uuid,
    pub package_version_id: Uuid,
    pub downloaded_by: Option<Uuid>,
    pub ip_address: String,
    pub user_agent: Option<String>,
    pub downloaded_at: DateTime<Utc>,
}

// Request/Response DTOs
#[derive(Debug, Deserialize)]
pub struct PublishPackageRequest {
    pub package_type: PackageType,
    pub namespace: Option<String>,
    pub name: String,
    pub version: String,
    pub description: Option<String>,
    pub license: Option<String>,
    pub keywords: Vec<String>,
    pub homepage: Option<String>,
    pub repository_url: Option<String>,
    pub dependencies: Vec<PackageDependencyRequest>,
    pub metadata: serde_json::Value,
}

#[derive(Debug, Deserialize)]
pub struct PackageDependencyRequest {
    pub name: String,
    pub namespace: Option<String>,
    pub version_constraint: String,
    pub dependency_type: DependencyType,
    pub is_optional: bool,
}

#[derive(Debug, Serialize)]
pub struct PackageResponse {
    pub package: Package,
    pub versions: Vec<PackageVersionResponse>,
    pub total_downloads: i64,
    pub vulnerability_count: i32,
}

#[derive(Debug, Serialize)]
pub struct PackageVersionResponse {
    pub version: PackageVersion,
    pub dependencies: Vec<PackageDependency>,
    pub vulnerabilities: Vec<PackageVulnerability>,
}

#[derive(Debug, Serialize)]
pub struct PackageVulnerability {
    pub id: String,
    pub severity: String,
    pub title: String,
    pub description: String,
    pub fixed_version: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct PackageSearchResult {
    pub packages: Vec<PackageSearchItem>,
    pub total_count: i64,
    pub page: i32,
    pub per_page: i32,
}

#[derive(Debug, Serialize)]
pub struct PackageSearchItem {
    pub package: Package,
    pub latest_version: Option<PackageVersion>,
    pub relevance_score: f64,
}
```

### Database Schema

```sql
-- Packages table
CREATE TABLE packages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    repository_id UUID NOT NULL REFERENCES repositories(id),
    package_type package_type NOT NULL,
    namespace VARCHAR(255),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    homepage VARCHAR(500),
    repository_url VARCHAR(500),
    license VARCHAR(100),
    keywords TEXT[], -- Array of keywords
    maintainers TEXT[], -- Array of maintainer emails
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    download_count BIGINT DEFAULT 0,
    latest_version VARCHAR(50),
    
    UNIQUE(repository_id, package_type, COALESCE(namespace, ''), name)
);

-- Package versions table
CREATE TABLE package_versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    package_id UUID NOT NULL REFERENCES packages(id) ON DELETE CASCADE,
    version VARCHAR(50) NOT NULL,
    description TEXT,
    changelog TEXT,
    file_path VARCHAR(1000) NOT NULL,
    file_size BIGINT NOT NULL,
    file_hash VARCHAR(128) NOT NULL,
    checksum_sha256 VARCHAR(64) NOT NULL,
    metadata JSONB DEFAULT '{}',
    dependencies JSONB DEFAULT '[]',
    is_prerelease BOOLEAN DEFAULT FALSE,
    is_yanked BOOLEAN DEFAULT FALSE,
    published_by UUID NOT NULL REFERENCES users(id),
    published_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    download_count BIGINT DEFAULT 0,
    
    UNIQUE(package_id, version)
);

-- Package dependencies table
CREATE TABLE package_dependencies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    package_version_id UUID NOT NULL REFERENCES package_versions(id) ON DELETE CASCADE,
    dependency_name VARCHAR(255) NOT NULL,
    dependency_namespace VARCHAR(255),
    version_constraint VARCHAR(100) NOT NULL,
    dependency_type dependency_type NOT NULL DEFAULT 'runtime',
    is_optional BOOLEAN DEFAULT FALSE
);

-- Package downloads table (for analytics)
CREATE TABLE package_downloads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    package_version_id UUID NOT NULL REFERENCES package_versions(id) ON DELETE CASCADE,
    downloaded_by UUID REFERENCES users(id),
    ip_address INET NOT NULL,
    user_agent TEXT,
    downloaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Package vulnerabilities table
CREATE TABLE package_vulnerabilities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    package_version_id UUID NOT NULL REFERENCES package_versions(id) ON DELETE CASCADE,
    vulnerability_id VARCHAR(50) NOT NULL, -- CVE ID or similar
    severity VARCHAR(20) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    fixed_version VARCHAR(50),
    discovered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(package_version_id, vulnerability_id)
);

-- Indexes for performance
CREATE INDEX idx_packages_type_name ON packages(package_type, name);
CREATE INDEX idx_packages_namespace_name ON packages(namespace, name) WHERE namespace IS NOT NULL;
CREATE INDEX idx_packages_repository ON packages(repository_id, package_type);
CREATE INDEX idx_packages_downloads ON packages(download_count DESC);
CREATE INDEX idx_package_versions_package ON package_versions(package_id, version);
CREATE INDEX idx_package_versions_published ON package_versions(published_at DESC);
CREATE INDEX idx_package_dependencies_name ON package_dependencies(dependency_name, dependency_namespace);
CREATE INDEX idx_package_downloads_version ON package_downloads(package_version_id, downloaded_at);
CREATE INDEX idx_package_downloads_stats ON package_downloads(package_version_id, downloaded_at) WHERE downloaded_at > NOW() - INTERVAL '30 days';

-- Full-text search on package names and descriptions
CREATE INDEX idx_packages_search ON packages USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '') || ' ' || array_to_string(keywords, ' ')));
```

## 🔧 Package Registry Service Implementation

### Core Registry Service

```rust
// src/registry/service.rs
use sqlx::PgPool;
use uuid::Uuid;
use tokio::fs;
use tokio::io::AsyncWriteExt;
use sha2::{Sha256, Digest};
use semver::Version;
use crate::registry::*;

pub struct PackageRegistryService {
    db: PgPool,
    storage_path: String,
    vulnerability_scanner: VulnerabilityScanner,
}

impl PackageRegistryService {
    pub fn new(db: PgPool, storage_path: String) -> Self {
        let vulnerability_scanner = VulnerabilityScanner::new(db.clone());

        Self {
            db,
            storage_path,
            vulnerability_scanner,
        }
    }

    /// Publish a new package version
    pub async fn publish_package(
        &self,
        repository_id: Uuid,
        user_id: Uuid,
        request: PublishPackageRequest,
        file_data: Vec<u8>,
    ) -> Result<PackageVersion, RegistryError> {
        // Validate version format
        self.validate_version(&request.version, &request.package_type)?;

        // Calculate file hash and checksum
        let file_hash = format!("{:x}", md5::compute(&file_data));
        let checksum_sha256 = format!("{:x}", Sha256::digest(&file_data));

        let mut tx = self.db.begin().await?;

        // Get or create package
        let package = self.get_or_create_package(
            repository_id,
            &request,
            &mut tx,
        ).await?;

        // Check if version already exists
        if self.version_exists(package.id, &request.version, &mut tx).await? {
            return Err(RegistryError::VersionAlreadyExists);
        }

        // Store file
        let file_path = self.generate_file_path(&package, &request.version, &request.package_type);
        self.store_package_file(&file_path, &file_data).await?;

        // Create package version
        let package_version = sqlx::query_as::<_, PackageVersion>(
            r#"
            INSERT INTO package_versions (
                package_id, version, description, file_path, file_size,
                file_hash, checksum_sha256, metadata, published_by
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            RETURNING *
            "#
        )
        .bind(package.id)
        .bind(&request.version)
        .bind(&request.description)
        .bind(&file_path)
        .bind(file_data.len() as i64)
        .bind(&file_hash)
        .bind(&checksum_sha256)
        .bind(&request.metadata)
        .bind(user_id)
        .fetch_one(&mut *tx)
        .await?;

        // Store dependencies
        for dep_request in &request.dependencies {
            self.create_dependency(package_version.id, dep_request, &mut tx).await?;
        }

        // Update package latest version
        self.update_latest_version(package.id, &request.version, &mut tx).await?;

        tx.commit().await?;

        // Start vulnerability scan in background
        let scanner = self.vulnerability_scanner.clone();
        let version_id = package_version.id;
        tokio::spawn(async move {
            if let Err(e) = scanner.scan_package_version(version_id).await {
                tracing::error!("Vulnerability scan failed: {}", e);
            }
        });

        Ok(package_version)
    }

    /// Download a package version
    pub async fn download_package(
        &self,
        package_type: PackageType,
        namespace: Option<String>,
        name: String,
        version: String,
        user_id: Option<Uuid>,
        ip_address: String,
        user_agent: Option<String>,
    ) -> Result<Vec<u8>, RegistryError> {
        // Find package version
        let package_version = self.find_package_version(
            package_type,
            namespace,
            name,
            version,
        ).await?;

        // Check if version is yanked
        if package_version.is_yanked {
            return Err(RegistryError::VersionYanked);
        }

        // Read file
        let file_data = fs::read(&package_version.file_path).await
            .map_err(|e| RegistryError::FileNotFound(e.to_string()))?;

        // Verify checksum
        let computed_checksum = format!("{:x}", Sha256::digest(&file_data));
        if computed_checksum != package_version.checksum_sha256 {
            return Err(RegistryError::ChecksumMismatch);
        }

        // Record download
        self.record_download(
            package_version.id,
            user_id,
            ip_address,
            user_agent,
        ).await?;

        Ok(file_data)
    }

    /// Search packages
    pub async fn search_packages(
        &self,
        query: String,
        package_type: Option<PackageType>,
        namespace: Option<String>,
        page: i32,
        per_page: i32,
    ) -> Result<PackageSearchResult, RegistryError> {
        let offset = (page - 1) * per_page;

        let mut sql = String::from(
            r#"
            SELECT p.*,
                   ts_rank(to_tsvector('english', p.name || ' ' || COALESCE(p.description, '') || ' ' || array_to_string(p.keywords, ' ')),
                          plainto_tsquery('english', $1)) as relevance_score
            FROM packages p
            WHERE to_tsvector('english', p.name || ' ' || COALESCE(p.description, '') || ' ' || array_to_string(p.keywords, ' ')) @@ plainto_tsquery('english', $1)
            "#
        );

        let mut param_count = 1;

        if package_type.is_some() {
            param_count += 1;
            sql.push_str(&format!(" AND p.package_type = ${}", param_count));
        }

        if namespace.is_some() {
            param_count += 1;
            sql.push_str(&format!(" AND p.namespace = ${}", param_count));
        }

        sql.push_str(" ORDER BY relevance_score DESC, p.download_count DESC");
        sql.push_str(&format!(" LIMIT ${} OFFSET ${}", param_count + 1, param_count + 2));

        let mut query_builder = sqlx::query(&sql).bind(&query);

        if let Some(pkg_type) = package_type {
            query_builder = query_builder.bind(pkg_type);
        }

        if let Some(ns) = namespace {
            query_builder = query_builder.bind(ns);
        }

        query_builder = query_builder.bind(per_page).bind(offset);

        let rows = query_builder.fetch_all(&self.db).await?;

        let mut packages = Vec::new();
        for row in rows {
            let package: Package = Package {
                id: row.get("id"),
                repository_id: row.get("repository_id"),
                package_type: row.get("package_type"),
                namespace: row.get("namespace"),
                name: row.get("name"),
                description: row.get("description"),
                homepage: row.get("homepage"),
                repository_url: row.get("repository_url"),
                license: row.get("license"),
                keywords: row.get("keywords"),
                maintainers: row.get("maintainers"),
                created_at: row.get("created_at"),
                updated_at: row.get("updated_at"),
                download_count: row.get("download_count"),
                latest_version: row.get("latest_version"),
            };

            let relevance_score: f64 = row.get("relevance_score");

            // Get latest version details
            let latest_version = if let Some(version_str) = &package.latest_version {
                self.get_package_version(package.id, version_str).await.ok()
            } else {
                None
            };

            packages.push(PackageSearchItem {
                package,
                latest_version,
                relevance_score,
            });
        }

        // Get total count
        let total_count = self.count_search_results(&query, package_type, namespace).await?;

        Ok(PackageSearchResult {
            packages,
            total_count,
            page,
            per_page,
        })
    }

    /// Get package details with all versions
    pub async fn get_package_details(
        &self,
        package_type: PackageType,
        namespace: Option<String>,
        name: String,
    ) -> Result<PackageResponse, RegistryError> {
        let package = self.find_package(package_type, namespace, name).await?;

        // Get all versions
        let versions = self.get_package_versions(package.id).await?;

        let mut version_responses = Vec::new();
        let mut total_downloads = 0;
        let mut vulnerability_count = 0;

        for version in versions {
            let dependencies = self.get_version_dependencies(version.id).await?;
            let vulnerabilities = self.get_version_vulnerabilities(version.id).await?;

            total_downloads += version.download_count;
            vulnerability_count += vulnerabilities.len() as i32;

            version_responses.push(PackageVersionResponse {
                version,
                dependencies,
                vulnerabilities,
            });
        }

        Ok(PackageResponse {
            package,
            versions: version_responses,
            total_downloads,
            vulnerability_count,
        })
    }

    /// Yank a package version
    pub async fn yank_version(
        &self,
        package_type: PackageType,
        namespace: Option<String>,
        name: String,
        version: String,
        user_id: Uuid,
    ) -> Result<(), RegistryError> {
        let package_version = self.find_package_version(
            package_type,
            namespace,
            name,
            version,
        ).await?;

        // Check permissions (package maintainer or admin)
        self.check_package_permissions(package_version.package_id, user_id).await?;

        sqlx::query("UPDATE package_versions SET is_yanked = true WHERE id = $1")
            .bind(package_version.id)
            .execute(&self.db)
            .await?;

        Ok(())
    }

    // Helper methods
    async fn get_or_create_package(
        &self,
        repository_id: Uuid,
        request: &PublishPackageRequest,
        tx: &mut sqlx::Transaction<'_, sqlx::Postgres>,
    ) -> Result<Package, RegistryError> {
        // Try to find existing package
        if let Ok(package) = self.find_package_in_tx(
            request.package_type.clone(),
            request.namespace.clone(),
            request.name.clone(),
            tx,
        ).await {
            return Ok(package);
        }

        // Create new package
        let package = sqlx::query_as::<_, Package>(
            r#"
            INSERT INTO packages (
                repository_id, package_type, namespace, name, description,
                homepage, repository_url, license, keywords, maintainers
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            RETURNING *
            "#
        )
        .bind(repository_id)
        .bind(&request.package_type)
        .bind(&request.namespace)
        .bind(&request.name)
        .bind(&request.description)
        .bind(&request.homepage)
        .bind(&request.repository_url)
        .bind(&request.license)
        .bind(&request.keywords)
        .bind(&Vec::<String>::new()) // Empty maintainers initially
        .fetch_one(&mut **tx)
        .await?;

        Ok(package)
    }

    fn validate_version(&self, version: &str, package_type: &PackageType) -> Result<(), RegistryError> {
        match package_type {
            PackageType::Npm | PackageType::Cargo => {
                // Use semantic versioning
                Version::parse(version)
                    .map_err(|_| RegistryError::InvalidVersion(version.to_string()))?;
            }
            PackageType::Maven => {
                // Maven version format is more flexible
                if version.is_empty() {
                    return Err(RegistryError::InvalidVersion(version.to_string()));
                }
            }
            PackageType::Docker => {
                // Docker tags have specific rules
                if !self.is_valid_docker_tag(version) {
                    return Err(RegistryError::InvalidVersion(version.to_string()));
                }
            }
            _ => {
                // Basic validation for other types
                if version.is_empty() || version.len() > 50 {
                    return Err(RegistryError::InvalidVersion(version.to_string()));
                }
            }
        }

        Ok(())
    }

    fn is_valid_docker_tag(&self, tag: &str) -> bool {
        // Docker tag validation rules
        if tag.is_empty() || tag.len() > 128 {
            return false;
        }

        // Must not start or end with separator
        if tag.starts_with('.') || tag.starts_with('-') || tag.ends_with('.') || tag.ends_with('-') {
            return false;
        }

        // Only allow alphanumeric, dots, dashes, and underscores
        tag.chars().all(|c| c.is_alphanumeric() || c == '.' || c == '-' || c == '_')
    }

    fn generate_file_path(&self, package: &Package, version: &str, package_type: &PackageType) -> String {
        let namespace_part = package.namespace.as_deref().unwrap_or("_");
        let type_str = format!("{:?}", package_type).to_lowercase();

        format!(
            "{}/{}/{}/{}/{}/{}",
            self.storage_path,
            type_str,
            namespace_part,
            package.name,
            version,
            self.generate_filename(&package.name, version, package_type)
        )
    }

    fn generate_filename(&self, name: &str, version: &str, package_type: &PackageType) -> String {
        match package_type {
            PackageType::Npm => format!("{}-{}.tgz", name, version),
            PackageType::Maven => format!("{}-{}.jar", name, version),
            PackageType::Nuget => format!("{}.{}.nupkg", name, version),
            PackageType::Pypi => format!("{}-{}.tar.gz", name, version),
            PackageType::Cargo => format!("{}-{}.crate", name, version),
            PackageType::Docker => "manifest.json".to_string(),
            PackageType::Generic => format!("{}-{}.zip", name, version),
        }
    }

    async fn store_package_file(&self, file_path: &str, data: &[u8]) -> Result<(), RegistryError> {
        // Create directory if it doesn't exist
        if let Some(parent) = std::path::Path::new(file_path).parent() {
            fs::create_dir_all(parent).await
                .map_err(|e| RegistryError::StorageError(e.to_string()))?;
        }

        // Write file
        let mut file = fs::File::create(file_path).await
            .map_err(|e| RegistryError::StorageError(e.to_string()))?;

        file.write_all(data).await
            .map_err(|e| RegistryError::StorageError(e.to_string()))?;

        Ok(())
    }

    async fn version_exists(
        &self,
        package_id: Uuid,
        version: &str,
        tx: &mut sqlx::Transaction<'_, sqlx::Postgres>,
    ) -> Result<bool, RegistryError> {
        let count: i64 = sqlx::query_scalar(
            "SELECT COUNT(*) FROM package_versions WHERE package_id = $1 AND version = $2"
        )
        .bind(package_id)
        .bind(version)
        .fetch_one(&mut **tx)
        .await?;

        Ok(count > 0)
    }

    async fn create_dependency(
        &self,
        version_id: Uuid,
        dep_request: &PackageDependencyRequest,
        tx: &mut sqlx::Transaction<'_, sqlx::Postgres>,
    ) -> Result<(), RegistryError> {
        sqlx::query(
            r#"
            INSERT INTO package_dependencies (
                package_version_id, dependency_name, dependency_namespace,
                version_constraint, dependency_type, is_optional
            )
            VALUES ($1, $2, $3, $4, $5, $6)
            "#
        )
        .bind(version_id)
        .bind(&dep_request.name)
        .bind(&dep_request.namespace)
        .bind(&dep_request.version_constraint)
        .bind(&dep_request.dependency_type)
        .bind(dep_request.is_optional)
        .execute(&mut **tx)
        .await?;

        Ok(())
    }

    async fn update_latest_version(
        &self,
        package_id: Uuid,
        version: &str,
        tx: &mut sqlx::Transaction<'_, sqlx::Postgres>,
    ) -> Result<(), RegistryError> {
        sqlx::query(
            "UPDATE packages SET latest_version = $1, updated_at = NOW() WHERE id = $2"
        )
        .bind(version)
        .bind(package_id)
        .execute(&mut **tx)
        .await?;

        Ok(())
    }

    // Additional helper methods would be implemented here...
    async fn find_package(&self, package_type: PackageType, namespace: Option<String>, name: String) -> Result<Package, RegistryError> {
        // Implementation would find package by type, namespace, and name
        Err(RegistryError::PackageNotFound)
    }

    async fn find_package_version(&self, package_type: PackageType, namespace: Option<String>, name: String, version: String) -> Result<PackageVersion, RegistryError> {
        // Implementation would find specific package version
        Err(RegistryError::VersionNotFound)
    }

    async fn record_download(&self, version_id: Uuid, user_id: Option<Uuid>, ip_address: String, user_agent: Option<String>) -> Result<(), RegistryError> {
        // Implementation would record download statistics
        Ok(())
    }

    async fn check_package_permissions(&self, package_id: Uuid, user_id: Uuid) -> Result<(), RegistryError> {
        // Implementation would check if user can modify package
        Ok(())
    }

    // Additional methods for getting package data...
    async fn find_package_in_tx(&self, package_type: PackageType, namespace: Option<String>, name: String, tx: &mut sqlx::Transaction<'_, sqlx::Postgres>) -> Result<Package, RegistryError> {
        Err(RegistryError::PackageNotFound)
    }

    async fn get_package_version(&self, package_id: Uuid, version: &str) -> Result<PackageVersion, RegistryError> {
        Err(RegistryError::VersionNotFound)
    }

    async fn get_package_versions(&self, package_id: Uuid) -> Result<Vec<PackageVersion>, RegistryError> {
        Ok(Vec::new())
    }

    async fn get_version_dependencies(&self, version_id: Uuid) -> Result<Vec<PackageDependency>, RegistryError> {
        Ok(Vec::new())
    }

    async fn get_version_vulnerabilities(&self, version_id: Uuid) -> Result<Vec<PackageVulnerability>, RegistryError> {
        Ok(Vec::new())
    }

    async fn count_search_results(&self, query: &str, package_type: Option<PackageType>, namespace: Option<String>) -> Result<i64, RegistryError> {
        Ok(0)
    }
}

#[derive(Debug, Clone)]
pub struct VulnerabilityScanner {
    db: PgPool,
}

impl VulnerabilityScanner {
    pub fn new(db: PgPool) -> Self {
        Self { db }
    }

    pub async fn scan_package_version(&self, version_id: Uuid) -> Result<(), RegistryError> {
        // Implementation would scan package for vulnerabilities
        Ok(())
    }
}

#[derive(Debug, thiserror::Error)]
pub enum RegistryError {
    #[error("Package not found")]
    PackageNotFound,
    #[error("Version not found")]
    VersionNotFound,
    #[error("Version already exists")]
    VersionAlreadyExists,
    #[error("Version is yanked")]
    VersionYanked,
    #[error("Invalid version: {0}")]
    InvalidVersion(String),
    #[error("File not found: {0}")]
    FileNotFound(String),
    #[error("Checksum mismatch")]
    ChecksumMismatch,
    #[error("Storage error: {0}")]
    StorageError(String),
    #[error("Permission denied")]
    PermissionDenied,
    #[error("Database error: {0}")]
    DatabaseError(#[from] sqlx::Error),
}
```

## 🐳 Docker Registry Implementation

### Docker Registry V2 API

```rust
// src/registry/docker.rs
use axum::{
    extract::{Path, Query, State},
    http::{HeaderMap, StatusCode},
    response::{IntoResponse, Response},
    Json,
};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use std::collections::HashMap;
use crate::registry::PackageRegistryService;

#[derive(Debug, Serialize, Deserialize)]
pub struct DockerManifest {
    #[serde(rename = "schemaVersion")]
    pub schema_version: i32,
    #[serde(rename = "mediaType")]
    pub media_type: String,
    pub config: DockerDescriptor,
    pub layers: Vec<DockerDescriptor>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DockerDescriptor {
    #[serde(rename = "mediaType")]
    pub media_type: String,
    pub size: i64,
    pub digest: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DockerError {
    pub code: String,
    pub message: String,
    pub detail: Option<serde_json::Value>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DockerErrorResponse {
    pub errors: Vec<DockerError>,
}

pub struct DockerRegistryHandler {
    registry_service: PackageRegistryService,
}

impl DockerRegistryHandler {
    pub fn new(registry_service: PackageRegistryService) -> Self {
        Self { registry_service }
    }

    /// Check if registry supports Docker Registry V2 API
    pub async fn check_v2_support() -> impl IntoResponse {
        // Return empty response with proper headers
        let mut headers = HeaderMap::new();
        headers.insert("Docker-Distribution-API-Version", "registry/2.0".parse().unwrap());

        (StatusCode::OK, headers)
    }

    /// Get repository tags
    pub async fn get_tags(
        State(handler): State<DockerRegistryHandler>,
        Path(name): Path<String>,
        Query(params): Query<HashMap<String, String>>,
    ) -> impl IntoResponse {
        let n = params.get("n").and_then(|s| s.parse().ok()).unwrap_or(100);
        let last = params.get("last");

        match handler.get_repository_tags(&name, n, last).await {
            Ok(tags) => {
                let response = serde_json::json!({
                    "name": name,
                    "tags": tags
                });
                (StatusCode::OK, Json(response))
            }
            Err(e) => {
                let error_response = DockerErrorResponse {
                    errors: vec![DockerError {
                        code: "NAME_UNKNOWN".to_string(),
                        message: format!("Repository {} not found", name),
                        detail: None,
                    }],
                };
                (StatusCode::NOT_FOUND, Json(error_response))
            }
        }
    }

    /// Get manifest by tag or digest
    pub async fn get_manifest(
        State(handler): State<DockerRegistryHandler>,
        Path((name, reference)): Path<(String, String)>,
        headers: HeaderMap,
    ) -> impl IntoResponse {
        let accept_header = headers
            .get("Accept")
            .and_then(|h| h.to_str().ok())
            .unwrap_or("application/vnd.docker.distribution.manifest.v2+json");

        match handler.get_image_manifest(&name, &reference).await {
            Ok(manifest) => {
                let mut response_headers = HeaderMap::new();
                response_headers.insert(
                    "Content-Type",
                    accept_header.parse().unwrap(),
                );
                response_headers.insert(
                    "Docker-Content-Digest",
                    format!("sha256:{}", manifest.digest).parse().unwrap(),
                );

                (StatusCode::OK, response_headers, Json(manifest.content))
            }
            Err(_) => {
                let error_response = DockerErrorResponse {
                    errors: vec![DockerError {
                        code: "MANIFEST_UNKNOWN".to_string(),
                        message: format!("Manifest for {}:{} not found", name, reference),
                        detail: None,
                    }],
                };
                (StatusCode::NOT_FOUND, Json(error_response))
            }
        }
    }

    /// Put manifest (push)
    pub async fn put_manifest(
        State(handler): State<DockerRegistryHandler>,
        Path((name, reference)): Path<(String, String)>,
        headers: HeaderMap,
        body: String,
    ) -> impl IntoResponse {
        let content_type = headers
            .get("Content-Type")
            .and_then(|h| h.to_str().ok())
            .unwrap_or("application/vnd.docker.distribution.manifest.v2+json");

        match handler.store_image_manifest(&name, &reference, &body, content_type).await {
            Ok(digest) => {
                let mut response_headers = HeaderMap::new();
                response_headers.insert(
                    "Location",
                    format!("/v2/{}/manifests/{}", name, digest).parse().unwrap(),
                );
                response_headers.insert(
                    "Docker-Content-Digest",
                    digest.parse().unwrap(),
                );

                (StatusCode::CREATED, response_headers)
            }
            Err(_) => {
                let error_response = DockerErrorResponse {
                    errors: vec![DockerError {
                        code: "MANIFEST_INVALID".to_string(),
                        message: "Manifest is invalid".to_string(),
                        detail: None,
                    }],
                };
                (StatusCode::BAD_REQUEST, Json(error_response))
            }
        }
    }

    /// Get blob by digest
    pub async fn get_blob(
        State(handler): State<DockerRegistryHandler>,
        Path((name, digest)): Path<(String, String)>,
    ) -> impl IntoResponse {
        match handler.get_image_blob(&name, &digest).await {
            Ok(blob_data) => {
                let mut headers = HeaderMap::new();
                headers.insert(
                    "Content-Type",
                    "application/octet-stream".parse().unwrap(),
                );
                headers.insert(
                    "Docker-Content-Digest",
                    digest.parse().unwrap(),
                );
                headers.insert(
                    "Content-Length",
                    blob_data.len().to_string().parse().unwrap(),
                );

                (StatusCode::OK, headers, blob_data)
            }
            Err(_) => {
                let error_response = DockerErrorResponse {
                    errors: vec![DockerError {
                        code: "BLOB_UNKNOWN".to_string(),
                        message: format!("Blob {} not found", digest),
                        detail: None,
                    }],
                };
                (StatusCode::NOT_FOUND, Json(error_response)).into_response()
            }
        }
    }

    /// Start blob upload
    pub async fn start_blob_upload(
        State(handler): State<DockerRegistryHandler>,
        Path(name): Path<String>,
    ) -> impl IntoResponse {
        match handler.initiate_blob_upload(&name).await {
            Ok(upload_uuid) => {
                let mut headers = HeaderMap::new();
                headers.insert(
                    "Location",
                    format!("/v2/{}/blobs/uploads/{}", name, upload_uuid).parse().unwrap(),
                );
                headers.insert(
                    "Range",
                    "0-0".parse().unwrap(),
                );
                headers.insert(
                    "Docker-Upload-UUID",
                    upload_uuid.parse().unwrap(),
                );

                (StatusCode::ACCEPTED, headers)
            }
            Err(_) => {
                let error_response = DockerErrorResponse {
                    errors: vec![DockerError {
                        code: "UNSUPPORTED".to_string(),
                        message: "Blob upload not supported".to_string(),
                        detail: None,
                    }],
                };
                (StatusCode::BAD_REQUEST, Json(error_response))
            }
        }
    }

    /// Upload blob chunk
    pub async fn upload_blob_chunk(
        State(handler): State<DockerRegistryHandler>,
        Path((name, uuid)): Path<(String, String)>,
        headers: HeaderMap,
        body: Vec<u8>,
    ) -> impl IntoResponse {
        let content_range = headers
            .get("Content-Range")
            .and_then(|h| h.to_str().ok());

        match handler.upload_blob_chunk(&name, &uuid, body, content_range).await {
            Ok(range) => {
                let mut response_headers = HeaderMap::new();
                response_headers.insert(
                    "Location",
                    format!("/v2/{}/blobs/uploads/{}", name, uuid).parse().unwrap(),
                );
                response_headers.insert(
                    "Range",
                    range.parse().unwrap(),
                );
                response_headers.insert(
                    "Docker-Upload-UUID",
                    uuid.parse().unwrap(),
                );

                (StatusCode::ACCEPTED, response_headers)
            }
            Err(_) => {
                let error_response = DockerErrorResponse {
                    errors: vec![DockerError {
                        code: "BLOB_UPLOAD_INVALID".to_string(),
                        message: "Blob upload is invalid".to_string(),
                        detail: None,
                    }],
                };
                (StatusCode::BAD_REQUEST, Json(error_response))
            }
        }
    }

    /// Complete blob upload
    pub async fn complete_blob_upload(
        State(handler): State<DockerRegistryHandler>,
        Path((name, uuid)): Path<(String, String)>,
        Query(params): Query<HashMap<String, String>>,
        body: Vec<u8>,
    ) -> impl IntoResponse {
        let digest = params.get("digest").cloned();

        match handler.complete_blob_upload(&name, &uuid, body, digest).await {
            Ok(final_digest) => {
                let mut headers = HeaderMap::new();
                headers.insert(
                    "Location",
                    format!("/v2/{}/blobs/{}", name, final_digest).parse().unwrap(),
                );
                headers.insert(
                    "Docker-Content-Digest",
                    final_digest.parse().unwrap(),
                );

                (StatusCode::CREATED, headers)
            }
            Err(_) => {
                let error_response = DockerErrorResponse {
                    errors: vec![DockerError {
                        code: "BLOB_UPLOAD_INVALID".to_string(),
                        message: "Blob upload completion failed".to_string(),
                        detail: None,
                    }],
                };
                (StatusCode::BAD_REQUEST, Json(error_response))
            }
        }
    }

    // Implementation methods
    async fn get_repository_tags(
        &self,
        name: &str,
        limit: i32,
        last: Option<&String>,
    ) -> Result<Vec<String>, DockerRegistryError> {
        // Implementation would fetch tags from database
        Ok(vec!["latest".to_string(), "v1.0.0".to_string()])
    }

    async fn get_image_manifest(
        &self,
        name: &str,
        reference: &str,
    ) -> Result<DockerManifestResponse, DockerRegistryError> {
        // Implementation would fetch manifest from storage
        Err(DockerRegistryError::ManifestNotFound)
    }

    async fn store_image_manifest(
        &self,
        name: &str,
        reference: &str,
        manifest: &str,
        content_type: &str,
    ) -> Result<String, DockerRegistryError> {
        // Implementation would store manifest and return digest
        Ok("sha256:abc123".to_string())
    }

    async fn get_image_blob(
        &self,
        name: &str,
        digest: &str,
    ) -> Result<Vec<u8>, DockerRegistryError> {
        // Implementation would fetch blob data
        Err(DockerRegistryError::BlobNotFound)
    }

    async fn initiate_blob_upload(&self, name: &str) -> Result<String, DockerRegistryError> {
        // Implementation would create upload session
        Ok(Uuid::new_v4().to_string())
    }

    async fn upload_blob_chunk(
        &self,
        name: &str,
        uuid: &str,
        data: Vec<u8>,
        content_range: Option<&str>,
    ) -> Result<String, DockerRegistryError> {
        // Implementation would handle chunked upload
        Ok(format!("0-{}", data.len() - 1))
    }

    async fn complete_blob_upload(
        &self,
        name: &str,
        uuid: &str,
        data: Vec<u8>,
        expected_digest: Option<String>,
    ) -> Result<String, DockerRegistryError> {
        // Implementation would finalize blob upload
        Ok("sha256:def456".to_string())
    }
}

#[derive(Debug)]
pub struct DockerManifestResponse {
    pub content: DockerManifest,
    pub digest: String,
}

#[derive(Debug, thiserror::Error)]
pub enum DockerRegistryError {
    #[error("Manifest not found")]
    ManifestNotFound,
    #[error("Blob not found")]
    BlobNotFound,
    #[error("Upload not found")]
    UploadNotFound,
    #[error("Invalid digest")]
    InvalidDigest,
}
```

## 📦 NPM Registry Implementation

### NPM Registry API

```rust
// src/registry/npm.rs
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use crate::registry::PackageRegistryService;

#[derive(Debug, Serialize, Deserialize)]
pub struct NpmPackageMetadata {
    pub name: String,
    pub description: Option<String>,
    pub version: String,
    pub main: Option<String>,
    pub scripts: Option<HashMap<String, String>>,
    pub dependencies: Option<HashMap<String, String>>,
    #[serde(rename = "devDependencies")]
    pub dev_dependencies: Option<HashMap<String, String>>,
    #[serde(rename = "peerDependencies")]
    pub peer_dependencies: Option<HashMap<String, String>>,
    pub keywords: Option<Vec<String>>,
    pub author: Option<NpmAuthor>,
    pub license: Option<String>,
    pub repository: Option<NpmRepository>,
    pub homepage: Option<String>,
    pub bugs: Option<NpmBugs>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NpmAuthor {
    pub name: String,
    pub email: Option<String>,
    pub url: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NpmRepository {
    #[serde(rename = "type")]
    pub repo_type: String,
    pub url: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NpmBugs {
    pub url: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NpmPackageResponse {
    #[serde(rename = "_id")]
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    #[serde(rename = "dist-tags")]
    pub dist_tags: HashMap<String, String>,
    pub versions: HashMap<String, NpmVersionInfo>,
    pub time: HashMap<String, String>,
    pub maintainers: Vec<NpmMaintainer>,
    pub author: Option<NpmAuthor>,
    pub repository: Option<NpmRepository>,
    pub homepage: Option<String>,
    pub bugs: Option<NpmBugs>,
    pub license: Option<String>,
    pub keywords: Option<Vec<String>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NpmVersionInfo {
    pub name: String,
    pub version: String,
    pub description: Option<String>,
    pub main: Option<String>,
    pub scripts: Option<HashMap<String, String>>,
    pub dependencies: Option<HashMap<String, String>>,
    #[serde(rename = "devDependencies")]
    pub dev_dependencies: Option<HashMap<String, String>>,
    #[serde(rename = "peerDependencies")]
    pub peer_dependencies: Option<HashMap<String, String>>,
    pub author: Option<NpmAuthor>,
    pub license: Option<String>,
    pub dist: NpmDist,
    #[serde(rename = "_npmUser")]
    pub npm_user: Option<NpmUser>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NpmDist {
    pub tarball: String,
    pub shasum: String,
    pub integrity: Option<String>,
    #[serde(rename = "fileCount")]
    pub file_count: Option<i32>,
    #[serde(rename = "unpackedSize")]
    pub unpacked_size: Option<i64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NpmMaintainer {
    pub name: String,
    pub email: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NpmUser {
    pub name: String,
    pub email: String,
}

pub struct NpmRegistryHandler {
    registry_service: PackageRegistryService,
}

impl NpmRegistryHandler {
    pub fn new(registry_service: PackageRegistryService) -> Self {
        Self { registry_service }
    }

    /// Get package metadata
    pub async fn get_package(
        State(handler): State<NpmRegistryHandler>,
        Path(name): Path<String>,
    ) -> Result<Json<NpmPackageResponse>, StatusCode> {
        match handler.get_npm_package_metadata(&name).await {
            Ok(metadata) => Ok(Json(metadata)),
            Err(_) => Err(StatusCode::NOT_FOUND),
        }
    }

    /// Get specific package version
    pub async fn get_package_version(
        State(handler): State<NpmRegistryHandler>,
        Path((name, version)): Path<(String, String)>,
    ) -> Result<Json<NpmVersionInfo>, StatusCode> {
        match handler.get_npm_version_info(&name, &version).await {
            Ok(version_info) => Ok(Json(version_info)),
            Err(_) => Err(StatusCode::NOT_FOUND),
        }
    }

    /// Download package tarball
    pub async fn download_tarball(
        State(handler): State<NpmRegistryHandler>,
        Path((name, filename)): Path<(String, String)>,
    ) -> Result<Vec<u8>, StatusCode> {
        // Extract version from filename (e.g., "package-1.0.0.tgz")
        let version = handler.extract_version_from_filename(&filename);

        match handler.download_npm_package(&name, &version).await {
            Ok(tarball_data) => Ok(tarball_data),
            Err(_) => Err(StatusCode::NOT_FOUND),
        }
    }

    /// Publish package
    pub async fn publish_package(
        State(handler): State<NpmRegistryHandler>,
        Json(publish_data): Json<serde_json::Value>,
    ) -> Result<Json<serde_json::Value>, StatusCode> {
        match handler.publish_npm_package(publish_data).await {
            Ok(response) => Ok(Json(response)),
            Err(_) => Err(StatusCode::BAD_REQUEST),
        }
    }

    /// Search packages
    pub async fn search_packages(
        State(handler): State<NpmRegistryHandler>,
        Query(params): Query<HashMap<String, String>>,
    ) -> Result<Json<serde_json::Value>, StatusCode> {
        let text = params.get("text").cloned().unwrap_or_default();
        let size = params.get("size")
            .and_then(|s| s.parse().ok())
            .unwrap_or(20);
        let from = params.get("from")
            .and_then(|s| s.parse().ok())
            .unwrap_or(0);

        match handler.search_npm_packages(&text, size, from).await {
            Ok(results) => Ok(Json(results)),
            Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
        }
    }

    // Implementation methods
    async fn get_npm_package_metadata(&self, name: &str) -> Result<NpmPackageResponse, NpmError> {
        // Implementation would fetch package metadata from database
        Err(NpmError::PackageNotFound)
    }

    async fn get_npm_version_info(&self, name: &str, version: &str) -> Result<NpmVersionInfo, NpmError> {
        // Implementation would fetch specific version info
        Err(NpmError::VersionNotFound)
    }

    async fn download_npm_package(&self, name: &str, version: &str) -> Result<Vec<u8>, NpmError> {
        // Implementation would download tarball
        Err(NpmError::PackageNotFound)
    }

    async fn publish_npm_package(&self, data: serde_json::Value) -> Result<serde_json::Value, NpmError> {
        // Implementation would handle NPM publish protocol
        Ok(serde_json::json!({"ok": true}))
    }

    async fn search_npm_packages(&self, query: &str, size: i32, from: i32) -> Result<serde_json::Value, NpmError> {
        // Implementation would search packages
        Ok(serde_json::json!({
            "objects": [],
            "total": 0,
            "time": "2024-01-01T00:00:00.000Z"
        }))
    }

    fn extract_version_from_filename(&self, filename: &str) -> String {
        // Extract version from filename like "package-1.0.0.tgz"
        if let Some(captures) = regex::Regex::new(r"^(.+)-(\d+\.\d+\.\d+.*?)\.tgz$")
            .unwrap()
            .captures(filename)
        {
            captures.get(2).unwrap().as_str().to_string()
        } else {
            "latest".to_string()
        }
    }
}

#[derive(Debug, thiserror::Error)]
pub enum NpmError {
    #[error("Package not found")]
    PackageNotFound,
    #[error("Version not found")]
    VersionNotFound,
    #[error("Invalid package data")]
    InvalidPackageData,
}
```

## ☕ Maven Repository Implementation

### Maven Repository API

```rust
// src/registry/maven.rs
use axum::{
    extract::{Path, State},
    http::{HeaderMap, StatusCode},
    response::Response,
};
use serde::{Deserialize, Serialize};
use crate::registry::PackageRegistryService;

#[derive(Debug, Serialize, Deserialize)]
pub struct MavenMetadata {
    #[serde(rename = "groupId")]
    pub group_id: String,
    #[serde(rename = "artifactId")]
    pub artifact_id: String,
    pub versioning: MavenVersioning,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MavenVersioning {
    pub latest: Option<String>,
    pub release: Option<String>,
    pub versions: Vec<String>,
    #[serde(rename = "lastUpdated")]
    pub last_updated: String,
}

pub struct MavenRegistryHandler {
    registry_service: PackageRegistryService,
}

impl MavenRegistryHandler {
    pub fn new(registry_service: PackageRegistryService) -> Self {
        Self { registry_service }
    }

    /// Get artifact metadata
    pub async fn get_metadata(
        State(handler): State<MavenRegistryHandler>,
        Path(path): Path<String>,
    ) -> Result<Response, StatusCode> {
        let (group_id, artifact_id) = handler.parse_maven_path(&path)?;

        match handler.get_maven_metadata(&group_id, &artifact_id).await {
            Ok(metadata_xml) => {
                let mut headers = HeaderMap::new();
                headers.insert("Content-Type", "application/xml".parse().unwrap());

                Ok(Response::builder()
                    .status(StatusCode::OK)
                    .headers(headers)
                    .body(metadata_xml.into())
                    .unwrap())
            }
            Err(_) => Err(StatusCode::NOT_FOUND),
        }
    }

    /// Download artifact
    pub async fn download_artifact(
        State(handler): State<MavenRegistryHandler>,
        Path(path): Path<String>,
    ) -> Result<Vec<u8>, StatusCode> {
        match handler.get_maven_artifact(&path).await {
            Ok(artifact_data) => Ok(artifact_data),
            Err(_) => Err(StatusCode::NOT_FOUND),
        }
    }

    /// Upload artifact (PUT)
    pub async fn upload_artifact(
        State(handler): State<MavenRegistryHandler>,
        Path(path): Path<String>,
        body: Vec<u8>,
    ) -> Result<StatusCode, StatusCode> {
        match handler.store_maven_artifact(&path, body).await {
            Ok(_) => Ok(StatusCode::CREATED),
            Err(_) => Err(StatusCode::BAD_REQUEST),
        }
    }

    fn parse_maven_path(&self, path: &str) -> Result<(String, String), StatusCode> {
        // Parse Maven path like "com/example/my-artifact/maven-metadata.xml"
        let parts: Vec<&str> = path.split('/').collect();
        if parts.len() < 3 {
            return Err(StatusCode::BAD_REQUEST);
        }

        let artifact_id = parts[parts.len() - 2];
        let group_id = parts[..parts.len() - 2].join(".");

        Ok((group_id, artifact_id.to_string()))
    }

    async fn get_maven_metadata(&self, group_id: &str, artifact_id: &str) -> Result<String, MavenError> {
        // Implementation would generate Maven metadata XML
        let metadata = format!(
            r#"<?xml version="1.0" encoding="UTF-8"?>
<metadata>
  <groupId>{}</groupId>
  <artifactId>{}</artifactId>
  <versioning>
    <latest>1.0.0</latest>
    <release>1.0.0</release>
    <versions>
      <version>1.0.0</version>
    </versions>
    <lastUpdated>20240101000000</lastUpdated>
  </versioning>
</metadata>"#,
            group_id, artifact_id
        );

        Ok(metadata)
    }

    async fn get_maven_artifact(&self, path: &str) -> Result<Vec<u8>, MavenError> {
        // Implementation would fetch artifact file
        Err(MavenError::ArtifactNotFound)
    }

    async fn store_maven_artifact(&self, path: &str, data: Vec<u8>) -> Result<(), MavenError> {
        // Implementation would store artifact
        Ok(())
    }
}

#[derive(Debug, thiserror::Error)]
pub enum MavenError {
    #[error("Artifact not found")]
    ArtifactNotFound,
    #[error("Invalid path")]
    InvalidPath,
}
```

## 🐍 PyPI Registry Implementation

### PyPI Simple API

```rust
// src/registry/pypi.rs
use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::{Html, Json},
};
use serde::{Deserialize, Serialize};
use crate::registry::PackageRegistryService;

#[derive(Debug, Serialize, Deserialize)]
pub struct PyPIPackageInfo {
    pub info: PyPIInfo,
    pub urls: Vec<PyPIUrl>,
    pub releases: std::collections::HashMap<String, Vec<PyPIRelease>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PyPIInfo {
    pub name: String,
    pub version: String,
    pub summary: Option<String>,
    pub description: Option<String>,
    pub author: Option<String>,
    pub author_email: Option<String>,
    pub license: Option<String>,
    pub home_page: Option<String>,
    pub keywords: Option<String>,
    pub classifiers: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PyPIUrl {
    pub filename: String,
    pub url: String,
    pub md5_digest: String,
    pub sha256_digest: String,
    pub size: i64,
    pub upload_time: String,
    pub python_version: String,
    pub packagetype: String, // "sdist", "bdist_wheel", etc.
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PyPIRelease {
    pub filename: String,
    pub url: String,
    pub md5_digest: String,
    pub sha256_digest: String,
    pub size: i64,
    pub upload_time: String,
    pub python_version: String,
    pub packagetype: String,
}

pub struct PyPIRegistryHandler {
    registry_service: PackageRegistryService,
}

impl PyPIRegistryHandler {
    pub fn new(registry_service: PackageRegistryService) -> Self {
        Self { registry_service }
    }

    /// Simple API index
    pub async fn simple_index(
        State(handler): State<PyPIRegistryHandler>,
    ) -> Result<Html<String>, StatusCode> {
        match handler.get_pypi_simple_index().await {
            Ok(html) => Ok(Html(html)),
            Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
        }
    }

    /// Simple API package page
    pub async fn simple_package(
        State(handler): State<PyPIRegistryHandler>,
        Path(name): Path<String>,
    ) -> Result<Html<String>, StatusCode> {
        match handler.get_pypi_simple_package(&name).await {
            Ok(html) => Ok(Html(html)),
            Err(_) => Err(StatusCode::NOT_FOUND),
        }
    }

    /// JSON API package info
    pub async fn package_json(
        State(handler): State<PyPIRegistryHandler>,
        Path(name): Path<String>,
    ) -> Result<Json<PyPIPackageInfo>, StatusCode> {
        match handler.get_pypi_package_info(&name).await {
            Ok(info) => Ok(Json(info)),
            Err(_) => Err(StatusCode::NOT_FOUND),
        }
    }

    /// Download package file
    pub async fn download_file(
        State(handler): State<PyPIRegistryHandler>,
        Path((name, filename)): Path<(String, String)>,
    ) -> Result<Vec<u8>, StatusCode> {
        match handler.download_pypi_file(&name, &filename).await {
            Ok(file_data) => Ok(file_data),
            Err(_) => Err(StatusCode::NOT_FOUND),
        }
    }

    async fn get_pypi_simple_index(&self) -> Result<String, PyPIError> {
        // Implementation would generate simple index HTML
        let html = r#"<!DOCTYPE html>
<html>
<head>
    <title>Simple Index</title>
</head>
<body>
    <h1>Simple Index</h1>
    <a href="/simple/example-package/">example-package</a><br/>
</body>
</html>"#;

        Ok(html.to_string())
    }

    async fn get_pypi_simple_package(&self, name: &str) -> Result<String, PyPIError> {
        // Implementation would generate package page HTML with download links
        let html = format!(
            r#"<!DOCTYPE html>
<html>
<head>
    <title>Links for {}</title>
</head>
<body>
    <h1>Links for {}</h1>
    <a href="/packages/{}-1.0.0.tar.gz#sha256=abc123">{}-1.0.0.tar.gz</a><br/>
    <a href="/packages/{}-1.0.0-py3-none-any.whl#sha256=def456">{}-1.0.0-py3-none-any.whl</a><br/>
</body>
</html>"#,
            name, name, name, name, name, name
        );

        Ok(html)
    }

    async fn get_pypi_package_info(&self, name: &str) -> Result<PyPIPackageInfo, PyPIError> {
        // Implementation would fetch package info from database
        Err(PyPIError::PackageNotFound)
    }

    async fn download_pypi_file(&self, name: &str, filename: &str) -> Result<Vec<u8>, PyPIError> {
        // Implementation would download package file
        Err(PyPIError::FileNotFound)
    }
}

#[derive(Debug, thiserror::Error)]
pub enum PyPIError {
    #[error("Package not found")]
    PackageNotFound,
    #[error("File not found")]
    FileNotFound,
}
```

## 🦀 Cargo Registry Implementation

### Cargo Registry API

```rust
// src/registry/cargo.rs
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use crate::registry::PackageRegistryService;

#[derive(Debug, Serialize, Deserialize)]
pub struct CargoCrate {
    pub name: String,
    pub vers: String,
    pub deps: Vec<CargoDependency>,
    pub features: HashMap<String, Vec<String>>,
    pub authors: Vec<String>,
    pub description: Option<String>,
    pub documentation: Option<String>,
    pub homepage: Option<String>,
    pub readme: Option<String>,
    pub readme_file: Option<String>,
    pub keywords: Vec<String>,
    pub categories: Vec<String>,
    pub license: Option<String>,
    pub license_file: Option<String>,
    pub repository: Option<String>,
    pub badges: HashMap<String, HashMap<String, String>>,
    pub links: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CargoDependency {
    pub name: String,
    pub req: String,
    pub features: Vec<String>,
    pub optional: bool,
    pub default_features: bool,
    pub target: Option<String>,
    pub kind: String, // "normal", "dev", "build"
    pub registry: Option<String>,
    pub package: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CargoSearchResult {
    pub crates: Vec<CargoSearchCrate>,
    pub meta: CargoSearchMeta,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CargoSearchCrate {
    pub name: String,
    pub max_version: String,
    pub description: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CargoSearchMeta {
    pub total: i32,
}

pub struct CargoRegistryHandler {
    registry_service: PackageRegistryService,
}

impl CargoRegistryHandler {
    pub fn new(registry_service: PackageRegistryService) -> Self {
        Self { registry_service }
    }

    /// Download crate
    pub async fn download_crate(
        State(handler): State<CargoRegistryHandler>,
        Path((name, version)): Path<(String, String)>,
    ) -> Result<Vec<u8>, StatusCode> {
        match handler.get_cargo_crate(&name, &version).await {
            Ok(crate_data) => Ok(crate_data),
            Err(_) => Err(StatusCode::NOT_FOUND),
        }
    }

    /// Search crates
    pub async fn search_crates(
        State(handler): State<CargoRegistryHandler>,
        Query(params): Query<HashMap<String, String>>,
    ) -> Result<Json<CargoSearchResult>, StatusCode> {
        let q = params.get("q").cloned().unwrap_or_default();
        let per_page = params.get("per_page")
            .and_then(|s| s.parse().ok())
            .unwrap_or(10);

        match handler.search_cargo_crates(&q, per_page).await {
            Ok(results) => Ok(Json(results)),
            Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
        }
    }

    /// Publish crate
    pub async fn publish_crate(
        State(handler): State<CargoRegistryHandler>,
        body: Vec<u8>,
    ) -> Result<Json<serde_json::Value>, StatusCode> {
        match handler.publish_cargo_crate(body).await {
            Ok(response) => Ok(Json(response)),
            Err(_) => Err(StatusCode::BAD_REQUEST),
        }
    }

    async fn get_cargo_crate(&self, name: &str, version: &str) -> Result<Vec<u8>, CargoError> {
        // Implementation would fetch crate file
        Err(CargoError::CrateNotFound)
    }

    async fn search_cargo_crates(&self, query: &str, per_page: i32) -> Result<CargoSearchResult, CargoError> {
        // Implementation would search crates
        Ok(CargoSearchResult {
            crates: Vec::new(),
            meta: CargoSearchMeta { total: 0 },
        })
    }

    async fn publish_cargo_crate(&self, data: Vec<u8>) -> Result<serde_json::Value, CargoError> {
        // Implementation would handle Cargo publish protocol
        Ok(serde_json::json!({"warnings": {"invalid_categories": [], "invalid_badges": []}}))
    }
}

#[derive(Debug, thiserror::Error)]
pub enum CargoError {
    #[error("Crate not found")]
    CrateNotFound,
    #[error("Invalid crate data")]
    InvalidCrateData,
}
```

## 🎯 Key Takeaways

### Package Registry Benefits

1. **Centralized Distribution**: Single source for all package types
2. **Security Scanning**: Automated vulnerability detection
3. **Version Management**: Semantic versioning and dependency resolution
4. **Performance**: CDN distribution and caching
5. **Compliance**: License tracking and policy enforcement

### Advanced Features Implemented

- **Multi-Language Support**: Docker, NPM, Maven, NuGet, PyPI, Cargo registries
- **Protocol Compatibility**: Native client compatibility for each package type
- **Vulnerability Scanning**: Automated security analysis of packages
- **Dependency Resolution**: Smart dependency management and conflict detection
- **Package Signing**: Cryptographic verification of package integrity
- **Mirroring & Proxying**: Upstream registry integration and caching

### Performance Considerations

- **Storage Optimization**: Deduplication and compression
- **CDN Integration**: Global content distribution
- **Caching Strategy**: Multi-layer caching for metadata and packages
- **Parallel Processing**: Concurrent package operations
- **Database Indexing**: Optimized search and retrieval

### Security Best Practices

- **Package Verification**: Checksum and signature validation
- **Access Controls**: Fine-grained publishing permissions
- **Vulnerability Scanning**: Continuous security monitoring
- **Audit Logging**: Complete package activity tracking
- **Rate Limiting**: Prevent abuse and ensure availability

Ready to continue with [Module 15: Advanced Analytics & Insights](./module-15-analytics-insights.md)?
