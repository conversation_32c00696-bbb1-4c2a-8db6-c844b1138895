# Module 2: Git Protocol Implementation

## 🎯 Learning Objectives

By the end of this module, you will:
- Understand Git's internal object model (blobs, trees, commits, refs)
- Implement Git's smart HTTP protocol for clone/push/pull operations
- <PERSON> Rust's advanced error handling with custom error types
- Learn file system operations and repository management
- Understand performance considerations and zero-copy operations

## 🧠 Understanding Git's Internal Architecture

Before implementing the protocol, let's understand how Git actually works under the hood. This knowledge is crucial for building a Git server.

### Git's Object Model

Git stores everything as objects in a content-addressable filesystem. There are four types of objects:

```mermaid
graph TD
    subgraph "Git Object Types"
        BLOB[Blob Object<br/>File Content]
        TREE[Tree Object<br/>Directory Structure]
        COMMIT[Commit Object<br/>Snapshot + Metadata]
        TAG[Tag Object<br/>Named Reference]
    end
    
    subgraph "Relationships"
        COMMIT --> TREE
        TREE --> BLOB
        TREE --> TREE2[Sub-Tree]
        COMMIT --> PARENT[Parent Commit]
        TAG --> COMMIT
    end
    
    subgraph "Storage"
        OBJECTS[.git/objects/]
        REFS[.git/refs/]
        HEAD[.git/HEAD]
    end
    
    BLOB --> OBJECTS
    TREE --> OBJECTS
    COMMIT --> OBJECTS
    TAG --> OBJECTS
    
    COMMIT --> REFS
    TAG --> REFS
    HEAD --> REFS
```

### Why This Design is Brilliant

1. **Content Addressable**: Objects are named by their SHA-1 hash
2. **Immutable**: Once created, objects never change
3. **Deduplication**: Identical content = same hash = stored once
4. **Integrity**: Any corruption is immediately detectable

Let's see this in action:

```bash
# Create a simple repository to explore
mkdir git-exploration && cd git-exploration
git init
echo "Hello, World!" > hello.txt
git add hello.txt
git commit -m "Initial commit"

# Explore the objects
find .git/objects -type f
# Output: .git/objects/8a/b686eafeb1f44702738c8b0f24f2567c36da6d (blob)
#         .git/objects/68/aba62e560c0ebc3396e8ae9335232cd93a3f60 (tree)
#         .git/objects/a1/b2c3d4... (commit)
```

## 🛠️ Implementing Git Objects in Rust

Let's start by modeling Git's object system in Rust. This will teach us about:
- **Enums**: Rust's powerful algebraic data types
- **Traits**: Shared behavior across types
- **Serialization**: Converting between binary and text formats

### 1. Add New Dependencies

Update `backend/Cargo.toml`:

```toml
[dependencies]
# Existing dependencies...
sha1 = "0.10"
flate2 = "1.0"
hex = "0.4"
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
thiserror = "1.0"
anyhow = "1.0"
```

**Why These Choices?**:
- **sha1**: Git uses SHA-1 for object hashing
- **flate2**: Git compresses objects with zlib
- **hex**: Convert binary hashes to hex strings
- **chrono**: Handle timestamps in commits
- **thiserror**: Create custom error types easily
- **anyhow**: Ergonomic error handling

### 2. Define Git Objects

Create `src/git/mod.rs`:

```rust
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sha1::{Digest, Sha1};
use std::collections::HashMap;
use thiserror::Error;

#[derive(Error, Debug)]
pub enum GitError {
    #[error("Object not found: {hash}")]
    ObjectNotFound { hash: String },
    #[error("Invalid object type: {object_type}")]
    InvalidObjectType { object_type: String },
    #[error("Compression error: {0}")]
    CompressionError(#[from] std::io::Error),
    #[error("Serialization error: {0}")]
    SerializationError(String),
}

pub type GitResult<T> = Result<T, GitError>;

/// Git object types - this is an enum (like a union in C# but type-safe)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum GitObject {
    Blob(BlobObject),
    Tree(TreeObject),
    Commit(CommitObject),
    Tag(TagObject),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlobObject {
    pub content: Vec<u8>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TreeEntry {
    pub mode: String,      // File permissions (e.g., "100644", "040000")
    pub name: String,      // File/directory name
    pub hash: String,      // SHA-1 hash of the object
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TreeObject {
    pub entries: Vec<TreeEntry>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommitObject {
    pub tree: String,                    // Hash of the tree object
    pub parents: Vec<String>,            // Parent commit hashes
    pub author: GitSignature,
    pub committer: GitSignature,
    pub message: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitSignature {
    pub name: String,
    pub email: String,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TagObject {
    pub object: String,      // Hash of the tagged object
    pub object_type: String, // Type of the tagged object
    pub tag: String,         // Tag name
    pub tagger: GitSignature,
    pub message: String,
}

impl GitObject {
    /// Calculate the SHA-1 hash of this object (like Git does)
    pub fn hash(&self) -> GitResult<String> {
        let content = self.serialize()?;
        let mut hasher = Sha1::new();
        hasher.update(&content);
        Ok(hex::encode(hasher.finalize()))
    }
    
    /// Serialize object to Git's internal format
    pub fn serialize(&self) -> GitResult<Vec<u8>> {
        match self {
            GitObject::Blob(blob) => {
                let header = format!("blob {}\0", blob.content.len());
                let mut result = header.into_bytes();
                result.extend_from_slice(&blob.content);
                Ok(result)
            }
            GitObject::Tree(tree) => {
                let mut content = Vec::new();
                for entry in &tree.entries {
                    content.extend_from_slice(entry.mode.as_bytes());
                    content.push(b' ');
                    content.extend_from_slice(entry.name.as_bytes());
                    content.push(b'\0');
                    // Convert hex hash to binary
                    let hash_bytes = hex::decode(&entry.hash)
                        .map_err(|e| GitError::SerializationError(e.to_string()))?;
                    content.extend_from_slice(&hash_bytes);
                }
                
                let header = format!("tree {}\0", content.len());
                let mut result = header.into_bytes();
                result.extend_from_slice(&content);
                Ok(result)
            }
            GitObject::Commit(commit) => {
                let mut content = String::new();
                content.push_str(&format!("tree {}\n", commit.tree));
                
                for parent in &commit.parents {
                    content.push_str(&format!("parent {}\n", parent));
                }
                
                content.push_str(&format!(
                    "author {} <{}> {} +0000\n",
                    commit.author.name,
                    commit.author.email,
                    commit.author.timestamp.timestamp()
                ));
                
                content.push_str(&format!(
                    "committer {} <{}> {} +0000\n",
                    commit.committer.name,
                    commit.committer.email,
                    commit.committer.timestamp.timestamp()
                ));
                
                content.push('\n');
                content.push_str(&commit.message);
                
                let header = format!("commit {}\0", content.len());
                let mut result = header.into_bytes();
                result.extend_from_slice(content.as_bytes());
                Ok(result)
            }
            GitObject::Tag(tag) => {
                let content = format!(
                    "object {}\ntype {}\ntag {}\ntagger {} <{}> {} +0000\n\n{}",
                    tag.object,
                    tag.object_type,
                    tag.tag,
                    tag.tagger.name,
                    tag.tagger.email,
                    tag.tagger.timestamp.timestamp(),
                    tag.message
                );
                
                let header = format!("tag {}\0", content.len());
                let mut result = header.into_bytes();
                result.extend_from_slice(content.as_bytes());
                Ok(result)
            }
        }
    }
}

/// Trait for objects that can be stored in Git
pub trait GitStorable {
    fn object_type(&self) -> &'static str;
    fn content(&self) -> &[u8];
}

impl GitStorable for BlobObject {
    fn object_type(&self) -> &'static str {
        "blob"
    }
    
    fn content(&self) -> &[u8] {
        &self.content
    }
}
```

### 3. Key Rust Concepts Explained

#### Enums vs Classes

**C# Approach** (Inheritance):
```csharp
abstract class GitObject { }
class BlobObject : GitObject { }
class TreeObject : GitObject { }
```

**Rust Approach** (Algebraic Data Types):
```rust
enum GitObject {
    Blob(BlobObject),
    Tree(TreeObject),
    // ...
}
```

**Why Enums are Better**:
- **Exhaustive Matching**: Compiler ensures you handle all cases
- **Memory Efficient**: No vtables or heap allocation for polymorphism
- **Type Safety**: Impossible to have invalid states

#### Error Handling with `thiserror`

```rust
#[derive(Error, Debug)]
pub enum GitError {
    #[error("Object not found: {hash}")]
    ObjectNotFound { hash: String },
    #[error("Compression error: {0}")]
    CompressionError(#[from] std::io::Error),
}
```

This generates:
- `Display` implementation for user-friendly messages
- `From` implementations for automatic error conversion
- `Error` trait implementation

#### Traits vs Interfaces

**C# Interface**:
```csharp
interface IGitStorable {
    string ObjectType { get; }
    byte[] Content { get; }
}
```

**Rust Trait**:
```rust
trait GitStorable {
    fn object_type(&self) -> &'static str;
    fn content(&self) -> &[u8];
}
```

**Key Differences**:
- Traits can be implemented for existing types
- No runtime overhead (monomorphization)
- More flexible than inheritance

## 🧪 Testing Our Git Objects

Create `src/git/tests.rs`:

```rust
#[cfg(test)]
mod tests {
    use super::*;
    use chrono::Utc;

    #[test]
    fn test_blob_object_hash() {
        let blob = GitObject::Blob(BlobObject {
            content: b"Hello, World!".to_vec(),
        });
        
        let hash = blob.hash().unwrap();
        // This should match what Git would produce
        assert_eq!(hash.len(), 40); // SHA-1 is 40 hex characters
    }

    #[test]
    fn test_commit_object_serialization() {
        let commit = GitObject::Commit(CommitObject {
            tree: "abc123".to_string(),
            parents: vec!["def456".to_string()],
            author: GitSignature {
                name: "Test User".to_string(),
                email: "<EMAIL>".to_string(),
                timestamp: Utc::now(),
            },
            committer: GitSignature {
                name: "Test User".to_string(),
                email: "<EMAIL>".to_string(),
                timestamp: Utc::now(),
            },
            message: "Test commit".to_string(),
        });

        let serialized = commit.serialize().unwrap();
        assert!(serialized.starts_with(b"commit "));
    }

    #[test]
    fn test_tree_object_with_entries() {
        let tree = GitObject::Tree(TreeObject {
            entries: vec![
                TreeEntry {
                    mode: "100644".to_string(),
                    name: "README.md".to_string(),
                    hash: "abc123def456".to_string(),
                },
                TreeEntry {
                    mode: "040000".to_string(),
                    name: "src".to_string(),
                    hash: "def456abc123".to_string(),
                },
            ],
        });

        let hash = tree.hash().unwrap();
        assert_eq!(hash.len(), 40);
    }
}
```

## 🎯 Key Takeaways

1. **Git's Elegance**: Simple object model enables powerful version control
2. **Rust Enums**: More powerful than inheritance for modeling data
3. **Error Handling**: `thiserror` makes custom errors ergonomic
4. **Traits**: Flexible way to define shared behavior
5. **Testing**: Comprehensive tests catch edge cases early

## 🚀 Next Steps

In the next section, we'll implement:
- Repository creation and management
- Object storage and retrieval
- Git's smart HTTP protocol
- Performance optimizations

Ready to continue with the repository implementation?
