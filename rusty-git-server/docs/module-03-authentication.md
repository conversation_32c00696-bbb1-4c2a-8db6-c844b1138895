# Module 3: Authentication and Authorization

## 🎯 Learning Objectives

By the end of this module, you will:
- Implement JWT-based authentication in Rust
- Understand password hashing and security best practices
- Create middleware for request authentication
- Build role-based access control (RBAC)
- Learn about security vulnerabilities and how to prevent them
- Master R<PERSON>'s async middleware patterns

## 🔐 Why Authentication Matters

Authentication and authorization are critical for any Git server. Let's understand the difference:

- **Authentication**: "Who are you?" (Login with username/password)
- **Authorization**: "What can you do?" (Can you push to this repository?)

### Security Architecture Overview

```mermaid
graph TB
    subgraph "Client Request Flow"
        CLIENT[Client Request]
        AUTH_CHECK{Has Valid JWT?}
        LOGIN[Login Endpoint]
        PROTECTED[Protected Resource]
    end
    
    subgraph "Authentication Layer"
        JWT_VERIFY[JWT Verification]
        USER_DB[(User Database)]
        HASH_CHECK[Password Hash Check]
    end
    
    subgraph "Authorization Layer"
        RBAC[Role-Based Access Control]
        PERM_CHECK[Permission Check]
        REPO_ACCESS[Repository Access Control]
    end
    
    CLIENT --> AUTH_CHECK
    AUTH_CHECK -->|No| LOGIN
    AUTH_CHECK -->|Yes| JWT_VERIFY
    
    LOGIN --> HASH_CHECK
    HASH_CHECK --> USER_DB
    
    JWT_VERIFY --> RBAC
    RBAC --> PERM_CHECK
    PERM_CHECK --> REPO_ACCESS
    REPO_ACCESS --> PROTECTED
    
    style AUTH_CHECK fill:#ffeb3b
    style JWT_VERIFY fill:#4caf50
    style RBAC fill:#2196f3
```

## 🛠️ Implementing Authentication

### 1. Add Authentication Dependencies

Update `backend/Cargo.toml`:

```toml
[dependencies]
# Existing dependencies...
jsonwebtoken = "9.0"
bcrypt = "0.15"
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "chrono", "uuid"] }
tower-http = { version = "0.5", features = ["cors", "trace"] }
tracing = "0.1"
tracing-subscriber = "0.3"
```

**Why These Choices?**:
- **jsonwebtoken**: Industry-standard JWT implementation
- **bcrypt**: Secure password hashing (better than SHA-256 for passwords)
- **sqlx**: Compile-time checked SQL queries
- **tower-http**: HTTP middleware for CORS and tracing
- **tracing**: Structured logging (better than println!)

### 2. User Model and Database Schema

Create `src/auth/mod.rs`:

```rust
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::{FromRow, PgPool};
use thiserror::Error;
use uuid::Uuid;

#[derive(Error, Debug)]
pub enum AuthError {
    #[error("Invalid credentials")]
    InvalidCredentials,
    #[error("Token expired")]
    TokenExpired,
    #[error("Invalid token")]
    InvalidToken,
    #[error("User not found")]
    UserNotFound,
    #[error("User already exists")]
    UserAlreadyExists,
    #[error("Database error: {0}")]
    DatabaseError(#[from] sqlx::Error),
    #[error("Password hashing error: {0}")]
    HashError(#[from] bcrypt::BcryptError),
    #[error("JWT error: {0}")]
    JwtError(#[from] jsonwebtoken::errors::Error),
}

pub type AuthResult<T> = Result<T, AuthError>;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct User {
    pub id: Uuid,
    pub username: String,
    pub email: String,
    pub password_hash: String,
    pub role: UserRole,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub is_active: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "user_role", rename_all = "lowercase")]
pub enum UserRole {
    Admin,
    User,
    ReadOnly,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateUserRequest {
    pub username: String,
    pub email: String,
    pub password: String,
    pub role: Option<UserRole>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LoginResponse {
    pub token: String,
    pub user: UserInfo,
    pub expires_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserInfo {
    pub id: Uuid,
    pub username: String,
    pub email: String,
    pub role: UserRole,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,      // Subject (user ID)
    pub username: String,
    pub role: UserRole,
    pub exp: usize,       // Expiration time
    pub iat: usize,       // Issued at
}

impl From<User> for UserInfo {
    fn from(user: User) -> Self {
        Self {
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role,
        }
    }
}
```

### 3. Password Security - Why bcrypt?

**❌ Bad Approach** (What NOT to do):
```rust
// NEVER DO THIS - Vulnerable to rainbow table attacks
let password_hash = format!("{:x}", md5::compute(password));
```

**✅ Good Approach** (Using bcrypt):
```rust
use bcrypt::{hash, verify, DEFAULT_COST};

pub fn hash_password(password: &str) -> AuthResult<String> {
    hash(password, DEFAULT_COST).map_err(AuthError::HashError)
}

pub fn verify_password(password: &str, hash: &str) -> AuthResult<bool> {
    verify(password, hash).map_err(AuthError::HashError)
}
```

**Why bcrypt is Superior**:
1. **Salt**: Automatically generates unique salt for each password
2. **Cost Factor**: Adjustable work factor (can increase as computers get faster)
3. **Time-tested**: Industry standard, resistant to rainbow table attacks
4. **Slow by Design**: Makes brute force attacks impractical

### 4. JWT Token Management

Create `src/auth/jwt.rs`:

```rust
use super::{AuthError, AuthResult, Claims, UserRole};
use chrono::{Duration, Utc};
use jsonwebtoken::{decode, encode, DecodingKey, EncodingKey, Header, Validation};
use uuid::Uuid;

pub struct JwtManager {
    encoding_key: EncodingKey,
    decoding_key: DecodingKey,
    validation: Validation,
}

impl JwtManager {
    pub fn new(secret: &str) -> Self {
        Self {
            encoding_key: EncodingKey::from_secret(secret.as_ref()),
            decoding_key: DecodingKey::from_secret(secret.as_ref()),
            validation: Validation::default(),
        }
    }

    pub fn generate_token(
        &self,
        user_id: Uuid,
        username: String,
        role: UserRole,
    ) -> AuthResult<(String, chrono::DateTime<Utc>)> {
        let now = Utc::now();
        let expires_at = now + Duration::hours(24); // Token valid for 24 hours
        
        let claims = Claims {
            sub: user_id.to_string(),
            username,
            role,
            exp: expires_at.timestamp() as usize,
            iat: now.timestamp() as usize,
        };

        let token = encode(&Header::default(), &claims, &self.encoding_key)?;
        Ok((token, expires_at))
    }

    pub fn verify_token(&self, token: &str) -> AuthResult<Claims> {
        let token_data = decode::<Claims>(token, &self.decoding_key, &self.validation)?;
        Ok(token_data.claims)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_jwt_roundtrip() {
        let jwt_manager = JwtManager::new("test_secret_key_that_is_long_enough");
        let user_id = Uuid::new_v4();
        let username = "testuser".to_string();
        let role = UserRole::User;

        // Generate token
        let (token, _expires_at) = jwt_manager
            .generate_token(user_id, username.clone(), role.clone())
            .unwrap();

        // Verify token
        let claims = jwt_manager.verify_token(&token).unwrap();
        
        assert_eq!(claims.sub, user_id.to_string());
        assert_eq!(claims.username, username);
        assert!(matches!(claims.role, UserRole::User));
    }

    #[test]
    fn test_invalid_token() {
        let jwt_manager = JwtManager::new("test_secret_key_that_is_long_enough");
        let result = jwt_manager.verify_token("invalid.token.here");
        assert!(result.is_err());
    }
}
```

### 5. Understanding JWT Structure

JWT tokens have three parts separated by dots:

```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
```

1. **Header** (Algorithm & Token Type):
   ```json
   {
     "alg": "HS256",
     "typ": "JWT"
   }
   ```

2. **Payload** (Claims):
   ```json
   {
     "sub": "1234567890",
     "name": "John Doe",
     "iat": 1516239022
   }
   ```

3. **Signature** (Verification):
   ```
   HMACSHA256(
     base64UrlEncode(header) + "." +
     base64UrlEncode(payload),
     secret
   )
   ```

**Why JWT?**:
- **Stateless**: No need to store sessions in database
- **Scalable**: Works across multiple servers
- **Self-contained**: All info needed is in the token
- **Standard**: Widely supported across languages/platforms

## 🔒 Middleware Implementation

Create `src/auth/middleware.rs`:

```rust
use super::{AuthError, Claims, JwtManager};
use axum::{
    extract::{Request, State},
    http::{header::AUTHORIZATION, StatusCode},
    middleware::Next,
    response::Response,
};
use std::sync::Arc;

#[derive(Clone)]
pub struct AuthState {
    pub jwt_manager: Arc<JwtManager>,
}

pub async fn auth_middleware(
    State(auth_state): State<AuthState>,
    mut request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // Extract Authorization header
    let auth_header = request
        .headers()
        .get(AUTHORIZATION)
        .and_then(|header| header.to_str().ok())
        .ok_or(StatusCode::UNAUTHORIZED)?;

    // Check for Bearer token format
    if !auth_header.starts_with("Bearer ") {
        return Err(StatusCode::UNAUTHORIZED);
    }

    let token = &auth_header[7..]; // Remove "Bearer " prefix

    // Verify token
    let claims = auth_state
        .jwt_manager
        .verify_token(token)
        .map_err(|_| StatusCode::UNAUTHORIZED)?;

    // Add claims to request extensions for use in handlers
    request.extensions_mut().insert(claims);

    Ok(next.run(request).await)
}

// Helper to extract claims from request
pub fn extract_claims(request: &Request) -> Result<&Claims, AuthError> {
    request
        .extensions()
        .get::<Claims>()
        .ok_or(AuthError::InvalidToken)
}
```

### 6. Key Rust Concepts Explained

#### Middleware Pattern in Rust

**C# ASP.NET Middleware**:
```csharp
public class AuthMiddleware
{
    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        // Auth logic here
        await next(context);
    }
}
```

**Rust Axum Middleware**:
```rust
pub async fn auth_middleware(
    State(auth_state): State<AuthState>,
    mut request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // Auth logic here
    Ok(next.run(request).await)
}
```

**Key Differences**:
- **Ownership**: Rust middleware takes ownership of request
- **Error Handling**: Explicit Result types instead of exceptions
- **Type Safety**: Compile-time guarantees about state access

#### Arc (Atomically Reference Counted)

```rust
use std::sync::Arc;

let jwt_manager = Arc::new(JwtManager::new("secret"));
let cloned = Arc::clone(&jwt_manager); // Cheap clone, shared ownership
```

**Why Arc?**:
- **Shared Ownership**: Multiple parts of code can own the same data
- **Thread Safe**: Can be shared across async tasks
- **Memory Efficient**: Reference counting, not copying data

## 🧪 Testing Authentication

Create comprehensive tests in `src/auth/tests.rs`:

```rust
#[cfg(test)]
mod tests {
    use super::*;
    use crate::auth::{hash_password, verify_password};

    #[test]
    fn test_password_hashing() {
        let password = "secure_password_123";
        let hash = hash_password(password).unwrap();
        
        // Hash should be different from password
        assert_ne!(hash, password);
        
        // Should verify correctly
        assert!(verify_password(password, &hash).unwrap());
        
        // Should fail with wrong password
        assert!(!verify_password("wrong_password", &hash).unwrap());
    }

    #[test]
    fn test_password_hash_uniqueness() {
        let password = "same_password";
        let hash1 = hash_password(password).unwrap();
        let hash2 = hash_password(password).unwrap();
        
        // Same password should produce different hashes (due to salt)
        assert_ne!(hash1, hash2);
        
        // But both should verify correctly
        assert!(verify_password(password, &hash1).unwrap());
        assert!(verify_password(password, &hash2).unwrap());
    }

    #[tokio::test]
    async fn test_user_role_serialization() {
        let user = User {
            id: Uuid::new_v4(),
            username: "testuser".to_string(),
            email: "<EMAIL>".to_string(),
            password_hash: "hash".to_string(),
            role: UserRole::Admin,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            is_active: true,
        };

        let json = serde_json::to_string(&user).unwrap();
        let deserialized: User = serde_json::from_str(&json).unwrap();
        
        assert_eq!(user.username, deserialized.username);
        assert!(matches!(deserialized.role, UserRole::Admin));
    }
}
```

## 🎯 Key Takeaways

1. **Security First**: Never store plain text passwords, always use bcrypt
2. **JWT Benefits**: Stateless, scalable, self-contained authentication
3. **Middleware Pattern**: Clean separation of concerns for cross-cutting functionality
4. **Error Handling**: Comprehensive error types for better debugging
5. **Testing**: Security code needs thorough testing

## 🚀 Next Steps

In the next section, we'll implement:
- User registration and login endpoints
- Database integration with SQLx
- Role-based authorization
- Repository access control
- Security headers and CORS

Ready to continue with the database integration and API endpoints?
