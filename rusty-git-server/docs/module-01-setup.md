# Module 1: Project Setup and Rust Fundamentals

## 🎯 Learning Objectives

By the end of this module, you will:
- Understand Rust's ownership model and why it's revolutionary
- Set up a Rust project with proper structure
- Create a basic HTTP server using Tokio and Axum
- Write your first unit tests in Rust
- Understand error handling patterns

## 🤔 Why Rust? A Deep Dive

Coming from C# and JavaScript, you might wonder why we chose Rust. Let me explain with concrete examples:

### Memory Safety Without Garbage Collection

**C# Approach** (Garbage Collected):
```csharp
// C# - Runtime memory management
var data = new List<string>();
data.Add("Hello");
// GC will clean up eventually, but when?
```

**Rust Approach** (Compile-time Safety):
```rust
// Rust - Compile-time memory management
let mut data = Vec::new();
data.push("Hello".to_string());
// Memory freed immediately when `data` goes out of scope
```

**Why This Matters**: 
- No unpredictable GC pauses (critical for servers)
- No memory leaks (common in C/C++)
- Predictable performance characteristics

### The Ownership Model

Rust's ownership system is its superpower. Let's understand it step by step:

```mermaid
graph LR
    A[Variable Created] --> B[Owns Memory]
    B --> C[Variable Moved]
    C --> D[Original Invalid]
    D --> E[New Owner Valid]
    E --> F[Memory Freed When Owner Dies]
```

## 🛠️ Setting Up Your Development Environment

### 1. Install Rust

```bash
# Install Rust via rustup (official installer)
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# Restart your shell or run:
source ~/.cargo/env

# Verify installation
rustc --version
cargo --version
```

### 2. Install Essential Tools

```bash
# Code formatter (like Prettier for JS)
rustup component add rustfmt

# Linter (like ESLint for JS)
rustup component add clippy

# Language server for IDE support
rustup component add rust-analyzer
```

### 3. VS Code Setup (Recommended)

Install these extensions:
- `rust-analyzer` - Intelligent code completion
- `CodeLLDB` - Debugging support
- `crates` - Manage dependencies

## 🏗️ Project Structure

Let's create our backend project structure:

```bash
cd rusty-git-server
cargo new backend --name rusty-git-server
cd backend
```

This creates a standard Rust project with:
```
backend/
├── Cargo.toml          # Like package.json in Node.js
├── src/
│   └── main.rs         # Entry point
└── target/             # Build artifacts (like node_modules)
```

### Understanding Cargo.toml

Cargo is Rust's package manager (like npm for Node.js or NuGet for C#):

```toml
[package]
name = "rusty-git-server"
version = "0.1.0"
edition = "2021"

[dependencies]
# We'll add dependencies here
```

**Key Differences from package.json**:
- **Semantic Versioning**: More strict than npm
- **Feature Flags**: Enable/disable code at compile time
- **Build Scripts**: Custom build logic
- **Workspaces**: Monorepo support built-in

## 🚀 Your First Rust HTTP Server

Let's build a simple HTTP server to understand Rust concepts:

### 1. Add Dependencies

Edit `Cargo.toml`:

```toml
[package]
name = "rusty-git-server"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
axum = "0.7"
tower = "0.4"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
```

**Why These Choices?**:
- **Tokio**: Async runtime (like Node.js event loop, but better)
- **Axum**: Web framework (like Express.js, but type-safe)
- **Serde**: Serialization (like JSON.stringify/parse, but compile-time safe)

### 2. Basic Server Implementation

Replace `src/main.rs`:

```rust
use axum::{
    extract::Path,
    http::StatusCode,
    response::Json,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

// This is like a TypeScript interface
#[derive(Serialize, Deserialize, Debug, Clone)]
struct Repository {
    id: u32,
    name: String,
    description: Option<String>,
    created_at: String,
}

// Global state (we'll improve this later)
type AppState = HashMap<u32, Repository>;

#[tokio::main]
async fn main() {
    // Initialize our "database" (in-memory for now)
    let mut repos = HashMap::new();
    repos.insert(1, Repository {
        id: 1,
        name: "hello-world".to_string(),
        description: Some("My first repository".to_string()),
        created_at: "2024-01-01T00:00:00Z".to_string(),
    });

    // Build our application with routes
    let app = Router::new()
        .route("/", get(root))
        .route("/repositories", get(list_repositories))
        .route("/repositories/:id", get(get_repository))
        .with_state(repos);

    // Start the server
    let listener = tokio::net::TcpListener::bind("0.0.0.0:3000")
        .await
        .unwrap();
    
    println!("🚀 Server running on http://localhost:3000");
    axum::serve(listener, app).await.unwrap();
}

// Route handlers
async fn root() -> &'static str {
    "Welcome to Rusty Git Server! 🦀"
}

async fn list_repositories(
    axum::extract::State(repos): axum::extract::State<AppState>
) -> Json<Vec<Repository>> {
    let repo_list: Vec<Repository> = repos.values().cloned().collect();
    Json(repo_list)
}

async fn get_repository(
    Path(id): Path<u32>,
    axum::extract::State(repos): axum::extract::State<AppState>
) -> Result<Json<Repository>, StatusCode> {
    match repos.get(&id) {
        Some(repo) => Ok(Json(repo.clone())),
        None => Err(StatusCode::NOT_FOUND),
    }
}
```

### 3. Run Your Server

```bash
cargo run
```

Visit `http://localhost:3000` to see your server in action!

## 🧠 Understanding the Code

Let's break down the key Rust concepts:

### Ownership in Action

```rust
let name = String::from("hello");  // `name` owns the string
let name2 = name;                  // Ownership moved to `name2`
// println!("{}", name);           // ❌ This would cause a compile error!
println!("{}", name2);             // ✅ This works
```

**Why This is Powerful**:
- No accidental use of freed memory
- No double-free errors
- Compiler catches bugs at compile time

### Borrowing (References)

```rust
fn print_length(s: &String) {  // Borrows the string (doesn't take ownership)
    println!("Length: {}", s.len());
}

let name = String::from("hello");
print_length(&name);           // Lend the string
println!("{}", name);          // Still can use `name`!
```

### Pattern Matching

```rust
match repos.get(&id) {
    Some(repo) => Ok(Json(repo.clone())),  // Found the repository
    None => Err(StatusCode::NOT_FOUND),    // Repository not found
}
```

This is like a switch statement but:
- **Exhaustive**: Must handle all cases
- **Type-safe**: Compiler ensures correctness
- **Powerful**: Can destructure complex data

## 🧪 Writing Tests

Create `src/lib.rs`:

```rust
pub mod handlers {
    use super::*;
    
    pub fn calculate_repo_stats(repos: &[Repository]) -> (usize, usize) {
        let total = repos.len();
        let with_description = repos.iter()
            .filter(|repo| repo.description.is_some())
            .count();
        (total, with_description)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::handlers::calculate_repo_stats;

    #[test]
    fn test_repo_stats() {
        let repos = vec![
            Repository {
                id: 1,
                name: "repo1".to_string(),
                description: Some("Has description".to_string()),
                created_at: "2024-01-01T00:00:00Z".to_string(),
            },
            Repository {
                id: 2,
                name: "repo2".to_string(),
                description: None,
                created_at: "2024-01-01T00:00:00Z".to_string(),
            },
        ];

        let (total, with_desc) = calculate_repo_stats(&repos);
        assert_eq!(total, 2);
        assert_eq!(with_desc, 1);
    }

    #[test]
    fn test_empty_repos() {
        let repos = vec![];
        let (total, with_desc) = calculate_repo_stats(&repos);
        assert_eq!(total, 0);
        assert_eq!(with_desc, 0);
    }
}
```

Run tests:
```bash
cargo test
```

## 🎯 Key Takeaways

1. **Ownership**: Variables own their data, preventing memory issues
2. **Borrowing**: References allow sharing without transferring ownership
3. **Pattern Matching**: Exhaustive, type-safe control flow
4. **Cargo**: Powerful package manager with built-in testing
5. **Type Safety**: Catch errors at compile time, not runtime

## 🚀 Next Steps

In the next module, we'll dive into Git's internals and implement the core Git protocol. You'll learn about:
- Git's object model (blobs, trees, commits)
- File system operations
- Advanced error handling
- Performance considerations

## 💡 Exercises

1. Add a new endpoint `/repositories/:id/stats` that returns repository statistics
2. Implement error handling for invalid repository IDs
3. Add logging using the `tracing` crate
4. Write integration tests for your HTTP endpoints

Ready for [Module 2: Git Protocol Implementation](./module-02-git-protocol.md)?
