# Module 10: Code Review System

## 🎯 Learning Objectives

By the end of this module, you will:
- Build a complete pull/merge request workflow system
- Implement inline code comments and discussions
- Create review assignment and approval workflows
- Understand CODEOWNERS and automated reviewer selection
- Build draft PR functionality for work-in-progress collaboration
- Master review analytics and quality metrics

## 🔍 Why Code Review Systems Matter

Code review is the cornerstone of software quality and team collaboration:
- **Quality Assurance**: Catch bugs and design issues before production
- **Knowledge Sharing**: Spread domain knowledge across the team
- **Mentorship**: Help junior developers learn best practices
- **Compliance**: Meet regulatory requirements for code changes
- **Documentation**: Create a history of decision-making

### Code Review Workflow Architecture

```mermaid
graph TB
    subgraph "Pull Request Lifecycle"
        CREATE[Create PR]
        DRAFT[Draft State]
        REVIEW[Ready for Review]
        APPROVED[Approved]
        MERGED[Merged]
        CLOSED[Closed]
    end
    
    subgraph "Review Process"
        ASSIGN[Auto-assign Reviewers]
        COMMENT[Inline Comments]
        DISCUSS[Discussion Threads]
        APPROVE[Approve Changes]
        REQUEST[Request Changes]
    end
    
    subgraph "Quality Gates"
        CI[CI/CD Checks]
        TESTS[Test Results]
        COVERAGE[Code Coverage]
        SECURITY[Security Scan]
        CODEOWNERS[CODEOWNERS Check]
    end
    
    CREATE --> DRAFT
    DRAFT --> REVIEW
    REVIEW --> ASSIGN
    ASSIGN --> COMMENT
    COMMENT --> DISCUSS
    DISCUSS --> APPROVE
    DISCUSS --> REQUEST
    APPROVE --> CI
    CI --> TESTS
    TESTS --> COVERAGE
    COVERAGE --> SECURITY
    SECURITY --> CODEOWNERS
    CODEOWNERS --> APPROVED
    APPROVED --> MERGED
    
    REQUEST --> COMMENT
    
    style CREATE fill:#e8f5e8
    style APPROVED fill:#e1f5fe
    style MERGED fill:#fff3e0
```

## 📝 Pull Request Data Model

### Core Data Structures

```rust
// src/review/mod.rs
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct PullRequest {
    pub id: Uuid,
    pub repository_id: Uuid,
    pub number: i32,                    // Sequential PR number per repository
    pub title: String,
    pub description: Option<String>,
    pub author_id: Uuid,
    pub source_branch: String,
    pub target_branch: String,
    pub source_commit: String,
    pub target_commit: String,
    pub merge_commit: Option<String>,
    pub state: PullRequestState,
    pub is_draft: bool,
    pub is_mergeable: bool,
    pub merge_method: Option<MergeMethod>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub merged_at: Option<DateTime<Utc>>,
    pub closed_at: Option<DateTime<Utc>>,
    
    // Metrics
    pub additions: i32,
    pub deletions: i32,
    pub changed_files: i32,
    pub commits_count: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "pull_request_state", rename_all = "lowercase")]
pub enum PullRequestState {
    Open,
    Closed,
    Merged,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "merge_method", rename_all = "lowercase")]
pub enum MergeMethod {
    Merge,      // Create merge commit
    Squash,     // Squash all commits into one
    Rebase,     // Rebase and fast-forward
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Review {
    pub id: Uuid,
    pub pull_request_id: Uuid,
    pub reviewer_id: Uuid,
    pub state: ReviewState,
    pub body: Option<String>,
    pub submitted_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "review_state", rename_all = "lowercase")]
pub enum ReviewState {
    Pending,        // Review requested but not started
    Commented,      // Comments added but no approval/rejection
    Approved,       // Changes approved
    ChangesRequested, // Changes requested before approval
    Dismissed,      // Review dismissed (usually by new commits)
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct ReviewComment {
    pub id: Uuid,
    pub pull_request_id: Uuid,
    pub review_id: Option<Uuid>,
    pub author_id: Uuid,
    pub body: String,
    pub file_path: Option<String>,
    pub line_number: Option<i32>,
    pub commit_hash: String,
    pub is_resolved: bool,
    pub in_reply_to: Option<Uuid>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct ReviewAssignment {
    pub id: Uuid,
    pub pull_request_id: Uuid,
    pub reviewer_id: Uuid,
    pub assigned_by: Uuid,
    pub assignment_type: AssignmentType,
    pub is_required: bool,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "assignment_type", rename_all = "lowercase")]
pub enum AssignmentType {
    Manual,         // Manually assigned by user
    Codeowners,     // Assigned by CODEOWNERS file
    Algorithm,      // Assigned by load balancing algorithm
    Team,           // Assigned to entire team
}

// Request/Response DTOs
#[derive(Debug, Deserialize)]
pub struct CreatePullRequestRequest {
    pub title: String,
    pub description: Option<String>,
    pub source_branch: String,
    pub target_branch: String,
    pub is_draft: bool,
}

#[derive(Debug, Deserialize)]
pub struct UpdatePullRequestRequest {
    pub title: Option<String>,
    pub description: Option<String>,
    pub is_draft: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct CreateReviewRequest {
    pub body: Option<String>,
    pub event: ReviewEvent,
    pub comments: Vec<CreateReviewCommentRequest>,
}

#[derive(Debug, Deserialize)]
pub enum ReviewEvent {
    Comment,
    Approve,
    RequestChanges,
}

#[derive(Debug, Deserialize)]
pub struct CreateReviewCommentRequest {
    pub body: String,
    pub file_path: String,
    pub line_number: i32,
    pub commit_hash: String,
}

#[derive(Debug, Serialize)]
pub struct PullRequestResponse {
    pub pull_request: PullRequest,
    pub author: UserInfo,
    pub reviews: Vec<ReviewResponse>,
    pub assignees: Vec<UserInfo>,
    pub labels: Vec<String>,
    pub mergeable: bool,
    pub merge_conflicts: Vec<String>,
}

#[derive(Debug, Serialize)]
pub struct ReviewResponse {
    pub review: Review,
    pub reviewer: UserInfo,
    pub comments: Vec<ReviewCommentResponse>,
}

#[derive(Debug, Serialize)]
pub struct ReviewCommentResponse {
    pub comment: ReviewComment,
    pub author: UserInfo,
    pub replies: Vec<ReviewCommentResponse>,
}

#[derive(Debug, Serialize)]
pub struct UserInfo {
    pub id: Uuid,
    pub username: String,
    pub email: String,
    pub avatar_url: Option<String>,
}
```

### Database Schema

```sql
-- Pull requests table
CREATE TABLE pull_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    repository_id UUID NOT NULL REFERENCES repositories(id),
    number SERIAL NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    author_id UUID NOT NULL REFERENCES users(id),
    source_branch VARCHAR(255) NOT NULL,
    target_branch VARCHAR(255) NOT NULL,
    source_commit VARCHAR(40) NOT NULL,
    target_commit VARCHAR(40) NOT NULL,
    merge_commit VARCHAR(40),
    state pull_request_state NOT NULL DEFAULT 'open',
    is_draft BOOLEAN DEFAULT FALSE,
    is_mergeable BOOLEAN DEFAULT TRUE,
    merge_method merge_method,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    merged_at TIMESTAMP WITH TIME ZONE,
    closed_at TIMESTAMP WITH TIME ZONE,
    
    -- Metrics
    additions INTEGER DEFAULT 0,
    deletions INTEGER DEFAULT 0,
    changed_files INTEGER DEFAULT 0,
    commits_count INTEGER DEFAULT 0,
    
    UNIQUE(repository_id, number)
);

-- Reviews table
CREATE TABLE reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    pull_request_id UUID NOT NULL REFERENCES pull_requests(id) ON DELETE CASCADE,
    reviewer_id UUID NOT NULL REFERENCES users(id),
    state review_state NOT NULL DEFAULT 'pending',
    body TEXT,
    submitted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(pull_request_id, reviewer_id)
);

-- Review comments table
CREATE TABLE review_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    pull_request_id UUID NOT NULL REFERENCES pull_requests(id) ON DELETE CASCADE,
    review_id UUID REFERENCES reviews(id) ON DELETE CASCADE,
    author_id UUID NOT NULL REFERENCES users(id),
    body TEXT NOT NULL,
    file_path VARCHAR(500),
    line_number INTEGER,
    commit_hash VARCHAR(40) NOT NULL,
    is_resolved BOOLEAN DEFAULT FALSE,
    in_reply_to UUID REFERENCES review_comments(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Review assignments table
CREATE TABLE review_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    pull_request_id UUID NOT NULL REFERENCES pull_requests(id) ON DELETE CASCADE,
    reviewer_id UUID NOT NULL REFERENCES users(id),
    assigned_by UUID NOT NULL REFERENCES users(id),
    assignment_type assignment_type NOT NULL,
    is_required BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(pull_request_id, reviewer_id)
);

-- PR labels (many-to-many)
CREATE TABLE pull_request_labels (
    pull_request_id UUID NOT NULL REFERENCES pull_requests(id) ON DELETE CASCADE,
    label VARCHAR(100) NOT NULL,
    PRIMARY KEY (pull_request_id, label)
);

-- Indexes for performance
CREATE INDEX idx_pull_requests_repository ON pull_requests(repository_id, state, created_at DESC);
CREATE INDEX idx_pull_requests_author ON pull_requests(author_id, created_at DESC);
CREATE INDEX idx_pull_requests_state ON pull_requests(state, updated_at DESC);
CREATE INDEX idx_reviews_pr ON reviews(pull_request_id, state);
CREATE INDEX idx_review_comments_pr ON review_comments(pull_request_id, created_at);
CREATE INDEX idx_review_comments_file ON review_comments(pull_request_id, file_path, line_number);
CREATE INDEX idx_review_assignments_reviewer ON review_assignments(reviewer_id, created_at DESC);

-- Full-text search on PR titles and descriptions
CREATE INDEX idx_pull_requests_search ON pull_requests USING gin(to_tsvector('english', title || ' ' || COALESCE(description, '')));
```

## 🔧 Pull Request Service Implementation

### Core Service Logic

```rust
// src/review/service.rs
use sqlx::{PgPool, Row};
use uuid::Uuid;
use chrono::Utc;
use std::collections::HashMap;
use crate::git::{BranchManager, DiffEngine, FileDiff};
use crate::review::*;

pub struct PullRequestService {
    db: PgPool,
    branch_manager: BranchManager,
}

impl PullRequestService {
    pub fn new(db: PgPool, branch_manager: BranchManager) -> Self {
        Self { db, branch_manager }
    }

    /// Create a new pull request
    pub async fn create_pull_request(
        &self,
        repository_id: Uuid,
        author_id: Uuid,
        request: CreatePullRequestRequest,
    ) -> Result<PullRequest, ReviewError> {
        // Validate branches exist
        self.validate_branches(repository_id, &request.source_branch, &request.target_branch).await?;

        // Get commit hashes for branches
        let source_commit = self.get_branch_commit(repository_id, &request.source_branch).await?;
        let target_commit = self.get_branch_commit(repository_id, &request.target_branch).await?;

        // Check if PR already exists for these branches
        if self.pr_exists(repository_id, &request.source_branch, &request.target_branch).await? {
            return Err(ReviewError::PullRequestAlreadyExists);
        }

        // Calculate diff statistics
        let diff_stats = self.calculate_diff_stats(repository_id, &source_commit, &target_commit).await?;

        // Get next PR number for repository
        let pr_number = self.get_next_pr_number(repository_id).await?;

        // Create pull request
        let pull_request = sqlx::query_as::<_, PullRequest>(
            r#"
            INSERT INTO pull_requests (
                repository_id, number, title, description, author_id,
                source_branch, target_branch, source_commit, target_commit,
                is_draft, additions, deletions, changed_files, commits_count
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
            RETURNING *
            "#
        )
        .bind(repository_id)
        .bind(pr_number)
        .bind(&request.title)
        .bind(&request.description)
        .bind(author_id)
        .bind(&request.source_branch)
        .bind(&request.target_branch)
        .bind(&source_commit)
        .bind(&target_commit)
        .bind(request.is_draft)
        .bind(diff_stats.additions)
        .bind(diff_stats.deletions)
        .bind(diff_stats.changed_files)
        .bind(diff_stats.commits_count)
        .fetch_one(&self.db)
        .await?;

        // Auto-assign reviewers if not draft
        if !request.is_draft {
            self.auto_assign_reviewers(&pull_request).await?;
        }

        Ok(pull_request)
    }

    /// Update pull request
    pub async fn update_pull_request(
        &self,
        pr_id: Uuid,
        user_id: Uuid,
        request: UpdatePullRequestRequest,
    ) -> Result<PullRequest, ReviewError> {
        // Check permissions
        let pr = self.get_pull_request(pr_id).await?;
        if pr.author_id != user_id {
            return Err(ReviewError::Unauthorized);
        }

        let mut query = sqlx::QueryBuilder::new("UPDATE pull_requests SET updated_at = NOW()");
        let mut has_updates = false;

        if let Some(title) = &request.title {
            query.push(", title = ");
            query.push_bind(title);
            has_updates = true;
        }

        if let Some(description) = &request.description {
            query.push(", description = ");
            query.push_bind(description);
            has_updates = true;
        }

        if let Some(is_draft) = request.is_draft {
            query.push(", is_draft = ");
            query.push_bind(is_draft);
            has_updates = true;

            // Auto-assign reviewers when converting from draft to ready
            if !is_draft && pr.is_draft {
                self.auto_assign_reviewers(&pr).await?;
            }
        }

        if !has_updates {
            return Ok(pr);
        }

        query.push(" WHERE id = ");
        query.push_bind(pr_id);
        query.push(" RETURNING *");

        let updated_pr = query
            .build_query_as::<PullRequest>()
            .fetch_one(&self.db)
            .await?;

        Ok(updated_pr)
    }

    /// Submit a review
    pub async fn submit_review(
        &self,
        pr_id: Uuid,
        reviewer_id: Uuid,
        request: CreateReviewRequest,
    ) -> Result<Review, ReviewError> {
        let pr = self.get_pull_request(pr_id).await?;

        // Check if reviewer is assigned
        if !self.is_reviewer_assigned(pr_id, reviewer_id).await? {
            return Err(ReviewError::ReviewerNotAssigned);
        }

        let mut tx = self.db.begin().await?;

        // Create or update review
        let review_state = match request.event {
            ReviewEvent::Comment => ReviewState::Commented,
            ReviewEvent::Approve => ReviewState::Approved,
            ReviewEvent::RequestChanges => ReviewState::ChangesRequested,
        };

        let review = sqlx::query_as::<_, Review>(
            r#"
            INSERT INTO reviews (pull_request_id, reviewer_id, state, body, submitted_at)
            VALUES ($1, $2, $3, $4, NOW())
            ON CONFLICT (pull_request_id, reviewer_id)
            DO UPDATE SET
                state = EXCLUDED.state,
                body = EXCLUDED.body,
                submitted_at = NOW(),
                updated_at = NOW()
            RETURNING *
            "#
        )
        .bind(pr_id)
        .bind(reviewer_id)
        .bind(review_state)
        .bind(&request.body)
        .fetch_one(&mut *tx)
        .await?;

        // Add review comments
        for comment_request in request.comments {
            sqlx::query(
                r#"
                INSERT INTO review_comments (
                    pull_request_id, review_id, author_id, body,
                    file_path, line_number, commit_hash
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                "#
            )
            .bind(pr_id)
            .bind(review.id)
            .bind(reviewer_id)
            .bind(&comment_request.body)
            .bind(&comment_request.file_path)
            .bind(comment_request.line_number)
            .bind(&comment_request.commit_hash)
            .execute(&mut *tx)
            .await?;
        }

        // Update PR mergeable status
        self.update_mergeable_status(pr_id, &mut tx).await?;

        tx.commit().await?;

        Ok(review)
    }

    /// Merge pull request
    pub async fn merge_pull_request(
        &self,
        pr_id: Uuid,
        user_id: Uuid,
        merge_method: MergeMethod,
    ) -> Result<PullRequest, ReviewError> {
        let pr = self.get_pull_request(pr_id).await?;

        // Validate merge conditions
        self.validate_merge_conditions(&pr, user_id).await?;

        let mut tx = self.db.begin().await?;

        // Perform Git merge
        let merge_commit = self.perform_git_merge(&pr, merge_method).await?;

        // Update pull request
        let merged_pr = sqlx::query_as::<_, PullRequest>(
            r#"
            UPDATE pull_requests
            SET state = 'merged', merge_commit = $1, merged_at = NOW(), updated_at = NOW()
            WHERE id = $2
            RETURNING *
            "#
        )
        .bind(&merge_commit)
        .bind(pr_id)
        .fetch_one(&mut *tx)
        .await?;

        // Dismiss pending reviews
        sqlx::query(
            "UPDATE reviews SET state = 'dismissed' WHERE pull_request_id = $1 AND state = 'pending'"
        )
        .bind(pr_id)
        .execute(&mut *tx)
        .await?;

        tx.commit().await?;

        Ok(merged_pr)
    }

    /// Get pull request with full details
    pub async fn get_pull_request_details(
        &self,
        pr_id: Uuid,
    ) -> Result<PullRequestResponse, ReviewError> {
        let pr = self.get_pull_request(pr_id).await?;

        // Get author info
        let author = self.get_user_info(pr.author_id).await?;

        // Get reviews with comments
        let reviews = self.get_pull_request_reviews(pr_id).await?;

        // Get assignees
        let assignees = self.get_pull_request_assignees(pr_id).await?;

        // Get labels
        let labels = self.get_pull_request_labels(pr_id).await?;

        // Check mergeability
        let (mergeable, conflicts) = self.check_mergeability(&pr).await?;

        Ok(PullRequestResponse {
            pull_request: pr,
            author,
            reviews,
            assignees,
            labels,
            mergeable,
            merge_conflicts: conflicts,
        })
    }

    // Helper methods
    async fn validate_branches(
        &self,
        repository_id: Uuid,
        source: &str,
        target: &str,
    ) -> Result<(), ReviewError> {
        // Implementation would check if branches exist in the repository
        Ok(())
    }

    async fn get_branch_commit(&self, repository_id: Uuid, branch: &str) -> Result<String, ReviewError> {
        // Implementation would get the latest commit hash for the branch
        Ok("abc123def456".to_string()) // Placeholder
    }

    async fn pr_exists(
        &self,
        repository_id: Uuid,
        source: &str,
        target: &str,
    ) -> Result<bool, ReviewError> {
        let count: i64 = sqlx::query_scalar(
            r#"
            SELECT COUNT(*) FROM pull_requests
            WHERE repository_id = $1 AND source_branch = $2 AND target_branch = $3 AND state = 'open'
            "#
        )
        .bind(repository_id)
        .bind(source)
        .bind(target)
        .fetch_one(&self.db)
        .await?;

        Ok(count > 0)
    }

    async fn get_next_pr_number(&self, repository_id: Uuid) -> Result<i32, ReviewError> {
        let next_number: Option<i32> = sqlx::query_scalar(
            "SELECT COALESCE(MAX(number), 0) + 1 FROM pull_requests WHERE repository_id = $1"
        )
        .bind(repository_id)
        .fetch_one(&self.db)
        .await?;

        Ok(next_number.unwrap_or(1))
    }

    async fn calculate_diff_stats(
        &self,
        repository_id: Uuid,
        source_commit: &str,
        target_commit: &str,
    ) -> Result<DiffStats, ReviewError> {
        // Implementation would calculate actual diff statistics
        Ok(DiffStats {
            additions: 42,
            deletions: 13,
            changed_files: 5,
            commits_count: 3,
        })
    }

    async fn auto_assign_reviewers(&self, pr: &PullRequest) -> Result<(), ReviewError> {
        // Implementation would:
        // 1. Parse CODEOWNERS file
        // 2. Apply load balancing algorithm
        // 3. Assign reviewers based on rules
        Ok(())
    }

    async fn get_pull_request(&self, pr_id: Uuid) -> Result<PullRequest, ReviewError> {
        let pr = sqlx::query_as::<_, PullRequest>(
            "SELECT * FROM pull_requests WHERE id = $1"
        )
        .bind(pr_id)
        .fetch_optional(&self.db)
        .await?
        .ok_or(ReviewError::PullRequestNotFound)?;

        Ok(pr)
    }

    async fn is_reviewer_assigned(&self, pr_id: Uuid, reviewer_id: Uuid) -> Result<bool, ReviewError> {
        let count: i64 = sqlx::query_scalar(
            "SELECT COUNT(*) FROM review_assignments WHERE pull_request_id = $1 AND reviewer_id = $2"
        )
        .bind(pr_id)
        .bind(reviewer_id)
        .fetch_one(&self.db)
        .await?;

        Ok(count > 0)
    }

    async fn update_mergeable_status(
        &self,
        pr_id: Uuid,
        tx: &mut sqlx::Transaction<'_, sqlx::Postgres>,
    ) -> Result<(), ReviewError> {
        // Implementation would check:
        // 1. Required reviews are approved
        // 2. No changes requested
        // 3. CI checks pass
        // 4. No merge conflicts
        Ok(())
    }

    async fn validate_merge_conditions(
        &self,
        pr: &PullRequest,
        user_id: Uuid,
    ) -> Result<(), ReviewError> {
        if pr.state != PullRequestState::Open {
            return Err(ReviewError::PullRequestNotOpen);
        }

        if !pr.is_mergeable {
            return Err(ReviewError::PullRequestNotMergeable);
        }

        // Check if user has merge permissions
        // Implementation would verify permissions

        Ok(())
    }

    async fn perform_git_merge(
        &self,
        pr: &PullRequest,
        merge_method: MergeMethod,
    ) -> Result<String, ReviewError> {
        // Implementation would perform actual Git merge
        Ok("merge123commit456".to_string()) // Placeholder
    }

    async fn get_user_info(&self, user_id: Uuid) -> Result<UserInfo, ReviewError> {
        // Implementation would fetch user information
        Ok(UserInfo {
            id: user_id,
            username: "user".to_string(),
            email: "<EMAIL>".to_string(),
            avatar_url: None,
        })
    }

    async fn get_pull_request_reviews(&self, pr_id: Uuid) -> Result<Vec<ReviewResponse>, ReviewError> {
        // Implementation would fetch reviews with comments
        Ok(Vec::new())
    }

    async fn get_pull_request_assignees(&self, pr_id: Uuid) -> Result<Vec<UserInfo>, ReviewError> {
        // Implementation would fetch assigned reviewers
        Ok(Vec::new())
    }

    async fn get_pull_request_labels(&self, pr_id: Uuid) -> Result<Vec<String>, ReviewError> {
        let labels: Vec<String> = sqlx::query_scalar(
            "SELECT label FROM pull_request_labels WHERE pull_request_id = $1"
        )
        .bind(pr_id)
        .fetch_all(&self.db)
        .await?;

        Ok(labels)
    }

    async fn check_mergeability(&self, pr: &PullRequest) -> Result<(bool, Vec<String>), ReviewError> {
        // Implementation would check for merge conflicts
        Ok((true, Vec::new()))
    }
}

#[derive(Debug)]
struct DiffStats {
    additions: i32,
    deletions: i32,
    changed_files: i32,
    commits_count: i32,
}

#[derive(Debug, thiserror::Error)]
pub enum ReviewError {
    #[error("Pull request not found")]
    PullRequestNotFound,
    #[error("Pull request already exists")]
    PullRequestAlreadyExists,
    #[error("Pull request is not open")]
    PullRequestNotOpen,
    #[error("Pull request is not mergeable")]
    PullRequestNotMergeable,
    #[error("Reviewer not assigned")]
    ReviewerNotAssigned,
    #[error("Unauthorized")]
    Unauthorized,
    #[error("Database error: {0}")]
    DatabaseError(#[from] sqlx::Error),
}
```

## 👥 CODEOWNERS and Reviewer Assignment

### CODEOWNERS File Implementation

```rust
// src/review/codeowners.rs
use std::collections::HashMap;
use regex::Regex;
use glob::Pattern;
use uuid::Uuid;

#[derive(Debug, Clone)]
pub struct CodeOwner {
    pub owner_type: OwnerType,
    pub identifier: String, // username, team name, or email
}

#[derive(Debug, Clone)]
pub enum OwnerType {
    User(Uuid),
    Team(String),
    Email(String),
}

#[derive(Debug, Clone)]
pub struct CodeOwnersRule {
    pub pattern: String,
    pub glob_pattern: Pattern,
    pub owners: Vec<CodeOwner>,
    pub is_optional: bool,
}

pub struct CodeOwnersParser;

impl CodeOwnersParser {
    /// Parse CODEOWNERS file content
    pub fn parse(content: &str) -> Result<Vec<CodeOwnersRule>, CodeOwnersError> {
        let mut rules = Vec::new();

        for (line_num, line) in content.lines().enumerate() {
            let line = line.trim();

            // Skip empty lines and comments
            if line.is_empty() || line.starts_with('#') {
                continue;
            }

            match Self::parse_line(line) {
                Ok(Some(rule)) => rules.push(rule),
                Ok(None) => continue,
                Err(e) => {
                    return Err(CodeOwnersError::ParseError {
                        line: line_num + 1,
                        message: e.to_string(),
                    });
                }
            }
        }

        // Reverse rules because later rules override earlier ones
        rules.reverse();

        Ok(rules)
    }

    fn parse_line(line: &str) -> Result<Option<CodeOwnersRule>, Box<dyn std::error::Error>> {
        let parts: Vec<&str> = line.split_whitespace().collect();

        if parts.is_empty() {
            return Ok(None);
        }

        let pattern = parts[0];
        let owner_parts = &parts[1..];

        if owner_parts.is_empty() {
            return Err("No owners specified".into());
        }

        // Check for optional marker
        let is_optional = pattern.starts_with('^');
        let clean_pattern = if is_optional {
            &pattern[1..]
        } else {
            pattern
        };

        // Create glob pattern
        let glob_pattern = Pattern::new(clean_pattern)?;

        // Parse owners
        let mut owners = Vec::new();
        for owner_str in owner_parts {
            let owner = Self::parse_owner(owner_str)?;
            owners.push(owner);
        }

        Ok(Some(CodeOwnersRule {
            pattern: clean_pattern.to_string(),
            glob_pattern,
            owners,
            is_optional,
        }))
    }

    fn parse_owner(owner_str: &str) -> Result<CodeOwner, Box<dyn std::error::Error>> {
        if owner_str.starts_with('@') {
            let identifier = &owner_str[1..];

            if identifier.contains('/') {
                // Team reference: @org/team
                Ok(CodeOwner {
                    owner_type: OwnerType::Team(identifier.to_string()),
                    identifier: identifier.to_string(),
                })
            } else {
                // User reference: @username
                // In real implementation, would resolve username to UUID
                Ok(CodeOwner {
                    owner_type: OwnerType::User(Uuid::new_v4()), // Placeholder
                    identifier: identifier.to_string(),
                })
            }
        } else if owner_str.contains('@') && owner_str.contains('.') {
            // Email address
            Ok(CodeOwner {
                owner_type: OwnerType::Email(owner_str.to_string()),
                identifier: owner_str.to_string(),
            })
        } else {
            Err(format!("Invalid owner format: {}", owner_str).into())
        }
    }
}

pub struct CodeOwnersService {
    user_service: UserService,
    team_service: TeamService,
}

impl CodeOwnersService {
    pub fn new(user_service: UserService, team_service: TeamService) -> Self {
        Self {
            user_service,
            team_service,
        }
    }

    /// Get code owners for changed files
    pub async fn get_owners_for_files(
        &self,
        repository_id: Uuid,
        changed_files: &[String],
    ) -> Result<Vec<Uuid>, CodeOwnersError> {
        // Get CODEOWNERS file content
        let codeowners_content = self.get_codeowners_file(repository_id).await?;
        let rules = CodeOwnersParser::parse(&codeowners_content)?;

        let mut all_owners = std::collections::HashSet::new();

        for file_path in changed_files {
            let file_owners = self.get_owners_for_file(file_path, &rules).await?;
            all_owners.extend(file_owners);
        }

        Ok(all_owners.into_iter().collect())
    }

    async fn get_owners_for_file(
        &self,
        file_path: &str,
        rules: &[CodeOwnersRule],
    ) -> Result<Vec<Uuid>, CodeOwnersError> {
        let mut owners = Vec::new();

        // Find matching rules (first match wins due to reversed order)
        for rule in rules {
            if rule.glob_pattern.matches(file_path) {
                for owner in &rule.owners {
                    match &owner.owner_type {
                        OwnerType::User(user_id) => {
                            owners.push(*user_id);
                        }
                        OwnerType::Team(team_name) => {
                            let team_members = self.team_service.get_team_members(team_name).await?;
                            owners.extend(team_members);
                        }
                        OwnerType::Email(email) => {
                            if let Some(user_id) = self.user_service.get_user_by_email(email).await? {
                                owners.push(user_id);
                            }
                        }
                    }
                }

                // Stop at first matching rule
                break;
            }
        }

        Ok(owners)
    }

    async fn get_codeowners_file(&self, repository_id: Uuid) -> Result<String, CodeOwnersError> {
        // Try different locations for CODEOWNERS file
        let locations = [
            ".github/CODEOWNERS",
            ".gitlab/CODEOWNERS",
            "docs/CODEOWNERS",
            "CODEOWNERS",
        ];

        for location in &locations {
            if let Ok(content) = self.read_file_from_repo(repository_id, location).await {
                return Ok(content);
            }
        }

        Err(CodeOwnersError::FileNotFound)
    }

    async fn read_file_from_repo(
        &self,
        repository_id: Uuid,
        file_path: &str,
    ) -> Result<String, CodeOwnersError> {
        // Implementation would read file from Git repository
        Ok(String::new()) // Placeholder
    }
}

#[derive(Debug, thiserror::Error)]
pub enum CodeOwnersError {
    #[error("CODEOWNERS file not found")]
    FileNotFound,
    #[error("Parse error at line {line}: {message}")]
    ParseError { line: usize, message: String },
    #[error("User service error: {0}")]
    UserServiceError(String),
    #[error("Team service error: {0}")]
    TeamServiceError(String),
}

// Placeholder services
pub struct UserService;
impl UserService {
    pub async fn get_user_by_email(&self, email: &str) -> Result<Option<Uuid>, CodeOwnersError> {
        Ok(Some(Uuid::new_v4())) // Placeholder
    }
}

pub struct TeamService;
impl TeamService {
    pub async fn get_team_members(&self, team_name: &str) -> Result<Vec<Uuid>, CodeOwnersError> {
        Ok(vec![Uuid::new_v4()]) // Placeholder
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_codeowners_parsing() {
        let content = r#"
# Global owners
* @global-owner1 @global-owner2

# Frontend team owns all JS/TS files
*.js @frontend-team
*.ts @frontend-team
src/frontend/ @frontend-team

# Backend team owns Rust files
*.rs @backend-team
src/backend/ @backend-team

# DevOps owns CI/CD files
.github/ @devops-team
Dockerfile @devops-team
docker-compose.yml @devops-team

# Security team owns security-related files
^security/ @security-team
*.security @security-team

# Specific file ownership
README.md @docs-team
CONTRIBUTING.md @docs-team

# Email-based ownership
sensitive-config.yml <EMAIL>
"#;

        let rules = CodeOwnersParser::parse(content).unwrap();

        // Should have multiple rules
        assert!(!rules.is_empty());

        // Check that security rules are marked as optional
        let security_rule = rules.iter()
            .find(|r| r.pattern == "security/")
            .unwrap();
        assert!(security_rule.is_optional);

        // Check global rule
        let global_rule = rules.iter()
            .find(|r| r.pattern == "*")
            .unwrap();
        assert_eq!(global_rule.owners.len(), 2);
    }

    #[test]
    fn test_owner_parsing() {
        // Test user reference
        let user_owner = CodeOwnersParser::parse_owner("@username").unwrap();
        assert!(matches!(user_owner.owner_type, OwnerType::User(_)));

        // Test team reference
        let team_owner = CodeOwnersParser::parse_owner("@org/team").unwrap();
        assert!(matches!(team_owner.owner_type, OwnerType::Team(_)));

        // Test email reference
        let email_owner = CodeOwnersParser::parse_owner("<EMAIL>").unwrap();
        assert!(matches!(email_owner.owner_type, OwnerType::Email(_)));
    }
}
```

### Intelligent Reviewer Assignment Algorithm

```rust
// src/review/assignment.rs
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc, Duration};
use rand::seq::SliceRandom;

#[derive(Debug, Clone)]
pub struct ReviewerCandidate {
    pub user_id: Uuid,
    pub username: String,
    pub expertise_score: f64,      // 0.0 - 1.0 based on file/language expertise
    pub availability_score: f64,   // 0.0 - 1.0 based on current workload
    pub collaboration_score: f64,  // 0.0 - 1.0 based on past collaboration
    pub is_required: bool,         // Required by CODEOWNERS
}

#[derive(Debug)]
pub struct AssignmentConfig {
    pub min_reviewers: usize,
    pub max_reviewers: usize,
    pub require_codeowners: bool,
    pub balance_workload: bool,
    pub prefer_expertise: bool,
    pub avoid_author_team: bool,
}

impl Default for AssignmentConfig {
    fn default() -> Self {
        Self {
            min_reviewers: 1,
            max_reviewers: 3,
            require_codeowners: true,
            balance_workload: true,
            prefer_expertise: true,
            avoid_author_team: true,
        }
    }
}

pub struct ReviewerAssignmentService {
    db: sqlx::PgPool,
    codeowners_service: CodeOwnersService,
}

impl ReviewerAssignmentService {
    pub fn new(db: sqlx::PgPool, codeowners_service: CodeOwnersService) -> Self {
        Self {
            db,
            codeowners_service,
        }
    }

    /// Assign reviewers to a pull request
    pub async fn assign_reviewers(
        &self,
        pr: &PullRequest,
        changed_files: &[String],
        config: &AssignmentConfig,
    ) -> Result<Vec<Uuid>, AssignmentError> {
        // Get potential reviewers
        let mut candidates = self.get_reviewer_candidates(pr, changed_files).await?;

        // Filter out the author
        candidates.retain(|c| c.user_id != pr.author_id);

        // Filter out author's team members if configured
        if config.avoid_author_team {
            let author_teams = self.get_user_teams(pr.author_id).await?;
            candidates.retain(|c| {
                !self.is_user_in_teams(c.user_id, &author_teams)
            });
        }

        // Separate required and optional reviewers
        let (required, optional): (Vec<_>, Vec<_>) = candidates
            .into_iter()
            .partition(|c| c.is_required);

        let mut selected_reviewers = Vec::new();

        // Add all required reviewers
        for reviewer in required {
            selected_reviewers.push(reviewer.user_id);
        }

        // Add optional reviewers using algorithm
        let additional_needed = config.max_reviewers.saturating_sub(selected_reviewers.len());
        if additional_needed > 0 && !optional.is_empty() {
            let additional = self.select_optimal_reviewers(optional, additional_needed, config).await?;
            selected_reviewers.extend(additional);
        }

        // Ensure minimum reviewers
        if selected_reviewers.len() < config.min_reviewers {
            return Err(AssignmentError::InsufficientReviewers {
                required: config.min_reviewers,
                available: selected_reviewers.len(),
            });
        }

        // Store assignments in database
        self.store_assignments(pr.id, &selected_reviewers).await?;

        Ok(selected_reviewers)
    }

    async fn get_reviewer_candidates(
        &self,
        pr: &PullRequest,
        changed_files: &[String],
    ) -> Result<Vec<ReviewerCandidate>, AssignmentError> {
        let mut candidates = Vec::new();

        // Get CODEOWNERS
        let codeowners = self.codeowners_service
            .get_owners_for_files(pr.repository_id, changed_files)
            .await
            .unwrap_or_default();

        // Get all potential reviewers (repository collaborators)
        let all_reviewers = self.get_repository_collaborators(pr.repository_id).await?;

        for reviewer_id in all_reviewers {
            let expertise_score = self.calculate_expertise_score(reviewer_id, changed_files).await?;
            let availability_score = self.calculate_availability_score(reviewer_id).await?;
            let collaboration_score = self.calculate_collaboration_score(pr.author_id, reviewer_id).await?;
            let is_required = codeowners.contains(&reviewer_id);

            let username = self.get_username(reviewer_id).await?;

            candidates.push(ReviewerCandidate {
                user_id: reviewer_id,
                username,
                expertise_score,
                availability_score,
                collaboration_score,
                is_required,
            });
        }

        Ok(candidates)
    }

    async fn select_optimal_reviewers(
        &self,
        mut candidates: Vec<ReviewerCandidate>,
        count: usize,
        config: &AssignmentConfig,
    ) -> Result<Vec<Uuid>, AssignmentError> {
        // Calculate composite scores
        for candidate in &mut candidates {
            candidate.calculate_composite_score(config);
        }

        // Sort by composite score (descending)
        candidates.sort_by(|a, b| {
            b.get_composite_score()
                .partial_cmp(&a.get_composite_score())
                .unwrap_or(std::cmp::Ordering::Equal)
        });

        // Select top candidates with some randomization to avoid always picking the same people
        let selection_pool_size = (count * 2).min(candidates.len());
        let pool = &candidates[..selection_pool_size];

        let mut rng = rand::thread_rng();
        let selected: Vec<_> = pool
            .choose_multiple(&mut rng, count)
            .map(|c| c.user_id)
            .collect();

        Ok(selected)
    }

    async fn calculate_expertise_score(
        &self,
        reviewer_id: Uuid,
        changed_files: &[String],
    ) -> Result<f64, AssignmentError> {
        // Calculate based on:
        // 1. Previous reviews in similar files
        // 2. Commits in similar files
        // 3. Language/framework expertise

        let mut total_score = 0.0;
        let mut file_count = 0;

        for file_path in changed_files {
            let file_score = self.get_file_expertise_score(reviewer_id, file_path).await?;
            total_score += file_score;
            file_count += 1;
        }

        Ok(if file_count > 0 {
            total_score / file_count as f64
        } else {
            0.0
        })
    }

    async fn calculate_availability_score(&self, reviewer_id: Uuid) -> Result<f64, AssignmentError> {
        // Calculate based on:
        // 1. Current number of pending reviews
        // 2. Recent review activity
        // 3. Time zone alignment

        let pending_reviews = self.get_pending_review_count(reviewer_id).await?;
        let recent_activity = self.get_recent_review_activity(reviewer_id).await?;

        // Higher pending reviews = lower availability
        let workload_factor = 1.0 - (pending_reviews as f64 * 0.1).min(0.8);

        // Recent activity indicates engagement
        let activity_factor = (recent_activity as f64 * 0.1).min(0.5);

        Ok((workload_factor + activity_factor).min(1.0))
    }

    async fn calculate_collaboration_score(
        &self,
        author_id: Uuid,
        reviewer_id: Uuid,
    ) -> Result<f64, AssignmentError> {
        // Calculate based on:
        // 1. Previous successful collaborations
        // 2. Review quality (helpful comments, timely reviews)
        // 3. Conflict resolution history

        let collaboration_history = self.get_collaboration_history(author_id, reviewer_id).await?;

        // Simple scoring based on successful past reviews
        let score = (collaboration_history.successful_reviews as f64 * 0.1)
            .min(1.0)
            .max(0.1); // Minimum score for new collaborations

        Ok(score)
    }

    // Helper methods (simplified implementations)
    async fn get_repository_collaborators(&self, repo_id: Uuid) -> Result<Vec<Uuid>, AssignmentError> {
        let collaborators: Vec<Uuid> = sqlx::query_scalar(
            r#"
            SELECT DISTINCT user_id FROM repository_permissions
            WHERE repository_id = $1 AND permission IN ('write', 'admin')
            "#
        )
        .bind(repo_id)
        .fetch_all(&self.db)
        .await?;

        Ok(collaborators)
    }

    async fn get_file_expertise_score(&self, user_id: Uuid, file_path: &str) -> Result<f64, AssignmentError> {
        // Simplified: count reviews and commits for similar files
        Ok(0.5) // Placeholder
    }

    async fn get_pending_review_count(&self, reviewer_id: Uuid) -> Result<i32, AssignmentError> {
        let count: i64 = sqlx::query_scalar(
            "SELECT COUNT(*) FROM reviews WHERE reviewer_id = $1 AND state = 'pending'"
        )
        .bind(reviewer_id)
        .fetch_one(&self.db)
        .await?;

        Ok(count as i32)
    }

    async fn get_recent_review_activity(&self, reviewer_id: Uuid) -> Result<i32, AssignmentError> {
        let count: i64 = sqlx::query_scalar(
            r#"
            SELECT COUNT(*) FROM reviews
            WHERE reviewer_id = $1 AND submitted_at > NOW() - INTERVAL '30 days'
            "#
        )
        .bind(reviewer_id)
        .fetch_one(&self.db)
        .await?;

        Ok(count as i32)
    }

    async fn get_collaboration_history(
        &self,
        author_id: Uuid,
        reviewer_id: Uuid,
    ) -> Result<CollaborationHistory, AssignmentError> {
        // Simplified implementation
        Ok(CollaborationHistory {
            successful_reviews: 5,
            total_reviews: 7,
            average_review_time_hours: 24,
        })
    }

    async fn get_user_teams(&self, user_id: Uuid) -> Result<Vec<String>, AssignmentError> {
        // Implementation would fetch user's teams
        Ok(Vec::new())
    }

    fn is_user_in_teams(&self, user_id: Uuid, teams: &[String]) -> bool {
        // Implementation would check if user is in any of the teams
        false
    }

    async fn get_username(&self, user_id: Uuid) -> Result<String, AssignmentError> {
        let username: String = sqlx::query_scalar(
            "SELECT username FROM users WHERE id = $1"
        )
        .bind(user_id)
        .fetch_one(&self.db)
        .await?;

        Ok(username)
    }

    async fn store_assignments(&self, pr_id: Uuid, reviewers: &[Uuid]) -> Result<(), AssignmentError> {
        for &reviewer_id in reviewers {
            sqlx::query(
                r#"
                INSERT INTO review_assignments (pull_request_id, reviewer_id, assigned_by, assignment_type)
                VALUES ($1, $2, $3, 'algorithm')
                ON CONFLICT (pull_request_id, reviewer_id) DO NOTHING
                "#
            )
            .bind(pr_id)
            .bind(reviewer_id)
            .bind(Uuid::new_v4()) // System user ID
            .execute(&self.db)
            .await?;
        }

        Ok(())
    }
}

impl ReviewerCandidate {
    fn calculate_composite_score(&mut self, config: &AssignmentConfig) {
        // Weighted combination of scores
        let expertise_weight = if config.prefer_expertise { 0.4 } else { 0.2 };
        let availability_weight = if config.balance_workload { 0.4 } else { 0.2 };
        let collaboration_weight = 0.2;

        self.composite_score = Some(
            self.expertise_score * expertise_weight
                + self.availability_score * availability_weight
                + self.collaboration_score * collaboration_weight
        );
    }

    fn get_composite_score(&self) -> f64 {
        self.composite_score.unwrap_or(0.0)
    }

    composite_score: Option<f64>,
}

#[derive(Debug)]
struct CollaborationHistory {
    successful_reviews: i32,
    total_reviews: i32,
    average_review_time_hours: i32,
}

#[derive(Debug, thiserror::Error)]
pub enum AssignmentError {
    #[error("Insufficient reviewers: need {required}, found {available}")]
    InsufficientReviewers { required: usize, available: usize },
    #[error("Database error: {0}")]
    DatabaseError(#[from] sqlx::Error),
    #[error("CodeOwners error: {0}")]
    CodeOwnersError(#[from] CodeOwnersError),
}
```

## 🎨 Angular Frontend Components

### Pull Request List Component

```typescript
// components/pull-request-list/pull-request-list.component.ts
import { Component, OnInit, Input } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, BehaviorSubject, combineLatest } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import { FormControl } from '@angular/forms';

export interface PullRequestListItem {
  id: string;
  number: number;
  title: string;
  author: {
    username: string;
    avatarUrl?: string;
  };
  state: 'open' | 'closed' | 'merged';
  isDraft: boolean;
  createdAt: Date;
  updatedAt: Date;
  reviewsCount: number;
  commentsCount: number;
  additions: number;
  deletions: number;
  labels: string[];
}

@Component({
  selector: 'app-pull-request-list',
  template: `
    <div class="pull-request-list">
      <div class="filters">
        <mat-form-field appearance="outline">
          <mat-label>Search pull requests</mat-label>
          <input matInput [formControl]="searchControl" placeholder="Search by title, author, or label">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <mat-button-toggle-group [formControl]="stateControl" multiple>
          <mat-button-toggle value="open">Open</mat-button-toggle>
          <mat-button-toggle value="closed">Closed</mat-button-toggle>
          <mat-button-toggle value="merged">Merged</mat-button-toggle>
        </mat-button-toggle-group>

        <mat-form-field appearance="outline">
          <mat-label>Sort by</mat-label>
          <mat-select [formControl]="sortControl">
            <mat-option value="created">Recently created</mat-option>
            <mat-option value="updated">Recently updated</mat-option>
            <mat-option value="comments">Most commented</mat-option>
            <mat-option value="reviews">Most reviewed</mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <div class="pull-requests" *ngIf="filteredPullRequests$ | async as pullRequests">
        <mat-card
          *ngFor="let pr of pullRequests"
          class="pr-card"
          [class.draft]="pr.isDraft"
          (click)="openPullRequest(pr)">

          <mat-card-header>
            <div mat-card-avatar>
              <img [src]="pr.author.avatarUrl || '/assets/default-avatar.png'"
                   [alt]="pr.author.username">
            </div>

            <mat-card-title>
              <div class="pr-title">
                <span class="pr-number">#{{ pr.number }}</span>
                <span class="title">{{ pr.title }}</span>
                <mat-chip-list *ngIf="pr.labels.length > 0">
                  <mat-chip *ngFor="let label of pr.labels"
                           [style.background-color]="getLabelColor(label)">
                    {{ label }}
                  </mat-chip>
                </mat-chip-list>
              </div>
            </mat-card-title>

            <mat-card-subtitle>
              <div class="pr-meta">
                <span class="author">by {{ pr.author.username }}</span>
                <span class="date">{{ pr.createdAt | timeAgo }}</span>
                <mat-icon *ngIf="pr.isDraft" class="draft-icon">edit</mat-icon>
              </div>
            </mat-card-subtitle>
          </mat-card-header>

          <mat-card-content>
            <div class="pr-stats">
              <div class="stat">
                <mat-icon>comment</mat-icon>
                <span>{{ pr.commentsCount }}</span>
              </div>
              <div class="stat">
                <mat-icon>rate_review</mat-icon>
                <span>{{ pr.reviewsCount }}</span>
              </div>
              <div class="stat changes">
                <span class="additions">+{{ pr.additions }}</span>
                <span class="deletions">-{{ pr.deletions }}</span>
              </div>
            </div>
          </mat-card-content>

          <mat-card-actions align="end">
            <div class="pr-state">
              <mat-icon [class]="'state-' + pr.state">
                {{ getStateIcon(pr.state) }}
              </mat-icon>
              <span class="state-text">{{ pr.state | titlecase }}</span>
            </div>
          </mat-card-actions>
        </mat-card>
      </div>

      <div *ngIf="(filteredPullRequests$ | async)?.length === 0" class="no-results">
        <mat-icon>inbox</mat-icon>
        <h3>No pull requests found</h3>
        <p>Try adjusting your search criteria or create a new pull request.</p>
      </div>
    </div>
  `,
  styleUrls: ['./pull-request-list.component.scss']
})
export class PullRequestListComponent implements OnInit {
  @Input() repositoryId!: string;

  searchControl = new FormControl('');
  stateControl = new FormControl(['open']);
  sortControl = new FormControl('updated');

  pullRequests$ = new BehaviorSubject<PullRequestListItem[]>([]);

  filteredPullRequests$ = combineLatest([
    this.pullRequests$,
    this.searchControl.valueChanges.pipe(startWith('')),
    this.stateControl.valueChanges.pipe(startWith(['open'])),
    this.sortControl.valueChanges.pipe(startWith('updated'))
  ]).pipe(
    map(([prs, search, states, sort]) => {
      let filtered = prs;

      // Filter by search
      if (search) {
        const searchLower = search.toLowerCase();
        filtered = filtered.filter(pr =>
          pr.title.toLowerCase().includes(searchLower) ||
          pr.author.username.toLowerCase().includes(searchLower) ||
          pr.labels.some(label => label.toLowerCase().includes(searchLower))
        );
      }

      // Filter by state
      if (states && states.length > 0) {
        filtered = filtered.filter(pr => states.includes(pr.state));
      }

      // Sort
      filtered.sort((a, b) => {
        switch (sort) {
          case 'created':
            return b.createdAt.getTime() - a.createdAt.getTime();
          case 'updated':
            return b.updatedAt.getTime() - a.updatedAt.getTime();
          case 'comments':
            return b.commentsCount - a.commentsCount;
          case 'reviews':
            return b.reviewsCount - a.reviewsCount;
          default:
            return 0;
        }
      });

      return filtered;
    })
  );

  constructor(
    private router: Router,
    private pullRequestService: PullRequestService
  ) {}

  ngOnInit(): void {
    this.loadPullRequests();
  }

  private loadPullRequests(): void {
    this.pullRequestService.getPullRequests(this.repositoryId)
      .subscribe(prs => this.pullRequests$.next(prs));
  }

  openPullRequest(pr: PullRequestListItem): void {
    this.router.navigate(['/repositories', this.repositoryId, 'pull-requests', pr.number]);
  }

  getStateIcon(state: string): string {
    switch (state) {
      case 'open': return 'radio_button_unchecked';
      case 'closed': return 'cancel';
      case 'merged': return 'merge_type';
      default: return 'help';
    }
  }

  getLabelColor(label: string): string {
    // Generate consistent colors based on label name
    const hash = label.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);

    const hue = Math.abs(hash) % 360;
    return `hsl(${hue}, 70%, 80%)`;
  }
}
```

### Pull Request Detail Component

```typescript
// components/pull-request-detail/pull-request-detail.component.ts
import { Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subject, BehaviorSubject, combineLatest } from 'rxjs';
import { takeUntil, switchMap, tap } from 'rxjs/operators';

@Component({
  selector: 'app-pull-request-detail',
  template: `
    <div class="pull-request-detail" *ngIf="pullRequest$ | async as pr">
      <!-- Header -->
      <div class="pr-header">
        <div class="pr-title-section">
          <h1>
            <span class="pr-number">#{{ pr.pullRequest.number }}</span>
            {{ pr.pullRequest.title }}
          </h1>

          <div class="pr-meta">
            <div class="author-info">
              <img [src]="pr.author.avatarUrl || '/assets/default-avatar.png'"
                   [alt]="pr.author.username" class="avatar">
              <span>{{ pr.author.username }}</span>
              <span class="date">opened {{ pr.pullRequest.createdAt | timeAgo }}</span>
            </div>

            <div class="pr-state">
              <mat-chip [class]="'state-' + pr.pullRequest.state">
                <mat-icon>{{ getStateIcon(pr.pullRequest.state) }}</mat-icon>
                {{ pr.pullRequest.state | titlecase }}
              </mat-chip>

              <mat-chip *ngIf="pr.pullRequest.isDraft" class="draft">
                <mat-icon>edit</mat-icon>
                Draft
              </mat-chip>
            </div>
          </div>
        </div>

        <div class="pr-actions" *ngIf="canEdit">
          <button mat-button (click)="editPullRequest()">
            <mat-icon>edit</mat-icon>
            Edit
          </button>

          <button mat-raised-button
                  color="primary"
                  *ngIf="canMerge && pr.mergeable"
                  (click)="mergePullRequest()">
            <mat-icon>merge_type</mat-icon>
            Merge
          </button>

          <button mat-button
                  color="warn"
                  *ngIf="canClose"
                  (click)="closePullRequest()">
            <mat-icon>close</mat-icon>
            Close
          </button>
        </div>
      </div>

      <!-- Tabs -->
      <mat-tab-group [(selectedIndex)]="selectedTab">
        <!-- Conversation Tab -->
        <mat-tab label="Conversation">
          <div class="conversation-tab">
            <!-- Description -->
            <mat-card class="description-card" *ngIf="pr.pullRequest.description">
              <mat-card-content>
                <div [innerHTML]="pr.pullRequest.description | markdown"></div>
              </mat-card-content>
            </mat-card>

            <!-- Reviews and Comments -->
            <div class="timeline">
              <div *ngFor="let item of timeline" class="timeline-item">
                <!-- Review -->
                <div *ngIf="item.type === 'review'" class="review-item">
                  <app-review-item [review]="item.data"></app-review-item>
                </div>

                <!-- Comment -->
                <div *ngIf="item.type === 'comment'" class="comment-item">
                  <app-comment-item [comment]="item.data"></app-comment-item>
                </div>
              </div>
            </div>

            <!-- Add Comment -->
            <mat-card class="add-comment-card" *ngIf="canComment">
              <mat-card-content>
                <form [formGroup]="commentForm" (ngSubmit)="addComment()">
                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Add a comment</mat-label>
                    <textarea matInput
                             formControlName="body"
                             rows="4"
                             placeholder="Leave a comment"></textarea>
                  </mat-form-field>

                  <div class="comment-actions">
                    <button mat-raised-button
                            color="primary"
                            type="submit"
                            [disabled]="commentForm.invalid">
                      Comment
                    </button>
                  </div>
                </form>
              </mat-card-content>
            </mat-card>
          </div>
        </mat-tab>

        <!-- Files Changed Tab -->
        <mat-tab label="Files changed ({{ pr.pullRequest.changedFiles }})">
          <app-file-diff-viewer
            [pullRequestId]="pr.pullRequest.id"
            [sourceCommit]="pr.pullRequest.sourceCommit"
            [targetCommit]="pr.pullRequest.targetCommit"
            (commentAdded)="onInlineCommentAdded($event)">
          </app-file-diff-viewer>
        </mat-tab>

        <!-- Commits Tab -->
        <mat-tab label="Commits ({{ pr.pullRequest.commitsCount }})">
          <app-commit-list
            [pullRequestId]="pr.pullRequest.id"
            [sourceCommit]="pr.pullRequest.sourceCommit"
            [targetCommit]="pr.pullRequest.targetCommit">
          </app-commit-list>
        </mat-tab>
      </mat-tab-group>

      <!-- Sidebar -->
      <div class="pr-sidebar">
        <!-- Reviewers -->
        <div class="sidebar-section">
          <h3>Reviewers</h3>
          <div class="reviewers">
            <div *ngFor="let review of pr.reviews" class="reviewer">
              <img [src]="review.reviewer.avatarUrl || '/assets/default-avatar.png'"
                   [alt]="review.reviewer.username" class="avatar-small">
              <span class="username">{{ review.reviewer.username }}</span>
              <mat-icon [class]="'review-state-' + review.review.state">
                {{ getReviewStateIcon(review.review.state) }}
              </mat-icon>
            </div>
          </div>

          <button mat-button
                  *ngIf="canAssignReviewers"
                  (click)="assignReviewers()">
            <mat-icon>person_add</mat-icon>
            Request review
          </button>
        </div>

        <!-- Assignees -->
        <div class="sidebar-section">
          <h3>Assignees</h3>
          <div class="assignees">
            <div *ngFor="let assignee of pr.assignees" class="assignee">
              <img [src]="assignee.avatarUrl || '/assets/default-avatar.png'"
                   [alt]="assignee.username" class="avatar-small">
              <span class="username">{{ assignee.username }}</span>
            </div>
          </div>
        </div>

        <!-- Labels -->
        <div class="sidebar-section">
          <h3>Labels</h3>
          <mat-chip-list>
            <mat-chip *ngFor="let label of pr.labels"
                     [style.background-color]="getLabelColor(label)">
              {{ label }}
            </mat-chip>
          </mat-chip-list>
        </div>

        <!-- Merge Status -->
        <div class="sidebar-section">
          <h3>Merge Status</h3>
          <div class="merge-status">
            <div class="status-item"
                 [class.success]="pr.mergeable"
                 [class.error]="!pr.mergeable">
              <mat-icon>{{ pr.mergeable ? 'check_circle' : 'error' }}</mat-icon>
              <span>{{ pr.mergeable ? 'Ready to merge' : 'Merge conflicts' }}</span>
            </div>

            <div *ngIf="!pr.mergeable" class="conflicts">
              <p>This branch has conflicts that must be resolved:</p>
              <ul>
                <li *ngFor="let conflict of pr.mergeConflicts">{{ conflict }}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./pull-request-detail.component.scss']
})
export class PullRequestDetailComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  pullRequest$ = new BehaviorSubject<PullRequestResponse | null>(null);
  timeline: TimelineItem[] = [];
  selectedTab = 0;

  commentForm: FormGroup;

  canEdit = false;
  canMerge = false;
  canClose = false;
  canComment = true;
  canAssignReviewers = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private fb: FormBuilder,
    private pullRequestService: PullRequestService,
    private authService: AuthService
  ) {
    this.commentForm = this.fb.group({
      body: ['', [Validators.required, Validators.minLength(1)]]
    });
  }

  ngOnInit(): void {
    this.route.params.pipe(
      switchMap(params => {
        const repositoryId = params['repositoryId'];
        const prNumber = params['prNumber'];
        return this.pullRequestService.getPullRequestDetails(repositoryId, prNumber);
      }),
      tap(pr => {
        this.updatePermissions(pr);
        this.buildTimeline(pr);
      }),
      takeUntil(this.destroy$)
    ).subscribe(pr => {
      this.pullRequest$.next(pr);
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private updatePermissions(pr: PullRequestResponse): void {
    const currentUser = this.authService.getCurrentUser();
    if (!currentUser) return;

    this.canEdit = pr.pullRequest.authorId === currentUser.id;
    this.canMerge = pr.mergeable && pr.pullRequest.state === 'open';
    this.canClose = pr.pullRequest.state === 'open';
    this.canAssignReviewers = true; // Based on repository permissions
  }

  private buildTimeline(pr: PullRequestResponse): void {
    this.timeline = [];

    // Add reviews and comments to timeline
    for (const review of pr.reviews) {
      this.timeline.push({
        type: 'review',
        data: review,
        timestamp: review.review.submittedAt || review.review.createdAt
      });

      // Add review comments
      for (const comment of review.comments) {
        this.timeline.push({
          type: 'comment',
          data: comment,
          timestamp: comment.comment.createdAt
        });
      }
    }

    // Sort by timestamp
    this.timeline.sort((a, b) =>
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
  }

  addComment(): void {
    if (this.commentForm.valid) {
      const body = this.commentForm.get('body')?.value;
      const pr = this.pullRequest$.value;

      if (pr) {
        this.pullRequestService.addComment(pr.pullRequest.id, body)
          .subscribe(() => {
            this.commentForm.reset();
            // Reload PR details
            this.ngOnInit();
          });
      }
    }
  }

  mergePullRequest(): void {
    const pr = this.pullRequest$.value;
    if (pr) {
      this.pullRequestService.mergePullRequest(pr.pullRequest.id, 'merge')
        .subscribe(() => {
          // Reload PR details
          this.ngOnInit();
        });
    }
  }

  closePullRequest(): void {
    const pr = this.pullRequest$.value;
    if (pr) {
      this.pullRequestService.closePullRequest(pr.pullRequest.id)
        .subscribe(() => {
          // Reload PR details
          this.ngOnInit();
        });
    }
  }

  editPullRequest(): void {
    // Open edit dialog or navigate to edit page
  }

  assignReviewers(): void {
    // Open reviewer assignment dialog
  }

  onInlineCommentAdded(event: any): void {
    // Reload PR details to show new comment
    this.ngOnInit();
  }

  getStateIcon(state: string): string {
    switch (state) {
      case 'open': return 'radio_button_unchecked';
      case 'closed': return 'cancel';
      case 'merged': return 'merge_type';
      default: return 'help';
    }
  }

  getReviewStateIcon(state: string): string {
    switch (state) {
      case 'approved': return 'check_circle';
      case 'changes_requested': return 'cancel';
      case 'commented': return 'comment';
      case 'pending': return 'schedule';
      default: return 'help';
    }
  }

  getLabelColor(label: string): string {
    const hash = label.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);

    const hue = Math.abs(hash) % 360;
    return `hsl(${hue}, 70%, 80%)`;
  }
}

interface TimelineItem {
  type: 'review' | 'comment';
  data: any;
  timestamp: Date;
}
```

## 🎯 Key Takeaways

### Code Review System Benefits

1. **Quality Assurance**: Systematic code review catches bugs and design issues early
2. **Knowledge Sharing**: Reviews spread domain knowledge across the team
3. **Mentorship**: Senior developers can guide junior team members
4. **Compliance**: Meet regulatory requirements for code changes
5. **Documentation**: Create a searchable history of technical decisions

### Advanced Features Implemented

- **CODEOWNERS Integration**: Automatic reviewer assignment based on file ownership
- **Intelligent Assignment**: Load balancing and expertise-based reviewer selection
- **Inline Comments**: Line-by-line code discussions with threading
- **Review States**: Comprehensive approval workflow with change requests
- **Draft PRs**: Work-in-progress collaboration before formal review
- **Merge Strategies**: Support for merge commits, squash, and rebase

### Performance Considerations

- **Database Indexing**: Optimized queries for PR lists and search
- **Caching**: Cache CODEOWNERS parsing and user permissions
- **Pagination**: Handle large numbers of PRs and comments efficiently
- **Real-time Updates**: WebSocket integration for live comment updates

### Security Best Practices

- **Permission Checks**: Verify user permissions for all operations
- **Input Validation**: Sanitize all user inputs and comments
- **Rate Limiting**: Prevent spam comments and review requests
- **Audit Logging**: Track all review activities for compliance

Ready to continue with [Module 11: Issue Tracking & Project Management](./module-11-issue-tracking.md)?
