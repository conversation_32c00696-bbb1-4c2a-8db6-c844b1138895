{"version": 0, "next_id": 3, "reports": [{"id": 1, "suggestion_message": "\nTo solve this problem, you can try the following approaches:\n\n\n- Some affected dependencies have newer versions available.\nYou may want to consider updating them to a newer version to see if the issue has been fixed.\n\nsqlx-postgres v0.7.4 has the following newer versions available: 0.8.0, 0.8.1, 0.8.2, 0.8.3, 0.8.5, 0.8.6\n\n- If the issue is not solved by updating the dependencies, a fix has to be\nimplemented by those dependencies. You can help with that by notifying the\nmaintainers of this problem (e.g. by creating a bug report) or by proposing a\nfix to the maintainers (e.g. by creating a pull request):\n\n  - sqlx-postgres@0.7.4\n  - Repository: https://github.com/launchbadge/sqlx\n  - Detailed warning command: `cargo report future-incompatibilities --id 1 --package sqlx-postgres@0.7.4`\n\n- If waiting for an upstream fix is not an option, you can use the `[patch]`\nsection in `Cargo.toml` to use your own version of the dependency. For more\ninformation, see:\nhttps://doc.rust-lang.org/cargo/reference/overriding-dependencies.html#the-patch-section\n", "per_package": {"sqlx-postgres@0.7.4": "The package `sqlx-postgres v0.7.4` currently triggers the following future incompatibility lints:\n> \u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this function depends on never type fallback being `()`\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/connection/executor.rs:23:1\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m/\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn prepare(\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    conn: &mut PgConnection,\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    sql: &str,\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    parameters: &[PgTypeInfo],\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    metadata: Option<Arc<PgStatementMetadata>>,\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m) -> Result<(Oid, Arc<PgStatementMetadata>), Error> {\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|___________________________________________________^\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: specify the types explicitly\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: in edition 2024, the requirement `!: sqlx_core::io::Decode<'_>` will fail\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/connection/executor.rs:68:10\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m68\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .recv_expect(MessageFormat::ParseComplete)\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `()` annotations to avoid fallback changes\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m66\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m    let _\u001b[0m\u001b[0m\u001b[38;5;10m: ()\u001b[0m\u001b[0m = conn\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[38;5;10m++++\u001b[0m\n> \n> \u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this function depends on never type fallback being `()`\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs:262:5\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m262\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn abort(mut self, msg: impl Into<String>) -> Result<()> {\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: specify the types explicitly\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: in edition 2024, the requirement `!: sqlx_core::io::Decode<'_>` will fail\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs:280:30\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m280\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m                   .recv_expect(MessageFormat::ReadyForQuery)\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `()` annotations to avoid fallback changes\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m280\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m                            .recv_expect\u001b[0m\u001b[0m\u001b[38;5;10m::<()>\u001b[0m\u001b[0m(MessageFormat::ReadyForQuery)\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[38;5;10m++++++\u001b[0m\n> \n> \u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this function depends on never type fallback being `()`\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs:294:5\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m294\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn finish(mut self) -> Result<u64> {\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: specify the types explicitly\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: in edition 2024, the requirement `!: sqlx_core::io::Decode<'_>` will fail\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs:314:14\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m314\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .recv_expect(MessageFormat::ReadyForQuery)\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `()` annotations to avoid fallback changes\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m314\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m            .recv_expect\u001b[0m\u001b[0m\u001b[38;5;10m::<()>\u001b[0m\u001b[0m(MessageFormat::ReadyForQuery)\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[38;5;10m++++++\u001b[0m\n> \n> \u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this function depends on never type fallback being `()`\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs:331:1\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m331\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m/\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn pg_begin_copy_out<'c, C: DerefMut<Target = PgConnection> + Send + 'c>(\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m332\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    mut conn: C,\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m333\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    statement: &str,\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m334\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m) -> Result<BoxStream<'c, Result<Bytes>>> {\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|_________________________________________^\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: specify the types explicitly\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: in edition 2024, the requirement `!: sqlx_core::io::Decode<'_>` will fail\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs:350:33\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m350\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    conn.stream.recv_expect(MessageFormat::CommandComplete).await?;\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `()` annotations to avoid fallback changes\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m350\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m                    conn.stream.recv_expect\u001b[0m\u001b[0m\u001b[38;5;10m::<()>\u001b[0m\u001b[0m(MessageFormat::CommandComplete).await?;\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m351\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m                    conn.stream.recv_expect\u001b[0m\u001b[0m\u001b[38;5;10m::<()>\u001b[0m\u001b[0m(MessageFormat::ReadyForQuery).await?;\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \nThe package `sqlx-postgres v0.7.4` currently triggers the following future incompatibility lints:\n> \u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this function depends on never type fallback being `()`\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/connection/executor.rs:23:1\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m/\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn prepare(\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    conn: &mut PgConnection,\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    sql: &str,\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    parameters: &[PgTypeInfo],\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    metadata: Option<Arc<PgStatementMetadata>>,\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m) -> Result<(Oid, Arc<PgStatementMetadata>), Error> {\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|___________________________________________________^\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: specify the types explicitly\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: in edition 2024, the requirement `!: sqlx_core::io::Decode<'_>` will fail\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/connection/executor.rs:68:10\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m68\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .recv_expect(MessageFormat::ParseComplete)\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `()` annotations to avoid fallback changes\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m66\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m    let _\u001b[0m\u001b[0m\u001b[38;5;10m: ()\u001b[0m\u001b[0m = conn\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[38;5;10m++++\u001b[0m\n> \n> \u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this function depends on never type fallback being `()`\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs:262:5\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m262\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn abort(mut self, msg: impl Into<String>) -> Result<()> {\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: specify the types explicitly\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: in edition 2024, the requirement `!: sqlx_core::io::Decode<'_>` will fail\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs:280:30\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m280\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m                   .recv_expect(MessageFormat::ReadyForQuery)\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `()` annotations to avoid fallback changes\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m280\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m                            .recv_expect\u001b[0m\u001b[0m\u001b[38;5;10m::<()>\u001b[0m\u001b[0m(MessageFormat::ReadyForQuery)\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[38;5;10m++++++\u001b[0m\n> \n> \u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this function depends on never type fallback being `()`\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs:294:5\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m294\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn finish(mut self) -> Result<u64> {\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: specify the types explicitly\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: in edition 2024, the requirement `!: sqlx_core::io::Decode<'_>` will fail\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs:314:14\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m314\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .recv_expect(MessageFormat::ReadyForQuery)\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `()` annotations to avoid fallback changes\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m314\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m            .recv_expect\u001b[0m\u001b[0m\u001b[38;5;10m::<()>\u001b[0m\u001b[0m(MessageFormat::ReadyForQuery)\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[38;5;10m++++++\u001b[0m\n> \n> \u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this function depends on never type fallback being `()`\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs:331:1\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m331\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m/\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn pg_begin_copy_out<'c, C: DerefMut<Target = PgConnection> + Send + 'c>(\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m332\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    mut conn: C,\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m333\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    statement: &str,\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m334\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m) -> Result<BoxStream<'c, Result<Bytes>>> {\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|_________________________________________^\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: specify the types explicitly\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: in edition 2024, the requirement `!: sqlx_core::io::Decode<'_>` will fail\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs:350:33\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m350\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    conn.stream.recv_expect(MessageFormat::CommandComplete).await?;\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `()` annotations to avoid fallback changes\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m350\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m                    conn.stream.recv_expect\u001b[0m\u001b[0m\u001b[38;5;10m::<()>\u001b[0m\u001b[0m(MessageFormat::CommandComplete).await?;\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m351\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m                    conn.stream.recv_expect\u001b[0m\u001b[0m\u001b[38;5;10m::<()>\u001b[0m\u001b[0m(MessageFormat::ReadyForQuery).await?;\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \n"}}, {"id": 2, "suggestion_message": "\nTo solve this problem, you can try the following approaches:\n\n\n- Some affected dependencies have newer versions available.\nYou may want to consider updating them to a newer version to see if the issue has been fixed.\n\nsqlx-postgres v0.7.4 has the following newer versions available: 0.8.0, 0.8.1, 0.8.2, 0.8.3, 0.8.5, 0.8.6\n\n- If the issue is not solved by updating the dependencies, a fix has to be\nimplemented by those dependencies. You can help with that by notifying the\nmaintainers of this problem (e.g. by creating a bug report) or by proposing a\nfix to the maintainers (e.g. by creating a pull request):\n\n  - sqlx-postgres@0.7.4\n  - Repository: https://github.com/launchbadge/sqlx\n  - Detailed warning command: `cargo report future-incompatibilities --id 2 --package sqlx-postgres@0.7.4`\n\n- If waiting for an upstream fix is not an option, you can use the `[patch]`\nsection in `Cargo.toml` to use your own version of the dependency. For more\ninformation, see:\nhttps://doc.rust-lang.org/cargo/reference/overriding-dependencies.html#the-patch-section\n", "per_package": {"sqlx-postgres@0.7.4": "The package `sqlx-postgres v0.7.4` currently triggers the following future incompatibility lints:\n> \u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this function depends on never type fallback being `()`\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/connection/executor.rs:23:1\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m/\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn prepare(\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    conn: &mut PgConnection,\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    sql: &str,\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    parameters: &[PgTypeInfo],\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    metadata: Option<Arc<PgStatementMetadata>>,\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m) -> Result<(Oid, Arc<PgStatementMetadata>), Error> {\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|___________________________________________________^\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: specify the types explicitly\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: in edition 2024, the requirement `!: sqlx_core::io::Decode<'_>` will fail\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/connection/executor.rs:68:10\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m68\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .recv_expect(MessageFormat::ParseComplete)\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `()` annotations to avoid fallback changes\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m66\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m    let _\u001b[0m\u001b[0m\u001b[38;5;10m: ()\u001b[0m\u001b[0m = conn\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[38;5;10m++++\u001b[0m\n> \n> \u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this function depends on never type fallback being `()`\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs:262:5\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m262\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn abort(mut self, msg: impl Into<String>) -> Result<()> {\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: specify the types explicitly\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: in edition 2024, the requirement `!: sqlx_core::io::Decode<'_>` will fail\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs:280:30\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m280\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m                   .recv_expect(MessageFormat::ReadyForQuery)\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `()` annotations to avoid fallback changes\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m280\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m                            .recv_expect\u001b[0m\u001b[0m\u001b[38;5;10m::<()>\u001b[0m\u001b[0m(MessageFormat::ReadyForQuery)\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[38;5;10m++++++\u001b[0m\n> \n> \u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this function depends on never type fallback being `()`\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs:294:5\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m294\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn finish(mut self) -> Result<u64> {\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: specify the types explicitly\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: in edition 2024, the requirement `!: sqlx_core::io::Decode<'_>` will fail\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs:314:14\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m314\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .recv_expect(MessageFormat::ReadyForQuery)\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `()` annotations to avoid fallback changes\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m314\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m            .recv_expect\u001b[0m\u001b[0m\u001b[38;5;10m::<()>\u001b[0m\u001b[0m(MessageFormat::ReadyForQuery)\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[38;5;10m++++++\u001b[0m\n> \n> \u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this function depends on never type fallback being `()`\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs:331:1\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m331\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m/\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn pg_begin_copy_out<'c, C: DerefMut<Target = PgConnection> + Send + 'c>(\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m332\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    mut conn: C,\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m333\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    statement: &str,\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m334\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m) -> Result<BoxStream<'c, Result<Bytes>>> {\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|_________________________________________^\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: specify the types explicitly\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: in edition 2024, the requirement `!: sqlx_core::io::Decode<'_>` will fail\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs:350:33\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m350\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    conn.stream.recv_expect(MessageFormat::CommandComplete).await?;\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `()` annotations to avoid fallback changes\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m350\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m                    conn.stream.recv_expect\u001b[0m\u001b[0m\u001b[38;5;10m::<()>\u001b[0m\u001b[0m(MessageFormat::CommandComplete).await?;\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m351\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m                    conn.stream.recv_expect\u001b[0m\u001b[0m\u001b[38;5;10m::<()>\u001b[0m\u001b[0m(MessageFormat::ReadyForQuery).await?;\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \n"}}]}