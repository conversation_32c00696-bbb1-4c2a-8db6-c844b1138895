use super::{Auth<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, jwt::JwtManager};
use axum::{
    extract::{Request, State},
    http::{header::AUTHORIZATION, StatusCode},
    middleware::Next,
    response::Response,
};
use std::sync::Arc;

#[derive(Clone)]
pub struct AuthState {
    pub jwt_manager: Arc<JwtManager>,
}

impl AuthState {
    pub fn new(jwt_manager: JwtManager) -> Self {
        Self {
            jwt_manager: Arc::new(jwt_manager),
        }
    }
}

pub async fn auth_middleware(
    State(auth_state): State<AuthState>,
    mut request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // Extract Authorization header
    let auth_header = request
        .headers()
        .get(AUTHORIZATION)
        .and_then(|header| header.to_str().ok())
        .ok_or(StatusCode::UNAUTHORIZED)?;

    // Check for Bearer token format
    if !auth_header.starts_with("Bearer ") {
        return Err(StatusCode::UNAUTHORIZED);
    }

    let token = &auth_header[7..]; // Remove "Bearer " prefix

    // Verify token
    let claims = auth_state
        .jwt_manager
        .verify_token(token)
        .map_err(|_| StatusCode::UNAUTHORIZED)?;

    // Add claims to request extensions for use in handlers
    request.extensions_mut().insert(claims);

    Ok(next.run(request).await)
}

// Helper to extract claims from request
pub fn extract_claims(request: &Request) -> Result<&Claims, AuthError> {
    request
        .extensions()
        .get::<Claims>()
        .ok_or(AuthError::InvalidToken)
}

// Optional auth middleware - doesn't fail if no token provided
pub async fn optional_auth_middleware(
    State(auth_state): State<AuthState>,
    mut request: Request,
    next: Next,
) -> Response {
    // Try to extract and verify token, but don't fail if missing
    if let Some(auth_header) = request
        .headers()
        .get(AUTHORIZATION)
        .and_then(|header| header.to_str().ok())
    {
        if auth_header.starts_with("Bearer ") {
            let token = &auth_header[7..];
            if let Ok(claims) = auth_state.jwt_manager.verify_token(token) {
                request.extensions_mut().insert(claims);
            }
        }
    }

    next.run(request).await
}

// Note: Middleware tests require more complex setup with tower-test
// For now, we'll test the JWT functionality directly in jwt.rs
// Integration tests will be added in a separate test file
