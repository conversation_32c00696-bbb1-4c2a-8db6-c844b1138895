use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::{FromRow, PgPool};
use thiserror::Error;
use uuid::Uuid;

pub mod jwt;
pub mod middleware;

#[derive(Error, Debug)]
pub enum AuthError {
    #[error("Invalid credentials")]
    InvalidCredentials,
    #[error("Token expired")]
    TokenExpired,
    #[error("Invalid token")]
    InvalidToken,
    #[error("User not found")]
    UserNotFound,
    #[error("User already exists")]
    UserAlreadyExists,
    #[error("Database error: {0}")]
    DatabaseError(#[from] sqlx::Error),
    #[error("Password hashing error: {0}")]
    HashError(#[from] bcrypt::BcryptError),
    #[error("JWT error: {0}")]
    JwtError(#[from] jsonwebtoken::errors::Error),
}

pub type AuthResult<T> = Result<T, AuthError>;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct User {
    pub id: Uuid,
    pub username: String,
    pub email: String,
    pub password_hash: String,
    pub role: UserRole,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub is_active: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "user_role", rename_all = "lowercase")]
pub enum UserRole {
    Admin,
    User,
    ReadOnly,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateUserRequest {
    pub username: String,
    pub email: String,
    pub password: String,
    pub role: Option<UserRole>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LoginResponse {
    pub token: String,
    pub user: UserInfo,
    pub expires_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserInfo {
    pub id: Uuid,
    pub username: String,
    pub email: String,
    pub role: UserRole,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,      // Subject (user ID)
    pub username: String,
    pub role: UserRole,
    pub exp: usize,       // Expiration time
    pub iat: usize,       // Issued at
}

impl From<User> for UserInfo {
    fn from(user: User) -> Self {
        Self {
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role,
        }
    }
}

// Password hashing utilities
pub fn hash_password(password: &str) -> AuthResult<String> {
    bcrypt::hash(password, bcrypt::DEFAULT_COST).map_err(AuthError::HashError)
}

pub fn verify_password(password: &str, hash: &str) -> AuthResult<bool> {
    bcrypt::verify(password, hash).map_err(AuthError::HashError)
}

// User service for database operations
pub struct UserService {
    pool: PgPool,
}

impl UserService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn create_user(&self, request: CreateUserRequest) -> AuthResult<User> {
        // Check if user already exists
        let existing = sqlx::query_as::<_, User>(
            "SELECT * FROM users WHERE username = $1 OR email = $2"
        )
        .bind(&request.username)
        .bind(&request.email)
        .fetch_optional(&self.pool)
        .await?;

        if existing.is_some() {
            return Err(AuthError::UserAlreadyExists);
        }

        // Hash password
        let password_hash = hash_password(&request.password)?;
        let role = request.role.unwrap_or(UserRole::User);
        let now = Utc::now();

        // Insert user
        let user = sqlx::query_as::<_, User>(
            r#"
            INSERT INTO users (id, username, email, password_hash, role, created_at, updated_at, is_active)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING *
            "#
        )
        .bind(Uuid::new_v4())
        .bind(&request.username)
        .bind(&request.email)
        .bind(&password_hash)
        .bind(&role)
        .bind(now)
        .bind(now)
        .bind(true)
        .fetch_one(&self.pool)
        .await?;

        Ok(user)
    }

    pub async fn authenticate_user(&self, request: LoginRequest) -> AuthResult<User> {
        let user = sqlx::query_as::<_, User>(
            "SELECT * FROM users WHERE username = $1 AND is_active = true"
        )
        .bind(&request.username)
        .fetch_optional(&self.pool)
        .await?
        .ok_or(AuthError::InvalidCredentials)?;

        if verify_password(&request.password, &user.password_hash)? {
            Ok(user)
        } else {
            Err(AuthError::InvalidCredentials)
        }
    }

    pub async fn get_user_by_id(&self, user_id: Uuid) -> AuthResult<User> {
        let user = sqlx::query_as::<_, User>(
            "SELECT * FROM users WHERE id = $1 AND is_active = true"
        )
        .bind(user_id)
        .fetch_optional(&self.pool)
        .await?
        .ok_or(AuthError::UserNotFound)?;

        Ok(user)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_password_hashing() {
        let password = "secure_password_123";
        let hash = hash_password(password).unwrap();
        
        // Hash should be different from password
        assert_ne!(hash, password);
        
        // Should verify correctly
        assert!(verify_password(password, &hash).unwrap());
        
        // Should fail with wrong password
        assert!(!verify_password("wrong_password", &hash).unwrap());
    }

    #[test]
    fn test_password_hash_uniqueness() {
        let password = "same_password";
        let hash1 = hash_password(password).unwrap();
        let hash2 = hash_password(password).unwrap();
        
        // Same password should produce different hashes (due to salt)
        assert_ne!(hash1, hash2);
        
        // But both should verify correctly
        assert!(verify_password(password, &hash1).unwrap());
        assert!(verify_password(password, &hash2).unwrap());
    }

    #[test]
    fn test_user_role_serialization() {
        let user = User {
            id: Uuid::new_v4(),
            username: "testuser".to_string(),
            email: "<EMAIL>".to_string(),
            password_hash: "hash".to_string(),
            role: UserRole::Admin,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            is_active: true,
        };

        let json = serde_json::to_string(&user).unwrap();
        let deserialized: User = serde_json::from_str(&json).unwrap();
        
        assert_eq!(user.username, deserialized.username);
        assert!(matches!(deserialized.role, UserRole::Admin));
    }

    #[test]
    fn test_user_info_conversion() {
        let user = User {
            id: Uuid::new_v4(),
            username: "testuser".to_string(),
            email: "<EMAIL>".to_string(),
            password_hash: "hash".to_string(),
            role: UserRole::User,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            is_active: true,
        };

        let user_info: UserInfo = user.clone().into();
        assert_eq!(user_info.id, user.id);
        assert_eq!(user_info.username, user.username);
        assert_eq!(user_info.email, user.email);
        assert!(matches!(user_info.role, UserRole::User));
    }
}
