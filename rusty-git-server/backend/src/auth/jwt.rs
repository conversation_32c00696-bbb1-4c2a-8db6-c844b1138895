use super::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, User<PERSON><PERSON>};
use chrono::{Duration, Utc};
use jsonwebtoken::{decode, encode, Decoding<PERSON><PERSON>, Enco<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Validation};
use uuid::Uuid;

pub struct JwtManager {
    encoding_key: Encoding<PERSON>ey,
    decoding_key: Decoding<PERSON><PERSON>,
    validation: Validation,
}

impl JwtManager {
    pub fn new(secret: &str) -> Self {
        Self {
            encoding_key: EncodingKey::from_secret(secret.as_ref()),
            decoding_key: DecodingKey::from_secret(secret.as_ref()),
            validation: Validation::default(),
        }
    }

    pub fn generate_token(
        &self,
        user_id: Uuid,
        username: String,
        role: UserRole,
    ) -> AuthResult<(String, chrono::DateTime<Utc>)> {
        let now = Utc::now();
        let expires_at = now + Duration::hours(24); // Token valid for 24 hours
        
        let claims = Claims {
            sub: user_id.to_string(),
            username,
            role,
            exp: expires_at.timestamp() as usize,
            iat: now.timestamp() as usize,
        };

        let token = encode(&Header::default(), &claims, &self.encoding_key)?;
        Ok((token, expires_at))
    }

    pub fn verify_token(&self, token: &str) -> AuthResult<Claims> {
        let token_data = decode::<Claims>(token, &self.decoding_key, &self.validation)?;
        Ok(token_data.claims)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_jwt_roundtrip() {
        let jwt_manager = JwtManager::new("test_secret_key_that_is_long_enough");
        let user_id = Uuid::new_v4();
        let username = "testuser".to_string();
        let role = UserRole::User;

        // Generate token
        let (token, _expires_at) = jwt_manager
            .generate_token(user_id, username.clone(), role.clone())
            .unwrap();

        // Verify token
        let claims = jwt_manager.verify_token(&token).unwrap();
        
        assert_eq!(claims.sub, user_id.to_string());
        assert_eq!(claims.username, username);
        assert!(matches!(claims.role, UserRole::User));
    }

    #[test]
    fn test_invalid_token() {
        let jwt_manager = JwtManager::new("test_secret_key_that_is_long_enough");
        let result = jwt_manager.verify_token("invalid.token.here");
        assert!(result.is_err());
    }

    #[test]
    fn test_token_expiration() {
        let jwt_manager = JwtManager::new("test_secret_key_that_is_long_enough");
        let user_id = Uuid::new_v4();
        let username = "testuser".to_string();
        let role = UserRole::User;

        let (token, expires_at) = jwt_manager
            .generate_token(user_id, username, role)
            .unwrap();

        // Token should be valid now
        assert!(jwt_manager.verify_token(&token).is_ok());
        
        // Expires at should be in the future
        assert!(expires_at > Utc::now());
    }

    #[test]
    fn test_different_secrets_fail() {
        let jwt_manager1 = JwtManager::new("secret1");
        let jwt_manager2 = JwtManager::new("secret2");
        
        let user_id = Uuid::new_v4();
        let (token, _) = jwt_manager1
            .generate_token(user_id, "user".to_string(), UserRole::User)
            .unwrap();

        // Token created with secret1 should not verify with secret2
        assert!(jwt_manager2.verify_token(&token).is_err());
    }
}
