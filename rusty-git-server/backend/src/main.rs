use axum::{
    extract::Path,
    http::StatusCode,
    response::<PERSON><PERSON>,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

mod git;
mod auth;

// This is like a TypeScript interface
#[derive(Serialize, Deserialize, Debug, Clone)]
struct Repository {
    id: u32,
    name: String,
    description: Option<String>,
    created_at: String,
}

// Global state (we'll improve this later)
type AppState = HashMap<u32, Repository>;

#[tokio::main]
async fn main() {
    // Initialize our "database" (in-memory for now)
    let mut repos = HashMap::new();
    repos.insert(1, Repository {
        id: 1,
        name: "hello-world".to_string(),
        description: Some("My first repository".to_string()),
        created_at: "2024-01-01T00:00:00Z".to_string(),
    });

    // Build our application with routes
    let app = Router::new()
        .route("/", get(root))
        .route("/repositories", get(list_repositories))
        .route("/repositories/:id", get(get_repository))
        .with_state(repos);

    // Start the server
    let listener = tokio::net::TcpListener::bind("0.0.0.0:3000")
        .await
        .unwrap();

    println!("🚀 Server running on http://localhost:3000");
    axum::serve(listener, app).await.unwrap();
}

// Route handlers
async fn root() -> &'static str {
    "Welcome to Rusty Git Server! 🦀"
}

async fn list_repositories(
    axum::extract::State(repos): axum::extract::State<AppState>
) -> Json<Vec<Repository>> {
    let repo_list: Vec<Repository> = repos.values().cloned().collect();
    Json(repo_list)
}

async fn get_repository(
    Path(id): Path<u32>,
    axum::extract::State(repos): axum::extract::State<AppState>
) -> Result<Json<Repository>, StatusCode> {
    match repos.get(&id) {
        Some(repo) => Ok(Json(repo.clone())),
        None => Err(StatusCode::NOT_FOUND),
    }
}
