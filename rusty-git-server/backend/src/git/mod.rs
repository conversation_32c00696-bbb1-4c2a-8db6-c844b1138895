use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sha1::{Digest, Sha1};
use thiserror::Error;

#[derive(Error, Debug)]
pub enum GitError {
    #[error("Object not found: {hash}")]
    ObjectNotFound { hash: String },
    #[error("Invalid object type: {object_type}")]
    InvalidObjectType { object_type: String },
    #[error("Compression error: {0}")]
    CompressionError(#[from] std::io::Error),
    #[error("Serialization error: {0}")]
    SerializationError(String),
}

pub type GitResult<T> = Result<T, GitError>;

/// Git object types - this is an enum (like a union in C# but type-safe)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum GitObject {
    Blob(BlobObject),
    Tree(TreeObject),
    Commit(CommitObject),
    Tag(TagObject),
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct BlobObject {
    pub content: Vec<u8>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TreeEntry {
    pub mode: String,      // File permissions (e.g., "100644", "040000")
    pub name: String,      // File/directory name
    pub hash: String,      // SHA-1 hash of the object
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TreeObject {
    pub entries: Vec<TreeEntry>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommitObject {
    pub tree: String,                    // Hash of the tree object
    pub parents: Vec<String>,            // Parent commit hashes
    pub author: GitSignature,
    pub committer: GitSignature,
    pub message: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitSignature {
    pub name: String,
    pub email: String,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TagObject {
    pub object: String,      // Hash of the tagged object
    pub object_type: String, // Type of the tagged object
    pub tag: String,         // Tag name
    pub tagger: GitSignature,
    pub message: String,
}

impl GitObject {
    /// Calculate the SHA-1 hash of this object (like Git does)
    pub fn hash(&self) -> GitResult<String> {
        let content = self.serialize()?;
        let mut hasher = Sha1::new();
        hasher.update(&content);
        Ok(hex::encode(hasher.finalize()))
    }
    
    /// Serialize object to Git's internal format
    pub fn serialize(&self) -> GitResult<Vec<u8>> {
        match self {
            GitObject::Blob(blob) => {
                let header = format!("blob {}\0", blob.content.len());
                let mut result = header.into_bytes();
                result.extend_from_slice(&blob.content);
                Ok(result)
            }
            GitObject::Tree(tree) => {
                let mut content = Vec::new();
                for entry in &tree.entries {
                    content.extend_from_slice(entry.mode.as_bytes());
                    content.push(b' ');
                    content.extend_from_slice(entry.name.as_bytes());
                    content.push(b'\0');
                    // Convert hex hash to binary
                    let hash_bytes = hex::decode(&entry.hash)
                        .map_err(|e| GitError::SerializationError(e.to_string()))?;
                    content.extend_from_slice(&hash_bytes);
                }
                
                let header = format!("tree {}\0", content.len());
                let mut result = header.into_bytes();
                result.extend_from_slice(&content);
                Ok(result)
            }
            GitObject::Commit(commit) => {
                let mut content = String::new();
                content.push_str(&format!("tree {}\n", commit.tree));
                
                for parent in &commit.parents {
                    content.push_str(&format!("parent {}\n", parent));
                }
                
                content.push_str(&format!(
                    "author {} <{}> {} +0000\n",
                    commit.author.name,
                    commit.author.email,
                    commit.author.timestamp.timestamp()
                ));
                
                content.push_str(&format!(
                    "committer {} <{}> {} +0000\n",
                    commit.committer.name,
                    commit.committer.email,
                    commit.committer.timestamp.timestamp()
                ));
                
                content.push('\n');
                content.push_str(&commit.message);
                
                let header = format!("commit {}\0", content.len());
                let mut result = header.into_bytes();
                result.extend_from_slice(content.as_bytes());
                Ok(result)
            }
            GitObject::Tag(tag) => {
                let content = format!(
                    "object {}\ntype {}\ntag {}\ntagger {} <{}> {} +0000\n\n{}",
                    tag.object,
                    tag.object_type,
                    tag.tag,
                    tag.tagger.name,
                    tag.tagger.email,
                    tag.tagger.timestamp.timestamp(),
                    tag.message
                );
                
                let header = format!("tag {}\0", content.len());
                let mut result = header.into_bytes();
                result.extend_from_slice(content.as_bytes());
                Ok(result)
            }
        }
    }
}

/// Trait for objects that can be stored in Git
pub trait GitStorable {
    fn object_type(&self) -> &'static str;
    fn content(&self) -> &[u8];
}

impl GitStorable for BlobObject {
    fn object_type(&self) -> &'static str {
        "blob"
    }
    
    fn content(&self) -> &[u8] {
        &self.content
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::Utc;

    #[test]
    fn test_blob_object_hash() {
        let blob = GitObject::Blob(BlobObject {
            content: b"Hello, World!".to_vec(),
        });
        
        let hash = blob.hash().unwrap();
        // This should match what Git would produce
        assert_eq!(hash.len(), 40); // SHA-1 is 40 hex characters
    }

    #[test]
    fn test_commit_object_serialization() {
        let commit = GitObject::Commit(CommitObject {
            tree: "abc123".to_string(),
            parents: vec!["def456".to_string()],
            author: GitSignature {
                name: "Test User".to_string(),
                email: "<EMAIL>".to_string(),
                timestamp: Utc::now(),
            },
            committer: GitSignature {
                name: "Test User".to_string(),
                email: "<EMAIL>".to_string(),
                timestamp: Utc::now(),
            },
            message: "Test commit".to_string(),
        });

        let serialized = commit.serialize().unwrap();
        assert!(serialized.starts_with(b"commit "));
    }

    #[test]
    fn test_tree_object_with_entries() {
        let tree = GitObject::Tree(TreeObject {
            entries: vec![
                TreeEntry {
                    mode: "100644".to_string(),
                    name: "README.md".to_string(),
                    hash: "abc123def456789012345678901234567890abcd".to_string(),
                },
                TreeEntry {
                    mode: "040000".to_string(),
                    name: "src".to_string(),
                    hash: "def456abc123456789012345678901234567890abc".to_string(),
                },
            ],
        });

        let hash = tree.hash().unwrap();
        assert_eq!(hash.len(), 40);
    }

    #[test]
    fn test_blob_content_matches_git() {
        // Test that our blob serialization matches Git's format
        let content = b"Hello, World!";
        let blob = GitObject::Blob(BlobObject {
            content: content.to_vec(),
        });
        
        let serialized = blob.serialize().unwrap();
        let expected = format!("blob {}\0Hello, World!", content.len());
        assert_eq!(serialized, expected.as_bytes());
    }

    #[test]
    fn test_empty_blob() {
        let blob = GitObject::Blob(BlobObject {
            content: Vec::new(),
        });
        
        let hash = blob.hash().unwrap();
        assert_eq!(hash.len(), 40);
        
        let serialized = blob.serialize().unwrap();
        assert_eq!(serialized, b"blob 0\0");
    }
}
