use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON><PERSON>, Deserialize, Debug, <PERSON><PERSON>)]
pub struct Repository {
    pub id: u32,
    pub name: String,
    pub description: Option<String>,
    pub created_at: String,
}

pub mod handlers {
    use super::*;
    
    pub fn calculate_repo_stats(repos: &[Repository]) -> (usize, usize) {
        let total = repos.len();
        let with_description = repos.iter()
            .filter(|repo| repo.description.is_some())
            .count();
        (total, with_description)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::handlers::calculate_repo_stats;

    #[test]
    fn test_repo_stats() {
        let repos = vec![
            Repository {
                id: 1,
                name: "repo1".to_string(),
                description: Some("Has description".to_string()),
                created_at: "2024-01-01T00:00:00Z".to_string(),
            },
            Repository {
                id: 2,
                name: "repo2".to_string(),
                description: None,
                created_at: "2024-01-01T00:00:00Z".to_string(),
            },
        ];

        let (total, with_desc) = calculate_repo_stats(&repos);
        assert_eq!(total, 2);
        assert_eq!(with_desc, 1);
    }

    #[test]
    fn test_empty_repos() {
        let repos = vec![];
        let (total, with_desc) = calculate_repo_stats(&repos);
        assert_eq!(total, 0);
        assert_eq!(with_desc, 0);
    }

    #[test]
    fn test_all_repos_with_description() {
        let repos = vec![
            Repository {
                id: 1,
                name: "repo1".to_string(),
                description: Some("Description 1".to_string()),
                created_at: "2024-01-01T00:00:00Z".to_string(),
            },
            Repository {
                id: 2,
                name: "repo2".to_string(),
                description: Some("Description 2".to_string()),
                created_at: "2024-01-01T00:00:00Z".to_string(),
            },
        ];

        let (total, with_desc) = calculate_repo_stats(&repos);
        assert_eq!(total, 2);
        assert_eq!(with_desc, 2);
    }

    #[test]
    fn test_no_repos_with_description() {
        let repos = vec![
            Repository {
                id: 1,
                name: "repo1".to_string(),
                description: None,
                created_at: "2024-01-01T00:00:00Z".to_string(),
            },
            Repository {
                id: 2,
                name: "repo2".to_string(),
                description: None,
                created_at: "2024-01-01T00:00:00Z".to_string(),
            },
        ];

        let (total, with_desc) = calculate_repo_stats(&repos);
        assert_eq!(total, 2);
        assert_eq!(with_desc, 0);
    }
}
