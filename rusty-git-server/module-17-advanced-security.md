# Module 17: Advanced Security & Threat Protection

## 🎯 Learning Objectives

By the end of this module, you will:
- Implement comprehensive DDoS protection and advanced rate limiting
- Build a Web Application Firewall (WAF) with real-time threat detection
- Create an Intrusion Detection System (IDS) with automated response
- Master advanced cryptography including end-to-end encryption and key rotation
- Implement Zero Trust Architecture principles
- Build container and infrastructure security hardening
- Create automated security incident response systems

## 🛡️ Why Advanced Security Matters

Modern applications face sophisticated threats requiring layered defense:
- **Advanced Persistent Threats (APTs)**: Long-term, stealthy attacks
- **Zero-Day Exploits**: Unknown vulnerabilities being exploited
- **DDoS Attacks**: Distributed denial of service at massive scale
- **Supply Chain Attacks**: Compromised dependencies and infrastructure
- **Insider Threats**: Malicious or compromised internal users
- **Compliance Requirements**: Stringent security standards (SOC2, FedRAMP)

### Advanced Security Architecture

```mermaid
graph TB
    subgraph "Perimeter Defense"
        CDN[CDN with DDoS Protection]
        WAF[Web Application Firewall]
        RATE[Advanced Rate Limiting]
        GEO[Geo-blocking]
    end
    
    subgraph "Network Security"
        VPN[Zero Trust VPN]
        FIREWALL[Network Firewall]
        IDS[Intrusion Detection]
        IPS[Intrusion Prevention]
    end
    
    subgraph "Application Security"
        AUTH[Multi-Factor Auth]
        RBAC[Zero Trust RBAC]
        ENCRYPT[End-to-End Encryption]
        SECRETS[Secret Management]
    end
    
    subgraph "Container Security"
        SCAN[Image Scanning]
        RUNTIME[Runtime Protection]
        POLICY[Security Policies]
        ISOLATION[Container Isolation]
    end
    
    subgraph "Monitoring & Response"
        SIEM[Security Information & Event Management]
        SOC[Security Operations Center]
        INCIDENT[Incident Response]
        FORENSICS[Digital Forensics]
    end
    
    subgraph "Data Protection"
        BACKUP[Encrypted Backups]
        DLP[Data Loss Prevention]
        PRIVACY[Privacy Controls]
        RETENTION[Data Retention]
    end
    
    CDN --> WAF
    WAF --> RATE
    RATE --> GEO
    
    GEO --> VPN
    VPN --> FIREWALL
    FIREWALL --> IDS
    IDS --> IPS
    
    IPS --> AUTH
    AUTH --> RBAC
    RBAC --> ENCRYPT
    ENCRYPT --> SECRETS
    
    SECRETS --> SCAN
    SCAN --> RUNTIME
    RUNTIME --> POLICY
    POLICY --> ISOLATION
    
    ISOLATION --> SIEM
    SIEM --> SOC
    SOC --> INCIDENT
    INCIDENT --> FORENSICS
    
    FORENSICS --> BACKUP
    BACKUP --> DLP
    DLP --> PRIVACY
    PRIVACY --> RETENTION
    
    style CDN fill:#ffebee
    style WAF fill:#e8f5e8
    style SIEM fill:#e1f5fe
    style ENCRYPT fill:#fff3e0
```

## 🚫 DDoS Protection & Advanced Rate Limiting

### Intelligent Rate Limiting System

```rust
// src/security/rate_limiting.rs
use std::collections::HashMap;
use std::net::IpAddr;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitConfig {
    pub requests_per_minute: u32,
    pub requests_per_hour: u32,
    pub requests_per_day: u32,
    pub burst_limit: u32,
    pub ban_duration_minutes: u32,
    pub whitelist_ips: Vec<IpAddr>,
    pub blacklist_ips: Vec<IpAddr>,
}

#[derive(Debug, Clone)]
pub struct RateLimitEntry {
    pub requests_minute: u32,
    pub requests_hour: u32,
    pub requests_day: u32,
    pub last_request: Instant,
    pub minute_window: Instant,
    pub hour_window: Instant,
    pub day_window: Instant,
    pub is_banned: bool,
    pub ban_until: Option<Instant>,
    pub threat_score: f64,
}

#[derive(Debug, Clone)]
pub enum RateLimitResult {
    Allowed,
    Limited { retry_after: Duration },
    Banned { ban_until: Instant },
    Suspicious { threat_score: f64 },
}

pub struct AdvancedRateLimiter {
    config: RateLimitConfig,
    entries: Arc<RwLock<HashMap<IpAddr, RateLimitEntry>>>,
    ddos_detector: DDoSDetector,
    threat_analyzer: ThreatAnalyzer,
}

impl AdvancedRateLimiter {
    pub fn new(config: RateLimitConfig) -> Self {
        Self {
            config: config.clone(),
            entries: Arc::new(RwLock::new(HashMap::new())),
            ddos_detector: DDoSDetector::new(config.clone()),
            threat_analyzer: ThreatAnalyzer::new(),
        }
    }
    
    /// Check if request should be allowed
    pub async fn check_request(
        &self,
        ip: IpAddr,
        user_agent: Option<&str>,
        endpoint: &str,
        user_id: Option<Uuid>,
    ) -> RateLimitResult {
        // Check whitelist/blacklist first
        if self.config.whitelist_ips.contains(&ip) {
            return RateLimitResult::Allowed;
        }
        
        if self.config.blacklist_ips.contains(&ip) {
            return RateLimitResult::Banned {
                ban_until: Instant::now() + Duration::from_secs(86400), // 24 hours
            };
        }
        
        let mut entries = self.entries.write().await;
        let now = Instant::now();
        
        let entry = entries.entry(ip).or_insert_with(|| RateLimitEntry {
            requests_minute: 0,
            requests_hour: 0,
            requests_day: 0,
            last_request: now,
            minute_window: now,
            hour_window: now,
            day_window: now,
            is_banned: false,
            ban_until: None,
            threat_score: 0.0,
        });
        
        // Check if currently banned
        if let Some(ban_until) = entry.ban_until {
            if now < ban_until {
                return RateLimitResult::Banned { ban_until };
            } else {
                entry.is_banned = false;
                entry.ban_until = None;
                entry.threat_score *= 0.5; // Reduce threat score after ban expires
            }
        }
        
        // Reset windows if needed
        self.reset_windows(entry, now);
        
        // Increment counters
        entry.requests_minute += 1;
        entry.requests_hour += 1;
        entry.requests_day += 1;
        entry.last_request = now;
        
        // Analyze threat level
        let threat_analysis = self.threat_analyzer.analyze_request(
            ip,
            user_agent,
            endpoint,
            user_id,
            entry,
        ).await;
        
        entry.threat_score = (entry.threat_score * 0.9) + (threat_analysis.score * 0.1);
        
        // Check DDoS patterns
        if self.ddos_detector.detect_ddos_pattern(ip, entry, &threat_analysis).await {
            entry.is_banned = true;
            entry.ban_until = Some(now + Duration::from_secs(self.config.ban_duration_minutes as u64 * 60));
            
            // Alert security team
            self.alert_security_team(ip, "DDoS pattern detected", entry.threat_score).await;
            
            return RateLimitResult::Banned {
                ban_until: entry.ban_until.unwrap(),
            };
        }
        
        // Check rate limits
        if entry.requests_minute > self.config.requests_per_minute {
            return RateLimitResult::Limited {
                retry_after: Duration::from_secs(60 - (now.duration_since(entry.minute_window).as_secs() % 60)),
            };
        }
        
        if entry.requests_hour > self.config.requests_per_hour {
            return RateLimitResult::Limited {
                retry_after: Duration::from_secs(3600 - (now.duration_since(entry.hour_window).as_secs() % 3600)),
            };
        }
        
        if entry.requests_day > self.config.requests_per_day {
            return RateLimitResult::Limited {
                retry_after: Duration::from_secs(86400 - (now.duration_since(entry.day_window).as_secs() % 86400)),
            };
        }
        
        // Check for suspicious activity
        if entry.threat_score > 0.7 {
            return RateLimitResult::Suspicious {
                threat_score: entry.threat_score,
            };
        }
        
        RateLimitResult::Allowed
    }
    
    fn reset_windows(&self, entry: &mut RateLimitEntry, now: Instant) {
        // Reset minute window
        if now.duration_since(entry.minute_window) >= Duration::from_secs(60) {
            entry.requests_minute = 0;
            entry.minute_window = now;
        }
        
        // Reset hour window
        if now.duration_since(entry.hour_window) >= Duration::from_secs(3600) {
            entry.requests_hour = 0;
            entry.hour_window = now;
        }
        
        // Reset day window
        if now.duration_since(entry.day_window) >= Duration::from_secs(86400) {
            entry.requests_day = 0;
            entry.day_window = now;
        }
    }
    
    async fn alert_security_team(&self, ip: IpAddr, reason: &str, threat_score: f64) {
        // Implementation would send alerts via email, Slack, PagerDuty, etc.
        tracing::warn!(
            "Security alert: {} from IP {} (threat score: {:.2})",
            reason,
            ip,
            threat_score
        );
    }
}

pub struct DDoSDetector {
    config: RateLimitConfig,
    global_stats: Arc<RwLock<GlobalTrafficStats>>,
}

#[derive(Debug, Default)]
pub struct GlobalTrafficStats {
    pub total_requests_per_second: f64,
    pub unique_ips_per_minute: usize,
    pub error_rate: f64,
    pub average_response_time: Duration,
    pub suspicious_patterns: Vec<SuspiciousPattern>,
}

#[derive(Debug)]
pub struct SuspiciousPattern {
    pub pattern_type: PatternType,
    pub confidence: f64,
    pub first_seen: Instant,
    pub last_seen: Instant,
    pub occurrences: u32,
}

#[derive(Debug)]
pub enum PatternType {
    VolumetricAttack,
    SlowLoris,
    ApplicationLayerFlood,
    BotnetActivity,
    ScanningActivity,
}

impl DDoSDetector {
    pub fn new(config: RateLimitConfig) -> Self {
        Self {
            config,
            global_stats: Arc::new(RwLock::new(GlobalTrafficStats::default())),
        }
    }
    
    pub async fn detect_ddos_pattern(
        &self,
        ip: IpAddr,
        entry: &RateLimitEntry,
        threat_analysis: &ThreatAnalysis,
    ) -> bool {
        let stats = self.global_stats.read().await;
        
        // Volumetric attack detection
        if entry.requests_minute > self.config.burst_limit * 2 {
            return true;
        }
        
        // Global traffic anomaly detection
        if stats.total_requests_per_second > 1000.0 && entry.requests_minute > 100 {
            return true;
        }
        
        // Bot-like behavior detection
        if threat_analysis.bot_probability > 0.8 && entry.requests_minute > 50 {
            return true;
        }
        
        // Scanning activity detection
        if threat_analysis.scanning_score > 0.7 {
            return true;
        }
        
        false
    }
}

pub struct ThreatAnalyzer {
    known_bad_user_agents: Vec<String>,
    suspicious_endpoints: Vec<String>,
}

#[derive(Debug)]
pub struct ThreatAnalysis {
    pub score: f64,
    pub bot_probability: f64,
    pub scanning_score: f64,
    pub anomaly_score: f64,
    pub reputation_score: f64,
}

impl ThreatAnalyzer {
    pub fn new() -> Self {
        Self {
            known_bad_user_agents: vec![
                "sqlmap".to_string(),
                "nikto".to_string(),
                "nmap".to_string(),
                "masscan".to_string(),
            ],
            suspicious_endpoints: vec![
                "/admin".to_string(),
                "/.env".to_string(),
                "/wp-admin".to_string(),
                "/phpmyadmin".to_string(),
            ],
        }
    }
    
    pub async fn analyze_request(
        &self,
        ip: IpAddr,
        user_agent: Option<&str>,
        endpoint: &str,
        user_id: Option<Uuid>,
        entry: &RateLimitEntry,
    ) -> ThreatAnalysis {
        let mut score = 0.0;
        let mut bot_probability = 0.0;
        let mut scanning_score = 0.0;
        let mut anomaly_score = 0.0;
        
        // User agent analysis
        if let Some(ua) = user_agent {
            if self.known_bad_user_agents.iter().any(|bad_ua| ua.contains(bad_ua)) {
                score += 0.8;
                bot_probability += 0.9;
            }
            
            if ua.is_empty() || ua.len() < 10 {
                score += 0.3;
                bot_probability += 0.4;
            }
        } else {
            score += 0.5;
            bot_probability += 0.6;
        }
        
        // Endpoint analysis
        if self.suspicious_endpoints.iter().any(|sus_ep| endpoint.contains(sus_ep)) {
            score += 0.6;
            scanning_score += 0.8;
        }
        
        // Request pattern analysis
        if entry.requests_minute > 30 {
            score += 0.4;
            anomaly_score += 0.5;
        }
        
        // Authenticated vs unauthenticated
        if user_id.is_none() && entry.requests_minute > 10 {
            score += 0.2;
            bot_probability += 0.3;
        }
        
        // IP reputation (would integrate with threat intelligence feeds)
        let reputation_score = self.check_ip_reputation(ip).await;
        score += reputation_score * 0.5;
        
        ThreatAnalysis {
            score: score.min(1.0),
            bot_probability: bot_probability.min(1.0),
            scanning_score: scanning_score.min(1.0),
            anomaly_score: anomaly_score.min(1.0),
            reputation_score,
        }
    }
    
    async fn check_ip_reputation(&self, ip: IpAddr) -> f64 {
        // Implementation would check against threat intelligence feeds
        // For now, return a placeholder
        0.0
    }
}
```

## 🔥 Web Application Firewall (WAF)

### Advanced WAF Implementation

```rust
// src/security/waf.rs
use regex::Regex;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WAFRule {
    pub id: Uuid,
    pub name: String,
    pub description: String,
    pub rule_type: WAFRuleType,
    pub pattern: String,
    pub action: WAFAction,
    pub severity: WAFSeverity,
    pub enabled: bool,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum WAFRuleType {
    SQLInjection,
    XSS,
    PathTraversal,
    CommandInjection,
    LDAP,
    XXE,
    SSRF,
    Custom,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum WAFAction {
    Block,
    Log,
    Challenge,
    RateLimit,
    Redirect(String),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum WAFSeverity {
    Critical,
    High,
    Medium,
    Low,
    Info,
}

#[derive(Debug)]
pub struct WAFRequest {
    pub method: String,
    pub path: String,
    pub query_params: HashMap<String, String>,
    pub headers: HashMap<String, String>,
    pub body: Option<String>,
    pub ip: std::net::IpAddr,
    pub user_agent: Option<String>,
}

#[derive(Debug)]
pub struct WAFResult {
    pub action: WAFAction,
    pub matched_rules: Vec<WAFRuleMatch>,
    pub threat_score: f64,
    pub should_block: bool,
}

#[derive(Debug)]
pub struct WAFRuleMatch {
    pub rule: WAFRule,
    pub matched_value: String,
    pub location: String, // header, query, body, etc.
}

pub struct WebApplicationFirewall {
    rules: Vec<CompiledWAFRule>,
    ml_detector: MLThreatDetector,
    reputation_service: ReputationService,
}

struct CompiledWAFRule {
    rule: WAFRule,
    regex: Regex,
}

impl WebApplicationFirewall {
    pub fn new() -> Self {
        let mut waf = Self {
            rules: Vec::new(),
            ml_detector: MLThreatDetector::new(),
            reputation_service: ReputationService::new(),
        };

        waf.load_default_rules();
        waf
    }

    /// Analyze incoming request for threats
    pub async fn analyze_request(&self, request: &WAFRequest) -> WAFResult {
        let mut matched_rules = Vec::new();
        let mut threat_score = 0.0;
        let mut highest_action = WAFAction::Log;

        // Check against all rules
        for compiled_rule in &self.rules {
            if !compiled_rule.rule.enabled {
                continue;
            }

            if let Some(rule_match) = self.check_rule(compiled_rule, request) {
                threat_score += self.get_severity_score(&compiled_rule.rule.severity);

                // Update action based on severity
                match compiled_rule.rule.action {
                    WAFAction::Block => highest_action = WAFAction::Block,
                    WAFAction::Challenge if !matches!(highest_action, WAFAction::Block) => {
                        highest_action = WAFAction::Challenge;
                    }
                    _ => {}
                }

                matched_rules.push(rule_match);
            }
        }

        // ML-based threat detection
        let ml_score = self.ml_detector.analyze_request(request).await;
        threat_score += ml_score;

        // IP reputation check
        let reputation_score = self.reputation_service.check_ip(request.ip).await;
        threat_score += reputation_score;

        // Normalize threat score
        threat_score = threat_score.min(10.0);

        let should_block = matches!(highest_action, WAFAction::Block) || threat_score > 8.0;

        WAFResult {
            action: highest_action,
            matched_rules,
            threat_score,
            should_block,
        }
    }

    fn check_rule(&self, compiled_rule: &CompiledWAFRule, request: &WAFRequest) -> Option<WAFRuleMatch> {
        // Check URL path
        if compiled_rule.regex.is_match(&request.path) {
            return Some(WAFRuleMatch {
                rule: compiled_rule.rule.clone(),
                matched_value: request.path.clone(),
                location: "path".to_string(),
            });
        }

        // Check query parameters
        for (key, value) in &request.query_params {
            let combined = format!("{}={}", key, value);
            if compiled_rule.regex.is_match(&combined) {
                return Some(WAFRuleMatch {
                    rule: compiled_rule.rule.clone(),
                    matched_value: combined,
                    location: format!("query.{}", key),
                });
            }
        }

        // Check headers
        for (key, value) in &request.headers {
            if compiled_rule.regex.is_match(value) {
                return Some(WAFRuleMatch {
                    rule: compiled_rule.rule.clone(),
                    matched_value: value.clone(),
                    location: format!("header.{}", key),
                });
            }
        }

        // Check body
        if let Some(body) = &request.body {
            if compiled_rule.regex.is_match(body) {
                return Some(WAFRuleMatch {
                    rule: compiled_rule.rule.clone(),
                    matched_value: body.clone(),
                    location: "body".to_string(),
                });
            }
        }

        None
    }

    fn get_severity_score(&self, severity: &WAFSeverity) -> f64 {
        match severity {
            WAFSeverity::Critical => 4.0,
            WAFSeverity::High => 3.0,
            WAFSeverity::Medium => 2.0,
            WAFSeverity::Low => 1.0,
            WAFSeverity::Info => 0.5,
        }
    }

    fn load_default_rules(&mut self) {
        let default_rules = vec![
            // SQL Injection Rules
            WAFRule {
                id: Uuid::new_v4(),
                name: "SQL Injection - Union Based".to_string(),
                description: "Detects UNION-based SQL injection attempts".to_string(),
                rule_type: WAFRuleType::SQLInjection,
                pattern: r"(?i)(union\s+(all\s+)?select|select\s+.*\s+from|insert\s+into|update\s+.*\s+set|delete\s+from)".to_string(),
                action: WAFAction::Block,
                severity: WAFSeverity::Critical,
                enabled: true,
                created_at: Utc::now(),
            },
            WAFRule {
                id: Uuid::new_v4(),
                name: "SQL Injection - Boolean Based".to_string(),
                description: "Detects boolean-based SQL injection".to_string(),
                rule_type: WAFRuleType::SQLInjection,
                pattern: r"(?i)(and|or)\s+\d+\s*=\s*\d+|'.*'.*=.*'.*'|\d+\s*=\s*\d+\s*(and|or)".to_string(),
                action: WAFAction::Block,
                severity: WAFSeverity::High,
                enabled: true,
                created_at: Utc::now(),
            },

            // XSS Rules
            WAFRule {
                id: Uuid::new_v4(),
                name: "XSS - Script Tags".to_string(),
                description: "Detects script tag injection attempts".to_string(),
                rule_type: WAFRuleType::XSS,
                pattern: r"(?i)<script[^>]*>.*?</script>|<script[^>]*>|javascript:|vbscript:|onload\s*=|onerror\s*=".to_string(),
                action: WAFAction::Block,
                severity: WAFSeverity::High,
                enabled: true,
                created_at: Utc::now(),
            },

            // Path Traversal Rules
            WAFRule {
                id: Uuid::new_v4(),
                name: "Path Traversal".to_string(),
                description: "Detects directory traversal attempts".to_string(),
                rule_type: WAFRuleType::PathTraversal,
                pattern: r"(\.\./|\.\.\\|%2e%2e%2f|%2e%2e%5c|\.\.%2f|\.\.%5c)".to_string(),
                action: WAFAction::Block,
                severity: WAFSeverity::High,
                enabled: true,
                created_at: Utc::now(),
            },

            // Command Injection Rules
            WAFRule {
                id: Uuid::new_v4(),
                name: "Command Injection".to_string(),
                description: "Detects OS command injection attempts".to_string(),
                rule_type: WAFRuleType::CommandInjection,
                pattern: r"(?i)(;|\||&|`|\$\(|<|>).*?(ls|cat|pwd|whoami|id|uname|ps|netstat|ifconfig|ping|wget|curl|nc|telnet|ssh)".to_string(),
                action: WAFAction::Block,
                severity: WAFSeverity::Critical,
                enabled: true,
                created_at: Utc::now(),
            },

            // LDAP Injection Rules
            WAFRule {
                id: Uuid::new_v4(),
                name: "LDAP Injection".to_string(),
                description: "Detects LDAP injection attempts".to_string(),
                rule_type: WAFRuleType::LDAP,
                pattern: r"(\*|\(|\)|&|\||!|=|<|>|~|%2a|%28|%29|%26|%7c|%21|%3d|%3c|%3e|%7e)".to_string(),
                action: WAFAction::Block,
                severity: WAFSeverity::Medium,
                enabled: true,
                created_at: Utc::now(),
            },
        ];

        for rule in default_rules {
            if let Ok(regex) = Regex::new(&rule.pattern) {
                self.rules.push(CompiledWAFRule { rule, regex });
            }
        }
    }
}

pub struct MLThreatDetector {
    // ML model for threat detection would be loaded here
}

impl MLThreatDetector {
    pub fn new() -> Self {
        Self {}
    }

    pub async fn analyze_request(&self, request: &WAFRequest) -> f64 {
        let mut score = 0.0;

        // Analyze request patterns using ML
        // This would use a trained model to detect anomalous patterns

        // Simple heuristics for demonstration
        if request.path.len() > 1000 {
            score += 1.0;
        }

        if request.query_params.len() > 50 {
            score += 1.0;
        }

        if let Some(body) = &request.body {
            if body.len() > 100000 {
                score += 1.0;
            }
        }

        // Check for suspicious patterns in user agent
        if let Some(ua) = &request.user_agent {
            if ua.contains("bot") || ua.contains("crawler") || ua.contains("scanner") {
                score += 0.5;
            }
        }

        score
    }
}

pub struct ReputationService {
    // Integration with threat intelligence feeds
}

impl ReputationService {
    pub fn new() -> Self {
        Self {}
    }

    pub async fn check_ip(&self, ip: std::net::IpAddr) -> f64 {
        // Implementation would check against:
        // - Known malicious IP databases
        // - Tor exit nodes
        // - VPN/proxy services
        // - Geolocation-based rules

        // Placeholder implementation
        0.0
    }
}
```

## 🕵️ Intrusion Detection System (IDS)

### Real-time Threat Detection

```rust
// src/security/ids.rs
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, VecDeque};
use std::net::IpAddr;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use uuid::Uuid;
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityEvent {
    pub id: Uuid,
    pub event_type: SecurityEventType,
    pub severity: SecuritySeverity,
    pub source_ip: IpAddr,
    pub target: String,
    pub description: String,
    pub evidence: serde_json::Value,
    pub timestamp: DateTime<Utc>,
    pub user_id: Option<Uuid>,
    pub session_id: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SecurityEventType {
    SuspiciousLogin,
    BruteForceAttempt,
    PrivilegeEscalation,
    DataExfiltration,
    MalwareDetection,
    AnomalousActivity,
    PolicyViolation,
    IntrusionAttempt,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SecuritySeverity {
    Critical,
    High,
    Medium,
    Low,
    Info,
}

#[derive(Debug)]
pub struct IntrusionPattern {
    pub pattern_id: Uuid,
    pub name: String,
    pub description: String,
    pub indicators: Vec<ThreatIndicator>,
    pub time_window: Duration,
    pub threshold: u32,
    pub severity: SecuritySeverity,
}

#[derive(Debug)]
pub struct ThreatIndicator {
    pub indicator_type: IndicatorType,
    pub value: String,
    pub weight: f64,
}

#[derive(Debug)]
pub enum IndicatorType {
    IPAddress,
    UserAgent,
    RequestPattern,
    ResponsePattern,
    TimingPattern,
    VolumePattern,
}

pub struct IntrusionDetectionSystem {
    patterns: Vec<IntrusionPattern>,
    event_buffer: Arc<RwLock<VecDeque<SecurityEvent>>>,
    active_sessions: Arc<RwLock<HashMap<String, SessionTracker>>>,
    ip_trackers: Arc<RwLock<HashMap<IpAddr, IPTracker>>>,
    alert_manager: AlertManager,
}

#[derive(Debug)]
struct SessionTracker {
    session_id: String,
    user_id: Option<Uuid>,
    ip_address: IpAddr,
    start_time: Instant,
    last_activity: Instant,
    request_count: u32,
    failed_attempts: u32,
    privilege_changes: u32,
    data_access_count: u32,
    anomaly_score: f64,
}

#[derive(Debug)]
struct IPTracker {
    ip: IpAddr,
    first_seen: Instant,
    last_seen: Instant,
    total_requests: u32,
    failed_logins: u32,
    successful_logins: u32,
    unique_user_agents: Vec<String>,
    accessed_endpoints: Vec<String>,
    threat_score: f64,
}

impl IntrusionDetectionSystem {
    pub fn new() -> Self {
        let mut ids = Self {
            patterns: Vec::new(),
            event_buffer: Arc::new(RwLock::new(VecDeque::with_capacity(10000))),
            active_sessions: Arc::new(RwLock::new(HashMap::new())),
            ip_trackers: Arc::new(RwLock::new(HashMap::new())),
            alert_manager: AlertManager::new(),
        };

        ids.load_detection_patterns();
        ids
    }

    /// Process incoming security event
    pub async fn process_event(&self, event: SecurityEvent) {
        // Add to event buffer
        {
            let mut buffer = self.event_buffer.write().await;
            buffer.push_back(event.clone());

            // Keep buffer size manageable
            if buffer.len() > 10000 {
                buffer.pop_front();
            }
        }

        // Update session tracking
        if let Some(session_id) = &event.session_id {
            self.update_session_tracker(session_id, &event).await;
        }

        // Update IP tracking
        self.update_ip_tracker(&event).await;

        // Check for pattern matches
        self.check_intrusion_patterns(&event).await;

        // Real-time anomaly detection
        self.detect_anomalies(&event).await;
    }

    async fn update_session_tracker(&self, session_id: &str, event: &SecurityEvent) {
        let mut sessions = self.active_sessions.write().await;
        let now = Instant::now();

        let tracker = sessions.entry(session_id.to_string()).or_insert_with(|| {
            SessionTracker {
                session_id: session_id.to_string(),
                user_id: event.user_id,
                ip_address: event.source_ip,
                start_time: now,
                last_activity: now,
                request_count: 0,
                failed_attempts: 0,
                privilege_changes: 0,
                data_access_count: 0,
                anomaly_score: 0.0,
            }
        });

        tracker.last_activity = now;
        tracker.request_count += 1;

        match event.event_type {
            SecurityEventType::SuspiciousLogin | SecurityEventType::BruteForceAttempt => {
                tracker.failed_attempts += 1;
                tracker.anomaly_score += 1.0;
            }
            SecurityEventType::PrivilegeEscalation => {
                tracker.privilege_changes += 1;
                tracker.anomaly_score += 2.0;
            }
            SecurityEventType::DataExfiltration => {
                tracker.data_access_count += 1;
                tracker.anomaly_score += 3.0;
            }
            _ => {}
        }

        // Check for session-based threats
        if tracker.anomaly_score > 10.0 {
            self.alert_manager.send_alert(SecurityAlert {
                alert_type: AlertType::HighRiskSession,
                severity: SecuritySeverity::High,
                message: format!("High-risk session detected: {}", session_id),
                evidence: serde_json::json!({
                    "session_id": session_id,
                    "anomaly_score": tracker.anomaly_score,
                    "failed_attempts": tracker.failed_attempts,
                    "privilege_changes": tracker.privilege_changes
                }),
                timestamp: Utc::now(),
            }).await;
        }
    }

    async fn update_ip_tracker(&self, event: &SecurityEvent) {
        let mut ip_trackers = self.ip_trackers.write().await;
        let now = Instant::now();

        let tracker = ip_trackers.entry(event.source_ip).or_insert_with(|| {
            IPTracker {
                ip: event.source_ip,
                first_seen: now,
                last_seen: now,
                total_requests: 0,
                failed_logins: 0,
                successful_logins: 0,
                unique_user_agents: Vec::new(),
                accessed_endpoints: Vec::new(),
                threat_score: 0.0,
            }
        });

        tracker.last_seen = now;
        tracker.total_requests += 1;

        match event.event_type {
            SecurityEventType::SuspiciousLogin | SecurityEventType::BruteForceAttempt => {
                tracker.failed_logins += 1;
                tracker.threat_score += 0.5;
            }
            _ => {}
        }

        // Detect distributed attacks
        if tracker.failed_logins > 50 {
            self.alert_manager.send_alert(SecurityAlert {
                alert_type: AlertType::DistributedAttack,
                severity: SecuritySeverity::Critical,
                message: format!("Distributed attack detected from IP: {}", event.source_ip),
                evidence: serde_json::json!({
                    "ip": event.source_ip,
                    "failed_logins": tracker.failed_logins,
                    "total_requests": tracker.total_requests,
                    "threat_score": tracker.threat_score
                }),
                timestamp: Utc::now(),
            }).await;
        }
    }

    async fn check_intrusion_patterns(&self, event: &SecurityEvent) {
        for pattern in &self.patterns {
            if self.matches_pattern(pattern, event).await {
                self.alert_manager.send_alert(SecurityAlert {
                    alert_type: AlertType::PatternMatch,
                    severity: pattern.severity.clone(),
                    message: format!("Intrusion pattern detected: {}", pattern.name),
                    evidence: serde_json::json!({
                        "pattern_id": pattern.pattern_id,
                        "pattern_name": pattern.name,
                        "event": event
                    }),
                    timestamp: Utc::now(),
                }).await;
            }
        }
    }

    async fn matches_pattern(&self, pattern: &IntrusionPattern, event: &SecurityEvent) -> bool {
        // Check if event matches any indicators in the pattern
        for indicator in &pattern.indicators {
            match indicator.indicator_type {
                IndicatorType::IPAddress => {
                    if event.source_ip.to_string().contains(&indicator.value) {
                        return true;
                    }
                }
                IndicatorType::RequestPattern => {
                    if event.target.contains(&indicator.value) {
                        return true;
                    }
                }
                _ => {}
            }
        }

        false
    }

    async fn detect_anomalies(&self, event: &SecurityEvent) {
        // Time-based anomaly detection
        let hour = event.timestamp.hour();
        if hour < 6 || hour > 22 {
            // Activity outside normal hours
            if matches!(event.event_type, SecurityEventType::DataExfiltration) {
                self.alert_manager.send_alert(SecurityAlert {
                    alert_type: AlertType::AnomalousActivity,
                    severity: SecuritySeverity::Medium,
                    message: "Data access outside normal hours".to_string(),
                    evidence: serde_json::json!({
                        "event": event,
                        "hour": hour
                    }),
                    timestamp: Utc::now(),
                }).await;
            }
        }

        // Volume-based anomaly detection
        let buffer = self.event_buffer.read().await;
        let recent_events: Vec<_> = buffer
            .iter()
            .filter(|e| e.source_ip == event.source_ip)
            .filter(|e| (Utc::now() - e.timestamp).num_minutes() < 5)
            .collect();

        if recent_events.len() > 100 {
            self.alert_manager.send_alert(SecurityAlert {
                alert_type: AlertType::VolumeAnomaly,
                severity: SecuritySeverity::High,
                message: format!("High volume activity from IP: {}", event.source_ip),
                evidence: serde_json::json!({
                    "ip": event.source_ip,
                    "event_count": recent_events.len(),
                    "time_window": "5 minutes"
                }),
                timestamp: Utc::now(),
            }).await;
        }
    }

    fn load_detection_patterns(&mut self) {
        // Load predefined intrusion patterns
        self.patterns.push(IntrusionPattern {
            pattern_id: Uuid::new_v4(),
            name: "Brute Force Login".to_string(),
            description: "Multiple failed login attempts from same IP".to_string(),
            indicators: vec![
                ThreatIndicator {
                    indicator_type: IndicatorType::RequestPattern,
                    value: "/auth/login".to_string(),
                    weight: 1.0,
                }
            ],
            time_window: Duration::from_secs(300), // 5 minutes
            threshold: 10,
            severity: SecuritySeverity::High,
        });

        // Add more patterns...
    }
}

pub struct AlertManager {
    // Alert routing and notification system
}

#[derive(Debug)]
pub struct SecurityAlert {
    pub alert_type: AlertType,
    pub severity: SecuritySeverity,
    pub message: String,
    pub evidence: serde_json::Value,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug)]
pub enum AlertType {
    PatternMatch,
    AnomalousActivity,
    VolumeAnomaly,
    HighRiskSession,
    DistributedAttack,
}

impl AlertManager {
    pub fn new() -> Self {
        Self {}
    }

    pub async fn send_alert(&self, alert: SecurityAlert) {
        // Implementation would send alerts via:
        // - Email
        // - Slack/Teams
        // - PagerDuty
        // - SIEM systems
        // - SMS

        tracing::warn!(
            "Security Alert: {} - {} (Severity: {:?})",
            alert.message,
            alert.alert_type,
            alert.severity
        );
    }
}
```

## 🔐 Zero Trust Architecture & Advanced Cryptography

### Zero Trust Security Model

```rust
// src/security/zero_trust.rs
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc, Duration};
use jsonwebtoken::{encode, decode, Header, Algorithm, Validation, EncodingKey, DecodingKey};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ZeroTrustPolicy {
    pub id: Uuid,
    pub name: String,
    pub description: String,
    pub conditions: Vec<AccessCondition>,
    pub actions: Vec<PolicyAction>,
    pub priority: i32,
    pub enabled: bool,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccessCondition {
    pub condition_type: ConditionType,
    pub operator: ConditionOperator,
    pub value: String,
    pub weight: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ConditionType {
    UserRole,
    IPAddress,
    Location,
    DeviceType,
    TimeOfDay,
    RiskScore,
    AuthenticationMethod,
    NetworkSegment,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ConditionOperator {
    Equals,
    NotEquals,
    Contains,
    NotContains,
    GreaterThan,
    LessThan,
    InRange,
    NotInRange,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PolicyAction {
    Allow,
    Deny,
    RequireMFA,
    RequireApproval,
    LimitAccess,
    LogOnly,
    Challenge,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccessRequest {
    pub user_id: Uuid,
    pub resource: String,
    pub action: String,
    pub context: AccessContext,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccessContext {
    pub ip_address: std::net::IpAddr,
    pub user_agent: String,
    pub device_id: Option<String>,
    pub location: Option<GeoLocation>,
    pub authentication_method: AuthMethod,
    pub session_id: String,
    pub risk_score: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeoLocation {
    pub country: String,
    pub region: String,
    pub city: String,
    pub latitude: f64,
    pub longitude: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AuthMethod {
    Password,
    MFA,
    Certificate,
    Biometric,
    SSO,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccessDecision {
    pub decision: PolicyDecision,
    pub matched_policies: Vec<Uuid>,
    pub confidence_score: f64,
    pub required_actions: Vec<PolicyAction>,
    pub expires_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PolicyDecision {
    Allow,
    Deny,
    Conditional,
}

pub struct ZeroTrustEngine {
    policies: Vec<ZeroTrustPolicy>,
    risk_calculator: RiskCalculator,
    crypto_service: CryptographyService,
    device_trust: DeviceTrustManager,
}

impl ZeroTrustEngine {
    pub fn new() -> Self {
        Self {
            policies: Vec::new(),
            risk_calculator: RiskCalculator::new(),
            crypto_service: CryptographyService::new(),
            device_trust: DeviceTrustManager::new(),
        }
    }

    /// Evaluate access request against Zero Trust policies
    pub async fn evaluate_access(&self, request: &AccessRequest) -> AccessDecision {
        let mut matched_policies = Vec::new();
        let mut decision_score = 0.0;
        let mut required_actions = Vec::new();

        // Calculate dynamic risk score
        let risk_score = self.risk_calculator.calculate_risk(request).await;

        // Evaluate against all policies
        for policy in &self.policies {
            if !policy.enabled {
                continue;
            }

            let policy_match = self.evaluate_policy(policy, request, risk_score).await;

            if policy_match.matches {
                matched_policies.push(policy.id);
                decision_score += policy_match.score * (policy.priority as f64 / 100.0);
                required_actions.extend(policy.actions.clone());
            }
        }

        // Make final decision
        let decision = if decision_score > 0.8 {
            PolicyDecision::Allow
        } else if decision_score > 0.3 {
            PolicyDecision::Conditional
        } else {
            PolicyDecision::Deny
        };

        // Set expiration based on risk
        let expires_at = if risk_score > 0.7 {
            Utc::now() + Duration::minutes(15) // High risk = short session
        } else {
            Utc::now() + Duration::hours(8) // Low risk = longer session
        };

        AccessDecision {
            decision,
            matched_policies,
            confidence_score: decision_score,
            required_actions,
            expires_at,
        }
    }

    async fn evaluate_policy(
        &self,
        policy: &ZeroTrustPolicy,
        request: &AccessRequest,
        risk_score: f64,
    ) -> PolicyMatch {
        let mut total_score = 0.0;
        let mut total_weight = 0.0;
        let mut matches = true;

        for condition in &policy.conditions {
            let condition_result = self.evaluate_condition(condition, request, risk_score).await;

            if condition_result.matches {
                total_score += condition_result.score * condition.weight;
            } else {
                matches = false;
                break;
            }

            total_weight += condition.weight;
        }

        let final_score = if total_weight > 0.0 {
            total_score / total_weight
        } else {
            0.0
        };

        PolicyMatch {
            matches,
            score: final_score,
        }
    }

    async fn evaluate_condition(
        &self,
        condition: &AccessCondition,
        request: &AccessRequest,
        risk_score: f64,
    ) -> ConditionResult {
        let matches = match &condition.condition_type {
            ConditionType::IPAddress => {
                self.evaluate_ip_condition(&condition.operator, &condition.value, request.context.ip_address)
            }
            ConditionType::RiskScore => {
                self.evaluate_numeric_condition(&condition.operator, &condition.value, risk_score)
            }
            ConditionType::TimeOfDay => {
                self.evaluate_time_condition(&condition.operator, &condition.value, request.timestamp)
            }
            ConditionType::AuthenticationMethod => {
                self.evaluate_auth_method_condition(&condition.operator, &condition.value, &request.context.authentication_method)
            }
            _ => false, // Other conditions would be implemented
        };

        ConditionResult {
            matches,
            score: if matches { 1.0 } else { 0.0 },
        }
    }

    fn evaluate_ip_condition(&self, operator: &ConditionOperator, value: &str, ip: std::net::IpAddr) -> bool {
        match operator {
            ConditionOperator::Equals => ip.to_string() == value,
            ConditionOperator::Contains => {
                // CIDR range check would be implemented here
                ip.to_string().starts_with(value)
            }
            _ => false,
        }
    }

    fn evaluate_numeric_condition(&self, operator: &ConditionOperator, value: &str, actual: f64) -> bool {
        if let Ok(threshold) = value.parse::<f64>() {
            match operator {
                ConditionOperator::GreaterThan => actual > threshold,
                ConditionOperator::LessThan => actual < threshold,
                ConditionOperator::Equals => (actual - threshold).abs() < 0.001,
                _ => false,
            }
        } else {
            false
        }
    }

    fn evaluate_time_condition(&self, operator: &ConditionOperator, value: &str, timestamp: DateTime<Utc>) -> bool {
        // Time-based conditions (business hours, etc.)
        let hour = timestamp.hour();

        match value {
            "business_hours" => hour >= 9 && hour <= 17,
            "after_hours" => hour < 9 || hour > 17,
            _ => false,
        }
    }

    fn evaluate_auth_method_condition(&self, operator: &ConditionOperator, value: &str, method: &AuthMethod) -> bool {
        let method_str = match method {
            AuthMethod::Password => "password",
            AuthMethod::MFA => "mfa",
            AuthMethod::Certificate => "certificate",
            AuthMethod::Biometric => "biometric",
            AuthMethod::SSO => "sso",
        };

        match operator {
            ConditionOperator::Equals => method_str == value,
            ConditionOperator::NotEquals => method_str != value,
            _ => false,
        }
    }
}

struct PolicyMatch {
    matches: bool,
    score: f64,
}

struct ConditionResult {
    matches: bool,
    score: f64,
}

pub struct RiskCalculator {
    // Risk calculation algorithms
}

impl RiskCalculator {
    pub fn new() -> Self {
        Self {}
    }

    pub async fn calculate_risk(&self, request: &AccessRequest) -> f64 {
        let mut risk_score = 0.0;

        // Location-based risk
        if let Some(location) = &request.context.location {
            risk_score += self.calculate_location_risk(location);
        }

        // Time-based risk
        risk_score += self.calculate_time_risk(request.timestamp);

        // Authentication method risk
        risk_score += self.calculate_auth_risk(&request.context.authentication_method);

        // Device trust risk
        if let Some(device_id) = &request.context.device_id {
            risk_score += self.calculate_device_risk(device_id).await;
        }

        // Behavioral risk
        risk_score += self.calculate_behavioral_risk(request).await;

        risk_score.min(1.0)
    }

    fn calculate_location_risk(&self, location: &GeoLocation) -> f64 {
        // Risk based on geographic location
        match location.country.as_str() {
            "US" | "CA" | "GB" | "DE" | "FR" | "AU" | "JP" => 0.0,
            _ => 0.3, // Higher risk for other countries
        }
    }

    fn calculate_time_risk(&self, timestamp: DateTime<Utc>) -> f64 {
        let hour = timestamp.hour();

        if hour >= 9 && hour <= 17 {
            0.0 // Business hours = low risk
        } else if hour >= 18 && hour <= 23 {
            0.1 // Evening = slight risk
        } else {
            0.3 // Night/early morning = higher risk
        }
    }

    fn calculate_auth_risk(&self, method: &AuthMethod) -> f64 {
        match method {
            AuthMethod::Biometric => 0.0,
            AuthMethod::Certificate => 0.1,
            AuthMethod::MFA => 0.1,
            AuthMethod::SSO => 0.2,
            AuthMethod::Password => 0.4,
        }
    }

    async fn calculate_device_risk(&self, device_id: &str) -> f64 {
        // Check device trust status
        // Implementation would check device registration, compliance, etc.
        0.2 // Placeholder
    }

    async fn calculate_behavioral_risk(&self, request: &AccessRequest) -> f64 {
        // Analyze user behavior patterns
        // Implementation would use ML to detect anomalous behavior
        0.1 // Placeholder
    }
}

pub struct CryptographyService {
    encryption_keys: HashMap<String, EncryptionKey>,
    signing_keys: HashMap<String, SigningKey>,
}

#[derive(Debug, Clone)]
pub struct EncryptionKey {
    pub key_id: String,
    pub algorithm: EncryptionAlgorithm,
    pub key_data: Vec<u8>,
    pub created_at: DateTime<Utc>,
    pub expires_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone)]
pub struct SigningKey {
    pub key_id: String,
    pub algorithm: SigningAlgorithm,
    pub private_key: Vec<u8>,
    pub public_key: Vec<u8>,
    pub created_at: DateTime<Utc>,
    pub expires_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone)]
pub enum EncryptionAlgorithm {
    AES256GCM,
    ChaCha20Poly1305,
    AES256CBC,
}

#[derive(Debug, Clone)]
pub enum SigningAlgorithm {
    Ed25519,
    ECDSA,
    RSA,
}

impl CryptographyService {
    pub fn new() -> Self {
        let mut service = Self {
            encryption_keys: HashMap::new(),
            signing_keys: HashMap::new(),
        };

        service.initialize_keys();
        service
    }

    /// Encrypt data with end-to-end encryption
    pub fn encrypt_data(&self, data: &[u8], key_id: &str) -> Result<Vec<u8>, CryptoError> {
        let key = self.encryption_keys.get(key_id)
            .ok_or(CryptoError::KeyNotFound)?;

        match key.algorithm {
            EncryptionAlgorithm::AES256GCM => {
                self.encrypt_aes_gcm(data, &key.key_data)
            }
            EncryptionAlgorithm::ChaCha20Poly1305 => {
                self.encrypt_chacha20(data, &key.key_data)
            }
            _ => Err(CryptoError::UnsupportedAlgorithm),
        }
    }

    /// Decrypt data
    pub fn decrypt_data(&self, encrypted_data: &[u8], key_id: &str) -> Result<Vec<u8>, CryptoError> {
        let key = self.encryption_keys.get(key_id)
            .ok_or(CryptoError::KeyNotFound)?;

        match key.algorithm {
            EncryptionAlgorithm::AES256GCM => {
                self.decrypt_aes_gcm(encrypted_data, &key.key_data)
            }
            EncryptionAlgorithm::ChaCha20Poly1305 => {
                self.decrypt_chacha20(encrypted_data, &key.key_data)
            }
            _ => Err(CryptoError::UnsupportedAlgorithm),
        }
    }

    /// Sign data
    pub fn sign_data(&self, data: &[u8], key_id: &str) -> Result<Vec<u8>, CryptoError> {
        let key = self.signing_keys.get(key_id)
            .ok_or(CryptoError::KeyNotFound)?;

        match key.algorithm {
            SigningAlgorithm::Ed25519 => {
                self.sign_ed25519(data, &key.private_key)
            }
            _ => Err(CryptoError::UnsupportedAlgorithm),
        }
    }

    /// Verify signature
    pub fn verify_signature(&self, data: &[u8], signature: &[u8], key_id: &str) -> Result<bool, CryptoError> {
        let key = self.signing_keys.get(key_id)
            .ok_or(CryptoError::KeyNotFound)?;

        match key.algorithm {
            SigningAlgorithm::Ed25519 => {
                self.verify_ed25519(data, signature, &key.public_key)
            }
            _ => Err(CryptoError::UnsupportedAlgorithm),
        }
    }

    /// Rotate encryption keys
    pub async fn rotate_keys(&mut self) -> Result<(), CryptoError> {
        // Generate new keys
        let new_encryption_key = self.generate_encryption_key("primary")?;
        let new_signing_key = self.generate_signing_key("primary")?;

        // Store old keys with different IDs for decryption of old data
        if let Some(old_key) = self.encryption_keys.get("primary") {
            let old_key_id = format!("primary-{}", old_key.created_at.timestamp());
            self.encryption_keys.insert(old_key_id, old_key.clone());
        }

        // Update primary keys
        self.encryption_keys.insert("primary".to_string(), new_encryption_key);
        self.signing_keys.insert("primary".to_string(), new_signing_key);

        Ok(())
    }

    fn initialize_keys(&mut self) {
        // Generate initial keys
        if let Ok(enc_key) = self.generate_encryption_key("primary") {
            self.encryption_keys.insert("primary".to_string(), enc_key);
        }

        if let Ok(sign_key) = self.generate_signing_key("primary") {
            self.signing_keys.insert("primary".to_string(), sign_key);
        }
    }

    fn generate_encryption_key(&self, key_id: &str) -> Result<EncryptionKey, CryptoError> {
        use rand::RngCore;

        let mut key_data = vec![0u8; 32]; // 256-bit key
        rand::thread_rng().fill_bytes(&mut key_data);

        Ok(EncryptionKey {
            key_id: key_id.to_string(),
            algorithm: EncryptionAlgorithm::AES256GCM,
            key_data,
            created_at: Utc::now(),
            expires_at: Some(Utc::now() + Duration::days(90)), // 90-day rotation
        })
    }

    fn generate_signing_key(&self, key_id: &str) -> Result<SigningKey, CryptoError> {
        use ed25519_dalek::{Keypair, SecretKey};
        use rand::rngs::OsRng;

        let mut csprng = OsRng {};
        let keypair: Keypair = Keypair::generate(&mut csprng);

        Ok(SigningKey {
            key_id: key_id.to_string(),
            algorithm: SigningAlgorithm::Ed25519,
            private_key: keypair.secret.to_bytes().to_vec(),
            public_key: keypair.public.to_bytes().to_vec(),
            created_at: Utc::now(),
            expires_at: Some(Utc::now() + Duration::days(365)), // 1-year rotation
        })
    }

    fn encrypt_aes_gcm(&self, data: &[u8], key: &[u8]) -> Result<Vec<u8>, CryptoError> {
        use aes_gcm::{Aes256Gcm, Key, Nonce, aead::{Aead, NewAead}};
        use rand::RngCore;

        let cipher = Aes256Gcm::new(Key::from_slice(key));
        let mut nonce_bytes = [0u8; 12];
        rand::thread_rng().fill_bytes(&mut nonce_bytes);
        let nonce = Nonce::from_slice(&nonce_bytes);

        let ciphertext = cipher.encrypt(nonce, data)
            .map_err(|_| CryptoError::EncryptionFailed)?;

        // Prepend nonce to ciphertext
        let mut result = nonce_bytes.to_vec();
        result.extend_from_slice(&ciphertext);

        Ok(result)
    }

    fn decrypt_aes_gcm(&self, encrypted_data: &[u8], key: &[u8]) -> Result<Vec<u8>, CryptoError> {
        use aes_gcm::{Aes256Gcm, Key, Nonce, aead::{Aead, NewAead}};

        if encrypted_data.len() < 12 {
            return Err(CryptoError::InvalidData);
        }

        let cipher = Aes256Gcm::new(Key::from_slice(key));
        let nonce = Nonce::from_slice(&encrypted_data[..12]);
        let ciphertext = &encrypted_data[12..];

        cipher.decrypt(nonce, ciphertext)
            .map_err(|_| CryptoError::DecryptionFailed)
    }

    fn encrypt_chacha20(&self, data: &[u8], key: &[u8]) -> Result<Vec<u8>, CryptoError> {
        // ChaCha20-Poly1305 implementation would go here
        Err(CryptoError::UnsupportedAlgorithm)
    }

    fn decrypt_chacha20(&self, encrypted_data: &[u8], key: &[u8]) -> Result<Vec<u8>, CryptoError> {
        // ChaCha20-Poly1305 implementation would go here
        Err(CryptoError::UnsupportedAlgorithm)
    }

    fn sign_ed25519(&self, data: &[u8], private_key: &[u8]) -> Result<Vec<u8>, CryptoError> {
        use ed25519_dalek::{SecretKey, Signature, Signer};

        let secret_key = SecretKey::from_bytes(private_key)
            .map_err(|_| CryptoError::InvalidKey)?;
        let keypair = ed25519_dalek::Keypair::from(secret_key);

        let signature: Signature = keypair.sign(data);
        Ok(signature.to_bytes().to_vec())
    }

    fn verify_ed25519(&self, data: &[u8], signature: &[u8], public_key: &[u8]) -> Result<bool, CryptoError> {
        use ed25519_dalek::{PublicKey, Signature, Verifier};

        let public_key = PublicKey::from_bytes(public_key)
            .map_err(|_| CryptoError::InvalidKey)?;
        let signature = Signature::from_bytes(signature)
            .map_err(|_| CryptoError::InvalidSignature)?;

        Ok(public_key.verify(data, &signature).is_ok())
    }
}

pub struct DeviceTrustManager {
    trusted_devices: HashMap<String, TrustedDevice>,
}

#[derive(Debug, Clone)]
pub struct TrustedDevice {
    pub device_id: String,
    pub user_id: Uuid,
    pub device_name: String,
    pub device_type: String,
    pub fingerprint: String,
    pub trust_level: TrustLevel,
    pub registered_at: DateTime<Utc>,
    pub last_seen: DateTime<Utc>,
}

#[derive(Debug, Clone)]
pub enum TrustLevel {
    Untrusted,
    Pending,
    Trusted,
    HighlyTrusted,
}

impl DeviceTrustManager {
    pub fn new() -> Self {
        Self {
            trusted_devices: HashMap::new(),
        }
    }

    pub async fn register_device(&mut self, device: TrustedDevice) -> Result<(), DeviceError> {
        self.trusted_devices.insert(device.device_id.clone(), device);
        Ok(())
    }

    pub async fn get_device_trust_level(&self, device_id: &str) -> TrustLevel {
        self.trusted_devices
            .get(device_id)
            .map(|device| device.trust_level.clone())
            .unwrap_or(TrustLevel::Untrusted)
    }
}

#[derive(Debug, thiserror::Error)]
pub enum CryptoError {
    #[error("Key not found")]
    KeyNotFound,
    #[error("Unsupported algorithm")]
    UnsupportedAlgorithm,
    #[error("Encryption failed")]
    EncryptionFailed,
    #[error("Decryption failed")]
    DecryptionFailed,
    #[error("Invalid key")]
    InvalidKey,
    #[error("Invalid signature")]
    InvalidSignature,
    #[error("Invalid data")]
    InvalidData,
}

#[derive(Debug, thiserror::Error)]
pub enum DeviceError {
    #[error("Device not found")]
    DeviceNotFound,
    #[error("Device not trusted")]
    DeviceNotTrusted,
}
```

## 🎯 Key Takeaways

### Advanced Security Benefits

1. **Comprehensive Threat Protection**: Multi-layered defense against sophisticated attacks
2. **Zero Trust Architecture**: Never trust, always verify approach
3. **Real-time Detection**: Immediate threat identification and response
4. **Advanced Cryptography**: State-of-the-art encryption and key management
5. **Compliance Ready**: Built-in compliance for major security standards

### Security Features Implemented

- **DDoS Protection**: Intelligent rate limiting with threat analysis
- **Web Application Firewall**: Real-time attack detection and blocking
- **Intrusion Detection**: Pattern-based and ML-powered threat detection
- **Zero Trust Policies**: Dynamic access control based on risk assessment
- **Advanced Cryptography**: End-to-end encryption with key rotation
- **Device Trust Management**: Device registration and trust scoring

### Performance Considerations

- **Async Processing**: Non-blocking security checks
- **Efficient Pattern Matching**: Optimized regex and ML inference
- **Caching**: Cache security decisions and risk scores
- **Batch Processing**: Process security events efficiently
- **Resource Management**: Prevent security systems from becoming bottlenecks

### Security Best Practices

- **Defense in Depth**: Multiple security layers working together
- **Principle of Least Privilege**: Minimal required access permissions
- **Continuous Monitoring**: Real-time security event analysis
- **Automated Response**: Immediate threat mitigation
- **Regular Updates**: Continuous security rule and pattern updates

Ready to continue with [Module 18: Performance Optimization & Scalability](./module-18-performance-optimization.md)?
