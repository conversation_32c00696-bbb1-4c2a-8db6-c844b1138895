# Module 8: CI/CD Pipeline for Server Deployment

## 🎯 Learning Objectives

By the end of this module, you will:
- Build comprehensive CI/CD pipelines with GitHub Actions
- Master Docker containerization for Rust and Angular applications
- Deploy to Kubernetes with proper configuration management
- Implement automated testing, security scanning, and monitoring
- Understand Infrastructure as Code with Terraform
- Create production-ready deployment strategies

## 🚀 Why CI/CD Matters for Git Servers

Git servers require high availability and reliability. A robust CI/CD pipeline ensures:

- **Zero-downtime deployments**: Rolling updates without service interruption
- **Automated testing**: Catch bugs before they reach production
- **Security scanning**: Detect vulnerabilities in dependencies
- **Consistent environments**: Same configuration from dev to production
- **Rollback capability**: Quick recovery from failed deployments

### CI/CD Pipeline Architecture

```mermaid
graph TB
    subgraph "Source Control"
        GIT[Git Repository]
        PR[Pull Request]
        MAIN[Main Branch]
    end
    
    subgraph "CI Pipeline"
        BUILD[Build & Test]
        LINT[Linting & Formatting]
        SECURITY[Security Scan]
        DOCKER[Docker Build]
        PUSH[Push to Registry]
    end
    
    subgraph "CD Pipeline"
        STAGING[Deploy to Staging]
        E2E[E2E Tests]
        PROD[Deploy to Production]
        MONITOR[Health Monitoring]
    end
    
    subgraph "Infrastructure"
        K8S[Kubernetes Cluster]
        DB[(Database)]
        REDIS[(Redis)]
        MONITORING[Prometheus/Grafana]
    end
    
    PR --> BUILD
    BUILD --> LINT
    LINT --> SECURITY
    SECURITY --> DOCKER
    DOCKER --> PUSH
    
    MAIN --> STAGING
    STAGING --> E2E
    E2E --> PROD
    PROD --> MONITOR
    
    PROD --> K8S
    K8S --> DB
    K8S --> REDIS
    K8S --> MONITORING
    
    style BUILD fill:#e8f5e8
    style PROD fill:#fff3e0
    style MONITOR fill:#e1f5fe
```

## 🐳 Docker Containerization

### Multi-stage Dockerfile for Rust Backend

```dockerfile
# Dockerfile.backend
# Build stage
FROM rust:1.75-slim as builder

# Install system dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN useradd -m -u 1001 appuser

WORKDIR /app

# Copy dependency files first for better caching
COPY Cargo.toml Cargo.lock ./
COPY src ./src

# Build the application
RUN cargo build --release

# Runtime stage
FROM debian:bookworm-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    libpq5 \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN useradd -m -u 1001 appuser

WORKDIR /app

# Copy the binary from builder stage
COPY --from=builder /app/target/release/rusty-git-server ./
COPY --from=builder /etc/passwd /etc/passwd

# Create directory for Git repositories
RUN mkdir -p /app/repositories && chown appuser:appuser /app/repositories

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Run the application
CMD ["./rusty-git-server"]
```

### Dockerfile for Angular Frontend

```dockerfile
# Dockerfile.frontend
# Build stage
FROM node:18-alpine as builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build the application
RUN npm run build --prod

# Runtime stage
FROM nginx:alpine

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Copy built application
COPY --from=builder /app/dist/rusty-git-frontend /usr/share/nginx/html

# Create non-root user
RUN addgroup -g 1001 -S appgroup && \
    adduser -S appuser -u 1001 -G appgroup

# Change ownership of nginx directories
RUN chown -R appuser:appgroup /var/cache/nginx /var/run /var/log/nginx /usr/share/nginx/html

# Switch to non-root user
USER appuser

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### Docker Compose for Development

```yaml
# docker-compose.yml
version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.backend
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=********************************************/rusty_git
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-super-secret-jwt-key
      - RUST_LOG=info
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - git_repositories:/app/repositories
    networks:
      - app-network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.frontend
    ports:
      - "4200:80"
    depends_on:
      - backend
    networks:
      - app-network

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=rusty_git
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - app-network

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    networks:
      - app-network

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    networks:
      - app-network

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - app-network

volumes:
  postgres_data:
  redis_data:
  git_repositories:
  prometheus_data:
  grafana_data:

networks:
  app-network:
    driver: bridge
```

## 🔄 GitHub Actions CI/CD Pipeline

### Comprehensive CI Pipeline

```yaml
# .github/workflows/ci.yml
name: CI Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  CARGO_TERM_COLOR: always
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Backend testing and building
  backend-test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v4

    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable
      with:
        components: rustfmt, clippy

    - name: Cache Rust dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/bin/
          ~/.cargo/registry/index/
          ~/.cargo/registry/cache/
          ~/.cargo/git/db/
          backend/target/
        key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y libpq-dev

    - name: Check formatting
      run: cd backend && cargo fmt --all -- --check

    - name: Run Clippy
      run: cd backend && cargo clippy --all-targets --all-features -- -D warnings

    - name: Run tests
      run: cd backend && cargo test --verbose
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379

    - name: Run integration tests
      run: cd backend && cargo test --test integration_tests
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379

    - name: Generate test coverage
      run: |
        cd backend
        cargo install cargo-tarpaulin
        cargo tarpaulin --out xml --output-dir coverage

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: backend/coverage/cobertura.xml
        flags: backend

  # Frontend testing and building
  frontend-test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: Install dependencies
      run: cd frontend && npm ci

    - name: Run linting
      run: cd frontend && npm run lint

    - name: Run unit tests
      run: cd frontend && npm run test:ci

    - name: Run e2e tests
      run: cd frontend && npm run e2e:ci

    - name: Build application
      run: cd frontend && npm run build --prod

    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: frontend-build
        path: frontend/dist/

  # Security scanning
  security-scan:
    runs-on: ubuntu-latest
    needs: [backend-test, frontend-test]

    steps:
    - uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

    - name: Audit Rust dependencies
      run: |
        cd backend
        cargo install cargo-audit
        cargo audit

    - name: Audit Node.js dependencies
      run: cd frontend && npm audit --audit-level high

  # Build and push Docker images
  build-images:
    runs-on: ubuntu-latest
    needs: [backend-test, frontend-test, security-scan]
    if: github.ref == 'refs/heads/main'

    permissions:
      contents: read
      packages: write

    steps:
    - uses: actions/checkout@v4

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta-backend
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-backend
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push backend image
      uses: docker/build-push-action@v5
      with:
        context: ./backend
        file: ./backend/Dockerfile.backend
        push: true
        tags: ${{ steps.meta-backend.outputs.tags }}
        labels: ${{ steps.meta-backend.outputs.labels }}

    - name: Extract metadata for frontend
      id: meta-frontend
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-frontend
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push frontend image
      uses: docker/build-push-action@v5
      with:
        context: ./frontend
        file: ./frontend/Dockerfile.frontend
        push: true
        tags: ${{ steps.meta-frontend.outputs.tags }}
        labels: ${{ steps.meta-frontend.outputs.labels }}
```

### Deployment Pipeline

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  workflow_run:
    workflows: ["CI Pipeline"]
    types:
      - completed
    branches: [main]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  deploy-staging:
    runs-on: ubuntu-latest
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    environment: staging

    steps:
    - uses: actions/checkout@v4

    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG_STAGING }}

    - name: Deploy to staging
      run: |
        # Update image tags in Kubernetes manifests
        sed -i "s|IMAGE_TAG|${{ github.sha }}|g" k8s/staging/*.yaml
        
        # Apply Kubernetes manifests
        kubectl apply -f k8s/staging/

    - name: Wait for deployment
      run: |
        kubectl rollout status deployment/rusty-git-backend -n staging --timeout=300s
        kubectl rollout status deployment/rusty-git-frontend -n staging --timeout=300s

    - name: Run smoke tests
      run: |
        # Wait for service to be ready
        sleep 30
        
        # Run basic health checks
        curl -f https://staging.git.example.com/health
        curl -f https://staging.git.example.com/api/health

  deploy-production:
    runs-on: ubuntu-latest
    needs: deploy-staging
    environment: production
    if: success()

    steps:
    - uses: actions/checkout@v4

    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG_PRODUCTION }}

    - name: Deploy to production
      run: |
        # Update image tags in Kubernetes manifests
        sed -i "s|IMAGE_TAG|${{ github.sha }}|g" k8s/production/*.yaml
        
        # Apply Kubernetes manifests with rolling update
        kubectl apply -f k8s/production/

    - name: Wait for deployment
      run: |
        kubectl rollout status deployment/rusty-git-backend -n production --timeout=600s
        kubectl rollout status deployment/rusty-git-frontend -n production --timeout=600s

    - name: Run production health checks
      run: |
        # Wait for service to be ready
        sleep 60
        
        # Comprehensive health checks
        curl -f https://git.example.com/health
        curl -f https://git.example.com/api/health
        
        # Test critical functionality
        ./scripts/production-smoke-tests.sh

    - name: Notify deployment success
      uses: 8398a7/action-slack@v3
      with:
        status: success
        text: "🚀 Rusty Git Server deployed successfully to production!"
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}

    - name: Notify deployment failure
      if: failure()
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        text: "❌ Rusty Git Server deployment to production failed!"
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}
```

## ☸️ Kubernetes Deployment

### Backend Deployment Configuration

```yaml
# k8s/production/backend-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rusty-git-backend
  namespace: production
  labels:
    app: rusty-git-backend
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: rusty-git-backend
  template:
    metadata:
      labels:
        app: rusty-git-backend
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: rusty-git-backend
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
      containers:
      - name: backend
        image: ghcr.io/your-org/rusty-git-server-backend:IMAGE_TAG
        ports:
        - containerPort: 3000
          name: http
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: jwt-secret
              key: secret
        - name: RUST_LOG
          value: "info"
        - name: SERVER_PORT
          value: "3000"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: git-repositories
          mountPath: /app/repositories
        - name: config
          mountPath: /app/config
          readOnly: true
      volumes:
      - name: git-repositories
        persistentVolumeClaim:
          claimName: git-repositories-pvc
      - name: config
        configMap:
          name: backend-config
      nodeSelector:
        node-type: application
      tolerations:
      - key: "application"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"

---
apiVersion: v1
kind: Service
metadata:
  name: rusty-git-backend-service
  namespace: production
  labels:
    app: rusty-git-backend
spec:
  selector:
    app: rusty-git-backend
  ports:
  - name: http
    port: 80
    targetPort: 3000
    protocol: TCP
  type: ClusterIP

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: rusty-git-backend
  namespace: production
```

### Frontend Deployment Configuration

```yaml
# k8s/production/frontend-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rusty-git-frontend
  namespace: production
  labels:
    app: rusty-git-frontend
    version: v1
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: rusty-git-frontend
  template:
    metadata:
      labels:
        app: rusty-git-frontend
        version: v1
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
      containers:
      - name: frontend
        image: ghcr.io/your-org/rusty-git-server-frontend:IMAGE_TAG
        ports:
        - containerPort: 80
          name: http
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/nginx.conf
          subPath: nginx.conf
          readOnly: true
      volumes:
      - name: nginx-config
        configMap:
          name: frontend-nginx-config
      nodeSelector:
        node-type: application

---
apiVersion: v1
kind: Service
metadata:
  name: rusty-git-frontend-service
  namespace: production
  labels:
    app: rusty-git-frontend
spec:
  selector:
    app: rusty-git-frontend
  ports:
  - name: http
    port: 80
    targetPort: 80
    protocol: TCP
  type: ClusterIP
```

### Ingress Configuration

```yaml
# k8s/production/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: rusty-git-ingress
  namespace: production
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "1g"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - git.example.com
    secretName: rusty-git-tls
  rules:
  - host: git.example.com
    http:
      paths:
      # API routes
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: rusty-git-backend-service
            port:
              number: 80
      # Git operations
      - path: /git
        pathType: Prefix
        backend:
          service:
            name: rusty-git-backend-service
            port:
              number: 80
      # WebSocket connections
      - path: /ws
        pathType: Prefix
        backend:
          service:
            name: rusty-git-backend-service
            port:
              number: 80
      # Frontend (catch-all)
      - path: /
        pathType: Prefix
        backend:
          service:
            name: rusty-git-frontend-service
            port:
              number: 80
```

### Database and Storage Configuration

```yaml
# k8s/production/postgres.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
  namespace: production
spec:
  serviceName: postgres-service
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      securityContext:
        runAsUser: 999
        runAsGroup: 999
        fsGroup: 999
      containers:
      - name: postgres
        image: postgres:15-alpine
        env:
        - name: POSTGRES_DB
          value: rusty_git
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: username
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: password
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        ports:
        - containerPort: 5432
          name: postgres
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        - name: postgres-config
          mountPath: /etc/postgresql/postgresql.conf
          subPath: postgresql.conf
          readOnly: true
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - $(POSTGRES_USER)
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - $(POSTGRES_USER)
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgres-config
        configMap:
          name: postgres-config
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: fast-ssd
      resources:
        requests:
          storage: 100Gi

---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: production
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
  type: ClusterIP

---
# Persistent Volume Claim for Git repositories
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: git-repositories-pvc
  namespace: production
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: nfs-storage
  resources:
    requests:
      storage: 1Ti
```

## 🏗️ Infrastructure as Code with Terraform

### AWS EKS Cluster Configuration

```hcl
# terraform/main.tf
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.0"
    }
  }

  backend "s3" {
    bucket = "your-terraform-state-bucket"
    key    = "rusty-git-server/terraform.tfstate"
    region = "us-west-2"
  }
}

provider "aws" {
  region = var.aws_region
}

# VPC Configuration
module "vpc" {
  source = "terraform-aws-modules/vpc/aws"

  name = "rusty-git-vpc"
  cidr = "10.0.0.0/16"

  azs             = ["${var.aws_region}a", "${var.aws_region}b", "${var.aws_region}c"]
  private_subnets = ["********/24", "********/24", "********/24"]
  public_subnets  = ["**********/24", "**********/24", "**********/24"]

  enable_nat_gateway = true
  enable_vpn_gateway = false
  enable_dns_hostnames = true
  enable_dns_support = true

  tags = {
    Environment = var.environment
    Project     = "rusty-git-server"
  }
}

# EKS Cluster
module "eks" {
  source = "terraform-aws-modules/eks/aws"

  cluster_name    = "rusty-git-cluster"
  cluster_version = "1.28"

  vpc_id     = module.vpc.vpc_id
  subnet_ids = module.vpc.private_subnets

  # Cluster endpoint configuration
  cluster_endpoint_private_access = true
  cluster_endpoint_public_access  = true
  cluster_endpoint_public_access_cidrs = ["0.0.0.0/0"]

  # Node groups
  eks_managed_node_groups = {
    application = {
      name = "application-nodes"

      instance_types = ["t3.large"]
      capacity_type  = "ON_DEMAND"

      min_size     = 2
      max_size     = 10
      desired_size = 3

      labels = {
        node-type = "application"
      }

      taints = {
        application = {
          key    = "application"
          value  = "true"
          effect = "NO_SCHEDULE"
        }
      }
    }

    database = {
      name = "database-nodes"

      instance_types = ["r5.xlarge"]
      capacity_type  = "ON_DEMAND"

      min_size     = 1
      max_size     = 3
      desired_size = 1

      labels = {
        node-type = "database"
      }

      taints = {
        database = {
          key    = "database"
          value  = "true"
          effect = "NO_SCHEDULE"
        }
      }
    }
  }

  tags = {
    Environment = var.environment
    Project     = "rusty-git-server"
  }
}

# RDS PostgreSQL Database
resource "aws_db_instance" "postgres" {
  identifier = "rusty-git-postgres"

  engine         = "postgres"
  engine_version = "15.4"
  instance_class = "db.r5.large"

  allocated_storage     = 100
  max_allocated_storage = 1000
  storage_type         = "gp3"
  storage_encrypted    = true

  db_name  = "rusty_git"
  username = var.db_username
  password = var.db_password

  vpc_security_group_ids = [aws_security_group.rds.id]
  db_subnet_group_name   = aws_db_subnet_group.postgres.name

  backup_retention_period = 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"

  skip_final_snapshot = false
  final_snapshot_identifier = "rusty-git-postgres-final-snapshot"

  tags = {
    Environment = var.environment
    Project     = "rusty-git-server"
  }
}

# ElastiCache Redis
resource "aws_elasticache_subnet_group" "redis" {
  name       = "rusty-git-redis-subnet-group"
  subnet_ids = module.vpc.private_subnets
}

resource "aws_elasticache_replication_group" "redis" {
  replication_group_id       = "rusty-git-redis"
  description                = "Redis cluster for Rusty Git Server"

  node_type                  = "cache.r6g.large"
  port                       = 6379
  parameter_group_name       = "default.redis7"

  num_cache_clusters         = 2
  automatic_failover_enabled = true
  multi_az_enabled          = true

  subnet_group_name = aws_elasticache_subnet_group.redis.name
  security_group_ids = [aws_security_group.redis.id]

  at_rest_encryption_enabled = true
  transit_encryption_enabled = true

  tags = {
    Environment = var.environment
    Project     = "rusty-git-server"
  }
}

# Security Groups
resource "aws_security_group" "rds" {
  name_prefix = "rusty-git-rds-"
  vpc_id      = module.vpc.vpc_id

  ingress {
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    cidr_blocks = [module.vpc.vpc_cidr_block]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "rusty-git-rds-sg"
  }
}

resource "aws_security_group" "redis" {
  name_prefix = "rusty-git-redis-"
  vpc_id      = module.vpc.vpc_id

  ingress {
    from_port   = 6379
    to_port     = 6379
    protocol    = "tcp"
    cidr_blocks = [module.vpc.vpc_cidr_block]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "rusty-git-redis-sg"
  }
}

# Variables
variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "us-west-2"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "production"
}

variable "db_username" {
  description = "Database username"
  type        = string
  sensitive   = true
}

variable "db_password" {
  description = "Database password"
  type        = string
  sensitive   = true
}

# Outputs
output "cluster_endpoint" {
  description = "EKS cluster endpoint"
  value       = module.eks.cluster_endpoint
}

output "cluster_name" {
  description = "EKS cluster name"
  value       = module.eks.cluster_name
}

output "database_endpoint" {
  description = "RDS instance endpoint"
  value       = aws_db_instance.postgres.endpoint
  sensitive   = true
}

output "redis_endpoint" {
  description = "Redis cluster endpoint"
  value       = aws_elasticache_replication_group.redis.primary_endpoint_address
  sensitive   = true
}
```

## 🎯 Key Takeaways

### Production Deployment Checklist

1. **Security**:
   - ✅ Non-root containers
   - ✅ Secret management
   - ✅ Network policies
   - ✅ RBAC configuration
   - ✅ TLS encryption

2. **Reliability**:
   - ✅ Health checks
   - ✅ Rolling updates
   - ✅ Resource limits
   - ✅ Persistent storage
   - ✅ Backup strategies

3. **Scalability**:
   - ✅ Horizontal pod autoscaling
   - ✅ Cluster autoscaling
   - ✅ Load balancing
   - ✅ Database read replicas
   - ✅ CDN integration

4. **Observability**:
   - ✅ Metrics collection
   - ✅ Centralized logging
   - ✅ Distributed tracing
   - ✅ Alerting rules
   - ✅ Dashboard creation

5. **Automation**:
   - ✅ CI/CD pipelines
   - ✅ Infrastructure as Code
   - ✅ Automated testing
   - ✅ Security scanning
   - ✅ Deployment strategies

## 🎉 Congratulations!

You've completed the comprehensive Rusty Git Server tutorial! You now have:

- **Deep Rust Knowledge**: Ownership, async programming, performance optimization
- **Modern Angular Skills**: TypeScript, reactive programming, component architecture
- **System Design Expertise**: Scalable architecture, caching, real-time features
- **DevOps Mastery**: CI/CD, containerization, Kubernetes, monitoring
- **Production Experience**: Security, reliability, performance, observability

Your Git server is now ready for production deployment with enterprise-grade features!
