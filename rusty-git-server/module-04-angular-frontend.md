# Module 4: Angular Frontend Setup

## 🎯 Learning Objectives

By the end of this module, you will:
- Understand TypeScript fundamentals and why it's superior to JavaScript
- Master Angular's component architecture and lifecycle
- Implement reactive programming with RxJS
- Create a responsive UI with Angular Material
- Build authentication flows and HTTP interceptors
- Understand dependency injection and services

## 🤔 Why Angular? A Deep Dive

Coming from JavaScript, you might wonder why we chose Angular over React or Vue. Let me explain:

### TypeScript: JavaScript That Scales

**JavaScript Challenges**:
```javascript
// JavaScript - Runtime errors waiting to happen
function getUserData(userId) {
    return fetch(`/api/users/${userId}`)
        .then(response => response.json())
        .then(data => {
            // What if data.name doesn't exist?
            return data.name.toUpperCase();
        });
}
```

**TypeScript Solution**:
```typescript
// TypeScript - Compile-time safety
interface User {
    id: string;
    name: string;
    email: string;
    role: 'admin' | 'user' | 'readonly';
}

async function getUserData(userId: string): Promise<string> {
    const response = await fetch(`/api/users/${userId}`);
    const data: User = await response.json();
    // Compiler ensures data.name exists and is a string
    return data.name.toUpperCase();
}
```

### Angular vs React vs Vue

```mermaid
graph TB
    subgraph "Framework Comparison"
        A[Angular]
        B[React]
        C[Vue]
    end
    
    subgraph "Angular Strengths"
        A1[Full Framework]
        A2[TypeScript First]
        A3[Dependency Injection]
        A4[Opinionated Structure]
        A5[Enterprise Ready]
    end
    
    subgraph "React Strengths"
        B1[Flexibility]
        B2[Large Ecosystem]
        B3[Simple Learning Curve]
        B4[Virtual DOM]
    end
    
    subgraph "Vue Strengths"
        C1[Easy to Learn]
        C2[Great Documentation]
        C3[Progressive Framework]
        C4[Template Syntax]
    end
    
    A --> A1
    A --> A2
    A --> A3
    A --> A4
    A --> A5
    
    B --> B1
    B --> B2
    B --> B3
    B --> B4
    
    C --> C1
    C --> C2
    C --> C3
    C --> C4
```

**Why Angular for Our Git Server**:
1. **Type Safety**: Catches errors at compile time
2. **Structure**: Opinionated architecture scales well
3. **Tooling**: Excellent CLI and development tools
4. **Enterprise Features**: Built-in routing, forms, HTTP client
5. **Long-term Support**: Backed by Google with predictable releases

## 🛠️ Setting Up Angular Development Environment

### 1. Install Node.js and Angular CLI

```bash
# Install Node.js (LTS version recommended)
# Download from https://nodejs.org or use a version manager

# Verify installation
node --version  # Should be 18+ or 20+
npm --version

# Install Angular CLI globally
npm install -g @angular/cli

# Verify Angular CLI
ng version
```

### 2. Create Angular Project

```bash
# Create new Angular project
ng new rusty-git-frontend --routing --style=scss --strict

# Navigate to project
cd rusty-git-frontend

# Install Angular Material
ng add @angular/material

# Choose a theme (e.g., Indigo/Pink)
# Set up global typography styles: Yes
# Include Angular Animations: Yes
```

### 3. Project Structure Overview

```
rusty-git-frontend/
├── src/
│   ├── app/
│   │   ├── components/          # Reusable UI components
│   │   ├── pages/              # Route components
│   │   ├── services/           # Business logic and API calls
│   │   ├── models/             # TypeScript interfaces
│   │   ├── guards/             # Route guards for authentication
│   │   ├── interceptors/       # HTTP interceptors
│   │   └── shared/             # Shared utilities
│   ├── assets/                 # Static files
│   ├── environments/           # Environment configurations
│   └── styles.scss            # Global styles
├── angular.json               # Angular CLI configuration
├── package.json              # Dependencies
└── tsconfig.json            # TypeScript configuration
```

## 📚 TypeScript Fundamentals

### Interfaces vs Classes

**JavaScript Object** (No type safety):
```javascript
const user = {
    id: 1,
    name: "John Doe",
    email: "<EMAIL>"
};

// Oops, typo in property name - runtime error
console.log(user.naem); // undefined
```

**TypeScript Interface** (Compile-time safety):
```typescript
interface User {
    id: number;
    name: string;
    email: string;
    role?: 'admin' | 'user'; // Optional property with union types
}

const user: User = {
    id: 1,
    name: "John Doe",
    email: "<EMAIL>"
};

// Compiler catches typo immediately
console.log(user.naem); // ❌ Property 'naem' does not exist
console.log(user.name); // ✅ Works perfectly
```

### Generics for Type Safety

```typescript
// Generic service for API calls
class ApiService<T> {
    constructor(private baseUrl: string) {}
    
    async get(id: string): Promise<T> {
        const response = await fetch(`${this.baseUrl}/${id}`);
        return response.json() as T;
    }
    
    async create(data: Omit<T, 'id'>): Promise<T> {
        const response = await fetch(this.baseUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        });
        return response.json() as T;
    }
}

// Usage with type safety
const userService = new ApiService<User>('/api/users');
const user = await userService.get('123'); // user is typed as User
```

### Union Types and Type Guards

```typescript
type LoadingState = 'idle' | 'loading' | 'success' | 'error';

interface ApiResponse<T> {
    state: LoadingState;
    data?: T;
    error?: string;
}

// Type guard function
function isSuccess<T>(response: ApiResponse<T>): response is ApiResponse<T> & { data: T } {
    return response.state === 'success' && response.data !== undefined;
}

// Usage
const response: ApiResponse<User> = await fetchUser();
if (isSuccess(response)) {
    // TypeScript knows response.data exists and is of type User
    console.log(response.data.name);
}
```

## 🏗️ Angular Architecture Fundamentals

### Component Lifecycle

```typescript
import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { Subscription } from 'rxjs';

@Component({
    selector: 'app-repository-card',
    template: `
        <mat-card class="repository-card">
            <mat-card-header>
                <mat-card-title>{{ repository.name }}</mat-card-title>
                <mat-card-subtitle>{{ repository.description }}</mat-card-subtitle>
            </mat-card-header>
            <mat-card-content>
                <p>Created: {{ repository.createdAt | date }}</p>
                <p>Owner: {{ repository.owner.username }}</p>
            </mat-card-content>
            <mat-card-actions>
                <button mat-button (click)="onClone()">Clone</button>
                <button mat-button (click)="onView()">View</button>
            </mat-card-actions>
        </mat-card>
    `,
    styleUrls: ['./repository-card.component.scss']
})
export class RepositoryCardComponent implements OnInit, OnDestroy {
    @Input() repository!: Repository; // Input from parent component
    @Output() clone = new EventEmitter<Repository>(); // Output to parent
    @Output() view = new EventEmitter<Repository>();
    
    private subscription = new Subscription();
    
    ngOnInit(): void {
        // Component initialization logic
        console.log('Repository card initialized for:', this.repository.name);
    }
    
    ngOnDestroy(): void {
        // Cleanup subscriptions to prevent memory leaks
        this.subscription.unsubscribe();
    }
    
    onClone(): void {
        this.clone.emit(this.repository);
    }
    
    onView(): void {
        this.view.emit(this.repository);
    }
}
```

### Services and Dependency Injection

```typescript
// models/repository.model.ts
export interface Repository {
    id: string;
    name: string;
    description?: string;
    owner: User;
    isPrivate: boolean;
    createdAt: Date;
    updatedAt: Date;
}

export interface CreateRepositoryRequest {
    name: string;
    description?: string;
    isPrivate: boolean;
}

// services/repository.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, tap } from 'rxjs/operators';

@Injectable({
    providedIn: 'root' // Singleton service
})
export class RepositoryService {
    private readonly apiUrl = '/api/repositories';
    private repositoriesSubject = new BehaviorSubject<Repository[]>([]);
    
    // Public observable for components to subscribe to
    public repositories$ = this.repositoriesSubject.asObservable();
    
    constructor(private http: HttpClient) {}
    
    loadRepositories(): Observable<Repository[]> {
        return this.http.get<Repository[]>(this.apiUrl).pipe(
            map(repos => repos.map(repo => ({
                ...repo,
                createdAt: new Date(repo.createdAt),
                updatedAt: new Date(repo.updatedAt)
            }))),
            tap(repos => this.repositoriesSubject.next(repos))
        );
    }
    
    createRepository(request: CreateRepositoryRequest): Observable<Repository> {
        return this.http.post<Repository>(this.apiUrl, request).pipe(
            tap(newRepo => {
                const currentRepos = this.repositoriesSubject.value;
                this.repositoriesSubject.next([...currentRepos, newRepo]);
            })
        );
    }
    
    getRepository(id: string): Observable<Repository> {
        return this.http.get<Repository>(`${this.apiUrl}/${id}`);
    }
}
```

## 🔄 Reactive Programming with RxJS

### Understanding Observables

**Promise vs Observable**:
```typescript
// Promise - Single value, not cancellable
const userPromise: Promise<User> = fetch('/api/user/123').then(r => r.json());

// Observable - Stream of values, cancellable, composable
const user$: Observable<User> = this.http.get<User>('/api/user/123');

// Observable can emit multiple values over time
const searchResults$ = this.searchInput$.pipe(
    debounceTime(300),           // Wait 300ms after user stops typing
    distinctUntilChanged(),      // Only emit if search term changed
    switchMap(term =>            // Cancel previous request, start new one
        this.searchService.search(term)
    )
);
```

### Reactive Component Pattern

```typescript
@Component({
    selector: 'app-repository-list',
    template: `
        <div class="repository-list">
            <mat-form-field>
                <input matInput 
                       placeholder="Search repositories..." 
                       [formControl]="searchControl">
            </mat-form-field>
            
            <div *ngIf="loading$ | async" class="loading">
                <mat-spinner></mat-spinner>
            </div>
            
            <div *ngIf="error$ | async as error" class="error">
                {{ error }}
            </div>
            
            <div class="repositories">
                <app-repository-card 
                    *ngFor="let repo of filteredRepositories$ | async"
                    [repository]="repo"
                    (clone)="onClone($event)"
                    (view)="onView($event)">
                </app-repository-card>
            </div>
        </div>
    `
})
export class RepositoryListComponent implements OnInit {
    searchControl = new FormControl('');
    
    repositories$ = this.repositoryService.repositories$;
    loading$ = new BehaviorSubject<boolean>(false);
    error$ = new BehaviorSubject<string | null>(null);
    
    filteredRepositories$ = combineLatest([
        this.repositories$,
        this.searchControl.valueChanges.pipe(startWith(''))
    ]).pipe(
        map(([repositories, searchTerm]) =>
            repositories.filter(repo =>
                repo.name.toLowerCase().includes(searchTerm?.toLowerCase() || '')
            )
        )
    );
    
    constructor(private repositoryService: RepositoryService) {}
    
    ngOnInit(): void {
        this.loadRepositories();
    }
    
    private loadRepositories(): void {
        this.loading$.next(true);
        this.error$.next(null);
        
        this.repositoryService.loadRepositories().subscribe({
            next: () => {
                this.loading$.next(false);
            },
            error: (error) => {
                this.loading$.next(false);
                this.error$.next('Failed to load repositories');
                console.error('Error loading repositories:', error);
            }
        });
    }
    
    onClone(repository: Repository): void {
        // Handle repository cloning
        console.log('Cloning repository:', repository.name);
    }
    
    onView(repository: Repository): void {
        // Navigate to repository view
        this.router.navigate(['/repositories', repository.id]);
    }
}
```

## 🎯 Key Takeaways

1. **TypeScript Benefits**: Compile-time error checking, better IDE support, self-documenting code
2. **Angular Architecture**: Component-based, dependency injection, reactive patterns
3. **RxJS Power**: Composable async operations, reactive data flows
4. **Material Design**: Consistent, accessible UI components
5. **Best Practices**: Unsubscribe from observables, use OnPush change detection, lazy loading

## 🚀 Next Steps

In the next section, we'll implement:
- Authentication guards and interceptors
- Routing and navigation
- Forms with validation
- Error handling and loading states
- Responsive design patterns

## 🔐 Authentication Implementation

### Authentication Service

```typescript
// services/auth.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, tap } from 'rxjs';
import { Router } from '@angular/router';

export interface LoginRequest {
    username: string;
    password: string;
}

export interface RegisterRequest {
    username: string;
    email: string;
    password: string;
}

export interface AuthResponse {
    token: string;
    user: User;
    expiresAt: string;
}

@Injectable({
    providedIn: 'root'
})
export class AuthService {
    private readonly TOKEN_KEY = 'auth_token';
    private currentUserSubject = new BehaviorSubject<User | null>(null);

    public currentUser$ = this.currentUserSubject.asObservable();
    public isAuthenticated$ = this.currentUser$.pipe(
        map(user => !!user)
    );

    constructor(
        private http: HttpClient,
        private router: Router
    ) {
        this.loadUserFromToken();
    }

    login(credentials: LoginRequest): Observable<AuthResponse> {
        return this.http.post<AuthResponse>('/api/auth/login', credentials).pipe(
            tap(response => this.handleAuthSuccess(response))
        );
    }

    register(userData: RegisterRequest): Observable<AuthResponse> {
        return this.http.post<AuthResponse>('/api/auth/register', userData).pipe(
            tap(response => this.handleAuthSuccess(response))
        );
    }

    logout(): void {
        localStorage.removeItem(this.TOKEN_KEY);
        this.currentUserSubject.next(null);
        this.router.navigate(['/login']);
    }

    getToken(): string | null {
        return localStorage.getItem(this.TOKEN_KEY);
    }

    private handleAuthSuccess(response: AuthResponse): void {
        localStorage.setItem(this.TOKEN_KEY, response.token);
        this.currentUserSubject.next(response.user);
    }

    private loadUserFromToken(): void {
        const token = this.getToken();
        if (token) {
            // Decode JWT to get user info (in production, validate on server)
            try {
                const payload = JSON.parse(atob(token.split('.')[1]));
                if (payload.exp * 1000 > Date.now()) {
                    // Token is still valid
                    this.currentUserSubject.next({
                        id: payload.sub,
                        username: payload.username,
                        email: payload.email,
                        role: payload.role
                    });
                } else {
                    // Token expired
                    this.logout();
                }
            } catch (error) {
                console.error('Invalid token:', error);
                this.logout();
            }
        }
    }
}
```

### HTTP Interceptor for Authentication

```typescript
// interceptors/auth.interceptor.ts
import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpErrorResponse } from '@angular/common/http';
import { catchError, throwError } from 'rxjs';
import { AuthService } from '../services/auth.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
    constructor(private authService: AuthService) {}

    intercept(req: HttpRequest<any>, next: HttpHandler) {
        // Add auth token to requests
        const token = this.authService.getToken();
        if (token) {
            req = req.clone({
                setHeaders: {
                    Authorization: `Bearer ${token}`
                }
            });
        }

        return next.handle(req).pipe(
            catchError((error: HttpErrorResponse) => {
                if (error.status === 401) {
                    // Token expired or invalid
                    this.authService.logout();
                }
                return throwError(() => error);
            })
        );
    }
}
```

### Route Guards

```typescript
// guards/auth.guard.ts
import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { Observable, map } from 'rxjs';
import { AuthService } from '../services/auth.service';

@Injectable({
    providedIn: 'root'
})
export class AuthGuard implements CanActivate {
    constructor(
        private authService: AuthService,
        private router: Router
    ) {}

    canActivate(): Observable<boolean> {
        return this.authService.isAuthenticated$.pipe(
            map(isAuthenticated => {
                if (!isAuthenticated) {
                    this.router.navigate(['/login']);
                    return false;
                }
                return true;
            })
        );
    }
}

// guards/guest.guard.ts - Redirect authenticated users away from login/register
@Injectable({
    providedIn: 'root'
})
export class GuestGuard implements CanActivate {
    constructor(
        private authService: AuthService,
        private router: Router
    ) {}

    canActivate(): Observable<boolean> {
        return this.authService.isAuthenticated$.pipe(
            map(isAuthenticated => {
                if (isAuthenticated) {
                    this.router.navigate(['/dashboard']);
                    return false;
                }
                return true;
            })
        );
    }
}
```

### Login Component

```typescript
// components/login/login.component.ts
import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';

@Component({
    selector: 'app-login',
    template: `
        <div class="login-container">
            <mat-card class="login-card">
                <mat-card-header>
                    <mat-card-title>Login to Rusty Git Server</mat-card-title>
                </mat-card-header>

                <mat-card-content>
                    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
                        <mat-form-field appearance="outline" class="full-width">
                            <mat-label>Username</mat-label>
                            <input matInput formControlName="username" required>
                            <mat-error *ngIf="loginForm.get('username')?.hasError('required')">
                                Username is required
                            </mat-error>
                        </mat-form-field>

                        <mat-form-field appearance="outline" class="full-width">
                            <mat-label>Password</mat-label>
                            <input matInput type="password" formControlName="password" required>
                            <mat-error *ngIf="loginForm.get('password')?.hasError('required')">
                                Password is required
                            </mat-error>
                        </mat-form-field>

                        <div *ngIf="errorMessage" class="error-message">
                            {{ errorMessage }}
                        </div>

                        <div class="actions">
                            <button mat-raised-button
                                    color="primary"
                                    type="submit"
                                    [disabled]="loginForm.invalid || loading">
                                <mat-spinner *ngIf="loading" diameter="20"></mat-spinner>
                                {{ loading ? 'Logging in...' : 'Login' }}
                            </button>

                            <a mat-button routerLink="/register">
                                Don't have an account? Register
                            </a>
                        </div>
                    </form>
                </mat-card-content>
            </mat-card>
        </div>
    `,
    styleUrls: ['./login.component.scss']
})
export class LoginComponent {
    loginForm: FormGroup;
    loading = false;
    errorMessage = '';

    constructor(
        private fb: FormBuilder,
        private authService: AuthService,
        private router: Router
    ) {
        this.loginForm = this.fb.group({
            username: ['', [Validators.required, Validators.minLength(3)]],
            password: ['', [Validators.required, Validators.minLength(6)]]
        });
    }

    onSubmit(): void {
        if (this.loginForm.valid) {
            this.loading = true;
            this.errorMessage = '';

            this.authService.login(this.loginForm.value).subscribe({
                next: () => {
                    this.router.navigate(['/dashboard']);
                },
                error: (error) => {
                    this.loading = false;
                    this.errorMessage = error.error?.message || 'Login failed';
                }
            });
        }
    }
}
```

### App Routing Configuration

```typescript
// app-routing.module.ts
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from './guards/auth.guard';
import { GuestGuard } from './guards/guest.guard';

const routes: Routes = [
    { path: '', redirectTo: '/dashboard', pathMatch: 'full' },

    // Public routes (guest only)
    {
        path: 'login',
        loadChildren: () => import('./pages/auth/auth.module').then(m => m.AuthModule),
        canActivate: [GuestGuard]
    },

    // Protected routes (authenticated users only)
    {
        path: 'dashboard',
        loadChildren: () => import('./pages/dashboard/dashboard.module').then(m => m.DashboardModule),
        canActivate: [AuthGuard]
    },
    {
        path: 'repositories',
        loadChildren: () => import('./pages/repositories/repositories.module').then(m => m.RepositoriesModule),
        canActivate: [AuthGuard]
    },

    // Wildcard route
    { path: '**', redirectTo: '/dashboard' }
];

@NgModule({
    imports: [RouterModule.forRoot(routes)],
    exports: [RouterModule]
})
export class AppRoutingModule { }
```

Ready to continue with [Module 5: Advanced Git Operations](./module-05-advanced-git.md)?
