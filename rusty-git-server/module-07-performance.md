# Module 7: Performance and Scalability

## 🎯 Learning Objectives

By the end of this module, you will:
- Implement multi-level caching strategies with Redis
- Optimize database queries and schema design
- Build comprehensive monitoring and observability
- Create horizontal scaling patterns
- Master performance profiling and benchmarking
- Understand load balancing and service discovery

## 📊 Performance Fundamentals

### The Performance Pyramid

```mermaid
graph TB
    subgraph "Performance Optimization Layers"
        L1[Algorithm Optimization<br/>O(n²) → O(n log n)]
        L2[Data Structure Choice<br/>HashMap vs BTreeMap]
        L3[Memory Management<br/>Zero-copy, Pool allocation]
        L4[Caching Strategy<br/>Multi-level caching]
        L5[Database Optimization<br/>Indexes, Query optimization]
        L6[Network Optimization<br/>Connection pooling, Compression]
        L7[Infrastructure Scaling<br/>Load balancing, CDN]
    end
    
    L1 --> L2
    L2 --> L3
    L3 --> L4
    L4 --> L5
    L5 --> L6
    L6 --> L7
    
    style L1 fill:#e8f5e8
    style L7 fill:#fff3e0
```

**Why This Order Matters**: Optimizing algorithms gives 10-100x improvements, while infrastructure scaling gives 2-5x improvements.

## 🚀 Advanced Caching with Redis

### Multi-Level Cache Architecture

```rust
// Cargo.toml additions
[dependencies]
redis = { version = "0.24", features = ["tokio-comp", "connection-manager"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }

// src/cache/redis_cache.rs
use redis::{AsyncCommands, Client, RedisResult};
use serde::{Deserialize, Serialize};
use std::time::Duration;
use tokio::time::timeout;

pub struct RedisCache {
    client: Client,
    default_ttl: Duration,
}

impl RedisCache {
    pub async fn new(redis_url: &str) -> RedisResult<Self> {
        let client = Client::open(redis_url)?;
        
        // Test connection
        let mut conn = client.get_async_connection().await?;
        let _: String = conn.ping().await?;
        
        Ok(Self {
            client,
            default_ttl: Duration::from_secs(3600), // 1 hour default
        })
    }
    
    /// Get value with automatic deserialization
    pub async fn get<T>(&self, key: &str) -> RedisResult<Option<T>>
    where
        T: for<'de> Deserialize<'de>,
    {
        let mut conn = self.client.get_async_connection().await?;
        
        // Use timeout to prevent hanging
        let result = timeout(Duration::from_secs(5), async {
            let json_str: Option<String> = conn.get(key).await?;
            match json_str {
                Some(json) => {
                    match serde_json::from_str(&json) {
                        Ok(value) => Ok(Some(value)),
                        Err(_) => Ok(None), // Invalid JSON, treat as cache miss
                    }
                }
                None => Ok(None),
            }
        }).await;
        
        match result {
            Ok(inner_result) => inner_result,
            Err(_) => Ok(None), // Timeout, treat as cache miss
        }
    }
    
    /// Set value with automatic serialization
    pub async fn set<T>(&self, key: &str, value: &T, ttl: Option<Duration>) -> RedisResult<()>
    where
        T: Serialize,
    {
        let json_str = serde_json::to_string(value)
            .map_err(|e| redis::RedisError::from((redis::ErrorKind::TypeError, "Serialization failed", e.to_string())))?;
        
        let mut conn = self.client.get_async_connection().await?;
        let ttl_seconds = ttl.unwrap_or(self.default_ttl).as_secs();
        
        conn.set_ex(key, json_str, ttl_seconds).await
    }
    
    /// Set multiple values in a pipeline for better performance
    pub async fn mset<T>(&self, items: Vec<(String, T)>, ttl: Option<Duration>) -> RedisResult<()>
    where
        T: Serialize,
    {
        let mut conn = self.client.get_async_connection().await?;
        let mut pipe = redis::pipe();
        
        for (key, value) in items {
            let json_str = serde_json::to_string(&value)
                .map_err(|e| redis::RedisError::from((redis::ErrorKind::TypeError, "Serialization failed", e.to_string())))?;
            
            let ttl_seconds = ttl.unwrap_or(self.default_ttl).as_secs();
            pipe.set_ex(&key, json_str, ttl_seconds);
        }
        
        pipe.query_async(&mut conn).await
    }
    
    /// Delete keys matching a pattern
    pub async fn delete_pattern(&self, pattern: &str) -> RedisResult<u64> {
        let mut conn = self.client.get_async_connection().await?;
        
        // Get all keys matching pattern
        let keys: Vec<String> = conn.keys(pattern).await?;
        
        if keys.is_empty() {
            return Ok(0);
        }
        
        // Delete in batches to avoid blocking Redis
        let batch_size = 100;
        let mut deleted_count = 0;
        
        for chunk in keys.chunks(batch_size) {
            let count: u64 = conn.del(chunk).await?;
            deleted_count += count;
        }
        
        Ok(deleted_count)
    }
    
    /// Increment counter with expiration
    pub async fn increment_counter(&self, key: &str, ttl: Duration) -> RedisResult<i64> {
        let mut conn = self.client.get_async_connection().await?;
        
        // Use Lua script for atomic increment with TTL
        let script = redis::Script::new(r#"
            local current = redis.call('INCR', KEYS[1])
            if current == 1 then
                redis.call('EXPIRE', KEYS[1], ARGV[1])
            end
            return current
        "#);
        
        script.key(key).arg(ttl.as_secs()).invoke_async(&mut conn).await
    }
    
    /// Get cache statistics
    pub async fn get_stats(&self) -> RedisResult<CacheStats> {
        let mut conn = self.client.get_async_connection().await?;
        
        let info: String = conn.info("memory").await?;
        let keyspace: String = conn.info("keyspace").await?;
        
        // Parse Redis INFO output (simplified)
        let memory_usage = Self::parse_info_value(&info, "used_memory:");
        let total_keys = Self::parse_keyspace_keys(&keyspace);
        
        Ok(CacheStats {
            memory_usage_bytes: memory_usage.unwrap_or(0),
            total_keys,
            hit_rate: 0.0, // Would need to track this separately
        })
    }
    
    fn parse_info_value(info: &str, key: &str) -> Option<u64> {
        info.lines()
            .find(|line| line.starts_with(key))
            .and_then(|line| line.split(':').nth(1))
            .and_then(|value| value.parse().ok())
    }
    
    fn parse_keyspace_keys(keyspace: &str) -> u64 {
        keyspace.lines()
            .filter(|line| line.starts_with("db"))
            .filter_map(|line| {
                line.split(',')
                    .find(|part| part.starts_with("keys="))
                    .and_then(|part| part.strip_prefix("keys="))
                    .and_then(|num| num.parse::<u64>().ok())
            })
            .sum()
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CacheStats {
    pub memory_usage_bytes: u64,
    pub total_keys: u64,
    pub hit_rate: f64,
}

// Cache key generators for consistent naming
pub struct CacheKeys;

impl CacheKeys {
    pub fn repository(repo_id: &str) -> String {
        format!("repo:{}", repo_id)
    }
    
    pub fn commit_history(repo_id: &str, branch: &str) -> String {
        format!("history:{}:{}", repo_id, branch)
    }
    
    pub fn file_diff(old_hash: &str, new_hash: &str) -> String {
        format!("diff:{}:{}", old_hash, new_hash)
    }
    
    pub fn user_permissions(user_id: &str, repo_id: &str) -> String {
        format!("perms:{}:{}", user_id, repo_id)
    }
    
    pub fn rate_limit(user_id: &str, endpoint: &str) -> String {
        format!("rate:{}:{}", user_id, endpoint)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde::{Deserialize, Serialize};
    
    #[derive(Debug, Serialize, Deserialize, PartialEq)]
    struct TestData {
        id: u32,
        name: String,
    }
    
    #[tokio::test]
    async fn test_redis_cache_operations() {
        // This test requires a running Redis instance
        // Skip if Redis is not available
        let cache = match RedisCache::new("redis://127.0.0.1:6379").await {
            Ok(cache) => cache,
            Err(_) => {
                println!("Redis not available, skipping test");
                return;
            }
        };
        
        let test_data = TestData {
            id: 123,
            name: "Test Repository".to_string(),
        };
        
        let key = "test:repo:123";
        
        // Test set and get
        cache.set(key, &test_data, Some(Duration::from_secs(60))).await.unwrap();
        let retrieved: Option<TestData> = cache.get(key).await.unwrap();
        
        assert_eq!(retrieved, Some(test_data));
        
        // Clean up
        let mut conn = cache.client.get_async_connection().await.unwrap();
        let _: () = conn.del(key).await.unwrap();
    }
}
```

### Intelligent Cache Warming

```rust
// src/cache/cache_warmer.rs
use crate::cache::RedisCache;
use crate::git::{Repository, CommitObject};
use std::sync::Arc;
use tokio::time::{interval, Duration};
use tracing::{info, warn, error};

pub struct CacheWarmer {
    redis_cache: Arc<RedisCache>,
    repository_service: Arc<dyn RepositoryService>,
}

impl CacheWarmer {
    pub fn new(
        redis_cache: Arc<RedisCache>,
        repository_service: Arc<dyn RepositoryService>,
    ) -> Self {
        Self {
            redis_cache,
            repository_service,
        }
    }
    
    /// Start background cache warming
    pub async fn start_background_warming(&self) {
        let mut interval = interval(Duration::from_secs(300)); // Every 5 minutes
        
        loop {
            interval.tick().await;
            
            if let Err(e) = self.warm_popular_repositories().await {
                error!("Cache warming failed: {}", e);
            }
        }
    }
    
    /// Warm cache for popular repositories
    async fn warm_popular_repositories(&self) -> Result<(), Box<dyn std::error::Error>> {
        info!("Starting cache warming for popular repositories");
        
        // Get most accessed repositories from the last hour
        let popular_repos = self.repository_service.get_popular_repositories(50).await?;
        
        for repo in popular_repos {
            // Warm repository metadata
            self.warm_repository_cache(&repo).await?;
            
            // Warm commit history for main branches
            self.warm_commit_history(&repo).await?;
            
            // Warm file tree cache
            self.warm_file_tree(&repo).await?;
        }
        
        info!("Cache warming completed for {} repositories", popular_repos.len());
        Ok(())
    }
    
    async fn warm_repository_cache(&self, repo: &Repository) -> Result<(), Box<dyn std::error::Error>> {
        let cache_key = CacheKeys::repository(&repo.id.to_string());
        
        // Only warm if not already cached
        let cached: Option<Repository> = self.redis_cache.get(&cache_key).await?;
        if cached.is_none() {
            self.redis_cache.set(&cache_key, repo, Some(Duration::from_secs(1800))).await?;
        }
        
        Ok(())
    }
    
    async fn warm_commit_history(&self, repo: &Repository) -> Result<(), Box<dyn std::error::Error>> {
        let main_branches = vec!["main", "master", "develop"];
        
        for branch in main_branches {
            let cache_key = CacheKeys::commit_history(&repo.id.to_string(), branch);
            
            // Check if already cached
            let cached: Option<Vec<CommitObject>> = self.redis_cache.get(&cache_key).await?;
            if cached.is_none() {
                // Fetch and cache commit history
                if let Ok(history) = self.repository_service.get_commit_history(&repo.id.to_string(), branch, Some(100)).await {
                    self.redis_cache.set(&cache_key, &history, Some(Duration::from_secs(900))).await?;
                }
            }
        }
        
        Ok(())
    }
    
    async fn warm_file_tree(&self, repo: &Repository) -> Result<(), Box<dyn std::error::Error>> {
        // Implementation would fetch and cache file tree structure
        // This is a simplified version
        info!("Warming file tree cache for repository: {}", repo.name);
        Ok(())
    }
}

// Trait for repository service (would be implemented by your actual service)
#[async_trait::async_trait]
pub trait RepositoryService: Send + Sync {
    async fn get_popular_repositories(&self, limit: usize) -> Result<Vec<Repository>, Box<dyn std::error::Error>>;
    async fn get_commit_history(&self, repo_id: &str, branch: &str, limit: Option<usize>) -> Result<Vec<CommitObject>, Box<dyn std::error::Error>>;
}
```

## 🗄️ Database Optimization

### Optimized Schema Design

```sql
-- Optimized PostgreSQL schema with performance considerations

-- Users table with proper indexing
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role user_role NOT NULL DEFAULT 'user',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Additional fields for performance
    last_login_at TIMESTAMP WITH TIME ZONE,
    login_count INTEGER DEFAULT 0
);

-- Composite indexes for common query patterns
CREATE INDEX idx_users_active_username ON users(username) WHERE is_active = true;
CREATE INDEX idx_users_email_active ON users(email) WHERE is_active = true;
CREATE INDEX idx_users_last_login ON users(last_login_at DESC) WHERE is_active = true;

-- Repositories table with denormalized data for performance
CREATE TABLE repositories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    owner_id UUID NOT NULL REFERENCES users(id),
    is_private BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Denormalized fields for performance
    owner_username VARCHAR(50) NOT NULL, -- Avoid JOIN for common queries
    commit_count INTEGER DEFAULT 0,
    branch_count INTEGER DEFAULT 0,
    contributor_count INTEGER DEFAULT 1,
    last_commit_at TIMESTAMP WITH TIME ZONE,
    size_bytes BIGINT DEFAULT 0,
    
    -- Full-text search
    search_vector tsvector,
    
    UNIQUE(owner_id, name)
);

-- Indexes for repositories
CREATE INDEX idx_repositories_owner ON repositories(owner_id);
CREATE INDEX idx_repositories_public ON repositories(created_at DESC) WHERE is_private = false;
CREATE INDEX idx_repositories_updated ON repositories(updated_at DESC);
CREATE INDEX idx_repositories_popular ON repositories(commit_count DESC, contributor_count DESC);

-- Full-text search index
CREATE INDEX idx_repositories_search ON repositories USING gin(search_vector);

-- Function to update search vector
CREATE OR REPLACE FUNCTION update_repository_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := to_tsvector('english', 
        COALESCE(NEW.name, '') || ' ' || 
        COALESCE(NEW.description, '') || ' ' ||
        COALESCE(NEW.owner_username, '')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update search vector
CREATE TRIGGER trigger_update_repository_search_vector
    BEFORE INSERT OR UPDATE ON repositories
    FOR EACH ROW EXECUTE FUNCTION update_repository_search_vector();

-- Commits table (partitioned by date for large repositories)
CREATE TABLE commits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    repository_id UUID NOT NULL REFERENCES repositories(id),
    hash VARCHAR(40) UNIQUE NOT NULL,
    message TEXT NOT NULL,
    author_name VARCHAR(255) NOT NULL,
    author_email VARCHAR(255) NOT NULL,
    committer_name VARCHAR(255) NOT NULL,
    committer_email VARCHAR(255) NOT NULL,
    authored_at TIMESTAMP WITH TIME ZONE NOT NULL,
    committed_at TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Performance fields
    parent_hashes TEXT[], -- Array of parent commit hashes
    file_count INTEGER DEFAULT 0,
    additions INTEGER DEFAULT 0,
    deletions INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
) PARTITION BY RANGE (committed_at);

-- Create partitions for commits (example: monthly partitions)
CREATE TABLE commits_2024_01 PARTITION OF commits
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
CREATE TABLE commits_2024_02 PARTITION OF commits
    FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');
-- ... continue for other months

-- Indexes on partitioned table
CREATE INDEX idx_commits_repository ON commits(repository_id, committed_at DESC);
CREATE INDEX idx_commits_hash ON commits(hash);
CREATE INDEX idx_commits_author ON commits(author_email, committed_at DESC);

-- Repository statistics materialized view for dashboard
CREATE MATERIALIZED VIEW repository_stats AS
SELECT 
    r.id,
    r.name,
    r.owner_username,
    r.is_private,
    r.created_at,
    COUNT(DISTINCT c.id) as total_commits,
    COUNT(DISTINCT c.author_email) as unique_contributors,
    MAX(c.committed_at) as last_commit_at,
    SUM(c.additions) as total_additions,
    SUM(c.deletions) as total_deletions,
    AVG(c.file_count) as avg_files_per_commit
FROM repositories r
LEFT JOIN commits c ON r.id = c.repository_id
WHERE r.is_active = true
GROUP BY r.id, r.name, r.owner_username, r.is_private, r.created_at;

-- Index on materialized view
CREATE INDEX idx_repository_stats_popular ON repository_stats(total_commits DESC, unique_contributors DESC);

-- Refresh materialized view periodically
CREATE OR REPLACE FUNCTION refresh_repository_stats()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY repository_stats;
END;
$$ LANGUAGE plpgsql;

-- Performance monitoring queries
-- 1. Find slow queries
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;

-- 2. Find unused indexes
SELECT schemaname, tablename, indexname, idx_scan
FROM pg_stat_user_indexes
WHERE idx_scan = 0
ORDER BY schemaname, tablename;

-- 3. Table sizes
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

## 📊 Comprehensive Monitoring and Observability

### Metrics Collection with Prometheus

```rust
// Cargo.toml additions
[dependencies]
prometheus = "0.13"
axum-prometheus = "0.4"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
sysinfo = "0.30"

// src/monitoring/metrics.rs
use prometheus::{
    Counter, Histogram, Gauge, IntCounter, IntGauge,
    register_counter, register_histogram, register_gauge,
    register_int_counter, register_int_gauge,
    Encoder, TextEncoder,
};
use std::sync::OnceLock;
use sysinfo::{System, SystemExt, CpuExt, DiskExt};

pub struct Metrics {
    // HTTP metrics
    pub http_requests_total: IntCounter,
    pub http_request_duration: Histogram,
    pub http_requests_in_flight: IntGauge,

    // Git operations
    pub git_operations_total: Counter,
    pub git_operation_duration: Histogram,
    pub repository_size_bytes: Gauge,

    // Database metrics
    pub db_connections_active: IntGauge,
    pub db_query_duration: Histogram,
    pub db_queries_total: IntCounter,

    // Cache metrics
    pub cache_hits_total: IntCounter,
    pub cache_misses_total: IntCounter,
    pub cache_size_bytes: Gauge,

    // WebSocket metrics
    pub websocket_connections: IntGauge,
    pub websocket_messages_sent: IntCounter,
    pub websocket_messages_received: IntCounter,

    // System metrics
    pub memory_usage_bytes: Gauge,
    pub cpu_usage_percent: Gauge,
    pub disk_usage_bytes: Gauge,
}

static METRICS: OnceLock<Metrics> = OnceLock::new();

impl Metrics {
    pub fn global() -> &'static Metrics {
        METRICS.get_or_init(|| {
            Metrics {
                http_requests_total: register_int_counter!(
                    "http_requests_total",
                    "Total number of HTTP requests"
                ).unwrap(),

                http_request_duration: register_histogram!(
                    "http_request_duration_seconds",
                    "HTTP request duration in seconds",
                    vec![0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0, 10.0]
                ).unwrap(),

                http_requests_in_flight: register_int_gauge!(
                    "http_requests_in_flight",
                    "Number of HTTP requests currently being processed"
                ).unwrap(),

                git_operations_total: register_counter!(
                    "git_operations_total",
                    "Total number of Git operations",
                    &["operation", "repository"]
                ).unwrap(),

                git_operation_duration: register_histogram!(
                    "git_operation_duration_seconds",
                    "Git operation duration in seconds",
                    vec![0.01, 0.05, 0.1, 0.5, 1.0, 5.0, 10.0, 30.0, 60.0]
                ).unwrap(),

                repository_size_bytes: register_gauge!(
                    "repository_size_bytes",
                    "Repository size in bytes",
                    &["repository"]
                ).unwrap(),

                db_connections_active: register_int_gauge!(
                    "db_connections_active",
                    "Number of active database connections"
                ).unwrap(),

                db_query_duration: register_histogram!(
                    "db_query_duration_seconds",
                    "Database query duration in seconds",
                    vec![0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0]
                ).unwrap(),

                db_queries_total: register_int_counter!(
                    "db_queries_total",
                    "Total number of database queries"
                ).unwrap(),

                cache_hits_total: register_int_counter!(
                    "cache_hits_total",
                    "Total number of cache hits"
                ).unwrap(),

                cache_misses_total: register_int_counter!(
                    "cache_misses_total",
                    "Total number of cache misses"
                ).unwrap(),

                cache_size_bytes: register_gauge!(
                    "cache_size_bytes",
                    "Cache size in bytes"
                ).unwrap(),

                websocket_connections: register_int_gauge!(
                    "websocket_connections",
                    "Number of active WebSocket connections"
                ).unwrap(),

                websocket_messages_sent: register_int_counter!(
                    "websocket_messages_sent_total",
                    "Total number of WebSocket messages sent"
                ).unwrap(),

                websocket_messages_received: register_int_counter!(
                    "websocket_messages_received_total",
                    "Total number of WebSocket messages received"
                ).unwrap(),

                memory_usage_bytes: register_gauge!(
                    "memory_usage_bytes",
                    "Memory usage in bytes"
                ).unwrap(),

                cpu_usage_percent: register_gauge!(
                    "cpu_usage_percent",
                    "CPU usage percentage"
                ).unwrap(),

                disk_usage_bytes: register_gauge!(
                    "disk_usage_bytes",
                    "Disk usage in bytes",
                    &["mount_point"]
                ).unwrap(),
            }
        })
    }

    /// Export metrics in Prometheus format
    pub fn export() -> String {
        let encoder = TextEncoder::new();
        let metric_families = prometheus::gather();
        encoder.encode_to_string(&metric_families).unwrap()
    }

    /// Update system metrics
    pub fn update_system_metrics() {
        let mut system = System::new_all();
        system.refresh_all();

        // Memory usage
        let memory_used = system.used_memory() * 1024; // Convert to bytes
        Self::global().memory_usage_bytes.set(memory_used as f64);

        // CPU usage (average across all cores)
        let cpu_usage: f64 = system.cpus().iter()
            .map(|cpu| cpu.cpu_usage() as f64)
            .sum::<f64>() / system.cpus().len() as f64;
        Self::global().cpu_usage_percent.set(cpu_usage);

        // Disk usage
        for disk in system.disks() {
            let mount_point = disk.mount_point().to_string_lossy();
            let used_bytes = disk.total_space() - disk.available_space();
            Self::global().disk_usage_bytes
                .with_label_values(&[&mount_point])
                .set(used_bytes as f64);
        }
    }
}

// Middleware for HTTP metrics
use axum::{extract::Request, middleware::Next, response::Response};
use std::time::Instant;

pub async fn metrics_middleware(request: Request, next: Next) -> Response {
    let start = Instant::now();
    let method = request.method().clone();
    let path = request.uri().path().to_string();

    // Increment in-flight requests
    Metrics::global().http_requests_in_flight.inc();

    let response = next.run(request).await;

    // Record metrics
    let duration = start.elapsed().as_secs_f64();
    let status = response.status().as_u16();

    Metrics::global().http_requests_total.inc();
    Metrics::global().http_request_duration.observe(duration);
    Metrics::global().http_requests_in_flight.dec();

    response
}
```

### Structured Logging with Tracing

```rust
// src/monitoring/logging.rs
use tracing::{info, warn, error, debug, span, Level, Instrument};
use tracing_subscriber::{
    layer::SubscriberExt,
    util::SubscriberInitExt,
    EnvFilter,
    fmt,
    Registry,
};
use serde_json::json;

pub fn init_logging() {
    let env_filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| EnvFilter::new("info"));

    Registry::default()
        .with(env_filter)
        .with(
            fmt::layer()
                .json()
                .with_current_span(true)
                .with_span_list(true)
                .with_target(true)
                .with_thread_ids(true)
                .with_thread_names(true)
        )
        .init();
}

// Structured logging macros for common operations
pub struct GitOperationSpan;

impl GitOperationSpan {
    pub async fn execute<F, T>(
        operation: &str,
        repository_id: &str,
        future: F,
    ) -> T
    where
        F: std::future::Future<Output = T>,
    {
        let span = span!(
            Level::INFO,
            "git_operation",
            operation = operation,
            repository_id = repository_id,
            duration_ms = tracing::field::Empty,
            success = tracing::field::Empty,
        );

        let start = std::time::Instant::now();
        let result = future.instrument(span.clone()).await;
        let duration = start.elapsed();

        span.record("duration_ms", duration.as_millis());
        span.record("success", true);

        // Also record in metrics
        Metrics::global()
            .git_operation_duration
            .observe(duration.as_secs_f64());

        result
    }
}

// Usage example
pub async fn clone_repository(repo_id: &str) -> Result<(), GitError> {
    GitOperationSpan::execute("clone", repo_id, async {
        info!("Starting repository clone");

        // Actual clone logic here
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

        info!("Repository clone completed successfully");
        Ok(())
    }).await
}
```

## 🏗️ Horizontal Scaling Architecture

### Load Balancing with Nginx

```nginx
# /etc/nginx/sites-available/rusty-git-server
upstream backend {
    # Least connections load balancing
    least_conn;

    # Backend servers
    server *********:3000 max_fails=3 fail_timeout=30s;
    server *********:3000 max_fails=3 fail_timeout=30s;
    server *********:3000 max_fails=3 fail_timeout=30s;

    # Health check (requires nginx-plus or custom module)
    # health_check interval=10s fails=3 passes=2;
}

# WebSocket upstream (sticky sessions for WebSocket connections)
upstream websocket {
    ip_hash; # Ensure same client goes to same server

    server *********:3000;
    server *********:3000;
    server *********:3000;
}

server {
    listen 80;
    listen [::]:80;
    server_name git.example.com;

    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name git.example.com;

    # SSL configuration
    ssl_certificate /etc/letsencrypt/live/git.example.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/git.example.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=git:10m rate=5r/s;

    # WebSocket upgrade
    location /ws {
        proxy_pass http://websocket;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocket timeout settings
        proxy_read_timeout 86400s;
        proxy_send_timeout 86400s;
    }

    # Git operations (higher timeout for large repositories)
    location ~ ^/(.*\.git|git-upload-pack|git-receive-pack) {
        limit_req zone=git burst=5 nodelay;

        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Large repository support
        client_max_body_size 1G;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
        proxy_connect_timeout 60s;
    }

    # API endpoints
    location /api {
        limit_req zone=api burst=20 nodelay;

        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # API timeout settings
        proxy_read_timeout 60s;
        proxy_send_timeout 60s;
        proxy_connect_timeout 10s;
    }

    # Static files (served directly by nginx)
    location /static {
        alias /var/www/rusty-git-server/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Angular frontend
    location / {
        try_files $uri $uri/ /index.html;
        root /var/www/rusty-git-server/frontend;

        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # Health check endpoint
    location /health {
        proxy_pass http://backend;
        access_log off;
    }

    # Metrics endpoint (restrict access)
    location /metrics {
        allow 10.0.0.0/8;
        allow **********/12;
        allow ***********/16;
        deny all;

        proxy_pass http://backend;
    }
}
```

### Service Discovery with Consul

```rust
// src/discovery/consul.rs
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::time::{interval, Duration};

#[derive(Debug, Serialize, Deserialize)]
pub struct ServiceRegistration {
    pub id: String,
    pub name: String,
    pub address: String,
    pub port: u16,
    pub tags: Vec<String>,
    pub check: HealthCheck,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct HealthCheck {
    pub http: String,
    pub interval: String,
    pub timeout: String,
    pub deregister_critical_service_after: String,
}

pub struct ConsulClient {
    base_url: String,
    client: reqwest::Client,
}

impl ConsulClient {
    pub fn new(consul_url: &str) -> Self {
        Self {
            base_url: consul_url.to_string(),
            client: reqwest::Client::new(),
        }
    }

    /// Register service with Consul
    pub async fn register_service(&self, registration: ServiceRegistration) -> Result<(), Box<dyn std::error::Error>> {
        let url = format!("{}/v1/agent/service/register", self.base_url);

        let response = self.client
            .put(&url)
            .json(&registration)
            .send()
            .await?;

        if response.status().is_success() {
            tracing::info!("Service registered successfully: {}", registration.name);
            Ok(())
        } else {
            let error = response.text().await?;
            Err(format!("Failed to register service: {}", error).into())
        }
    }

    /// Discover healthy services
    pub async fn discover_services(&self, service_name: &str) -> Result<Vec<ServiceInstance>, Box<dyn std::error::Error>> {
        let url = format!("{}/v1/health/service/{}?passing=true", self.base_url, service_name);

        let response = self.client.get(&url).send().await?;
        let services: Vec<ConsulServiceResponse> = response.json().await?;

        let instances = services
            .into_iter()
            .map(|s| ServiceInstance {
                id: s.service.id,
                address: s.service.address,
                port: s.service.port,
                tags: s.service.tags,
            })
            .collect();

        Ok(instances)
    }

    /// Start periodic health check updates
    pub async fn start_health_updates(&self, service_id: String) {
        let mut interval = interval(Duration::from_secs(30));

        loop {
            interval.tick().await;

            // Update TTL check
            let url = format!("{}/v1/agent/check/pass/service:{}", self.base_url, service_id);
            if let Err(e) = self.client.put(&url).send().await {
                tracing::error!("Failed to update health check: {}", e);
            }
        }
    }
}

#[derive(Debug, Deserialize)]
struct ConsulServiceResponse {
    #[serde(rename = "Service")]
    service: ConsulService,
}

#[derive(Debug, Deserialize)]
struct ConsulService {
    #[serde(rename = "ID")]
    id: String,
    #[serde(rename = "Address")]
    address: String,
    #[serde(rename = "Port")]
    port: u16,
    #[serde(rename = "Tags")]
    tags: Vec<String>,
}

#[derive(Debug, Clone)]
pub struct ServiceInstance {
    pub id: String,
    pub address: String,
    pub port: u16,
    pub tags: Vec<String>,
}

// Usage in main application
pub async fn register_with_consul() -> Result<(), Box<dyn std::error::Error>> {
    let consul = ConsulClient::new("http://consul:8500");

    let registration = ServiceRegistration {
        id: format!("rusty-git-server-{}", uuid::Uuid::new_v4()),
        name: "rusty-git-server".to_string(),
        address: "*********".to_string(),
        port: 3000,
        tags: vec!["git".to_string(), "api".to_string()],
        check: HealthCheck {
            http: "http://*********:3000/health".to_string(),
            interval: "10s".to_string(),
            timeout: "3s".to_string(),
            deregister_critical_service_after: "30s".to_string(),
        },
    };

    consul.register_service(registration).await?;

    // Start health check updates in background
    let service_id = "rusty-git-server".to_string();
    tokio::spawn(async move {
        consul.start_health_updates(service_id).await;
    });

    Ok(())
}
```

## 🎯 Key Performance Insights

### Performance Optimization Checklist

1. **Algorithm Level** (10-100x improvement):
   - Use efficient data structures (HashMap vs Vec for lookups)
   - Implement proper algorithms (Myers' diff vs naive comparison)
   - Avoid unnecessary computations

2. **Memory Management** (2-10x improvement):
   - Use zero-copy operations where possible
   - Implement object pooling for frequently allocated objects
   - Profile memory usage and fix leaks

3. **I/O Optimization** (2-5x improvement):
   - Use async I/O for all network and disk operations
   - Implement connection pooling
   - Batch database operations

4. **Caching Strategy** (5-50x improvement):
   - Multi-level caching (L1: in-memory, L2: Redis, L3: CDN)
   - Cache invalidation strategies
   - Precompute expensive operations

5. **Database Optimization** (2-20x improvement):
   - Proper indexing strategy
   - Query optimization
   - Connection pooling
   - Read replicas for scaling reads

Ready to continue with [Module 8: CI/CD Pipeline for Server Deployment](./module-08-cicd.md)?
