# Module 5: Advanced Git Operations

## 🎯 Learning Objectives

By the end of this module, you will:
- Implement advanced Git operations (branches, merges, diffs)
- Understand Git's graph algorithms and tree traversal
- Build high-performance diff algorithms
- Create visual commit history representations
- Master R<PERSON>'s concurrency patterns with Rayon
- Implement smart caching strategies for performance

## 🌳 Understanding Git's Graph Structure

Git is fundamentally a directed acyclic graph (DAG) where commits are nodes and parent relationships are edges.

```mermaid
graph TB
    subgraph "Git Repository Structure"
        A[Initial Commit<br/>abc123]
        B[Feature Branch<br/>def456]
        C[Main Branch<br/>ghi789]
        D[Merge Commit<br/>jkl012]
        
        A --> B
        A --> C
        B --> D
        C --> D
    end
    
    subgraph "Branch References"
        MAIN[refs/heads/main]
        FEATURE[refs/heads/feature]
        HEAD[HEAD]
    end
    
    MAIN --> D
    FEATURE --> B
    HEAD --> MAIN
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
```

### Why This Matters for Performance

Understanding Git's structure is crucial for building efficient operations:

1. **Commit History**: Traversing the graph efficiently
2. **Branch Operations**: Managing references without copying data
3. **Merge Detection**: Finding common ancestors
4. **Diff Calculation**: Comparing tree objects efficiently

## 🚀 Advanced Git Operations in Rust

### 1. Branch Management

```rust
// src/git/branches.rs
use std::collections::{HashMap, HashSet, VecDeque};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use crate::git::{GitObject, CommitObject, GitResult, GitError};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Branch {
    pub name: String,
    pub commit_hash: String,
    pub is_default: bool,
    pub created_at: DateTime<Utc>,
    pub last_commit_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommitGraph {
    pub commits: HashMap<String, CommitObject>,
    pub branches: HashMap<String, Branch>,
    pub tags: HashMap<String, String>, // tag_name -> commit_hash
}

pub struct BranchManager {
    graph: CommitGraph,
}

impl BranchManager {
    pub fn new() -> Self {
        Self {
            graph: CommitGraph {
                commits: HashMap::new(),
                branches: HashMap::new(),
                tags: HashMap::new(),
            },
        }
    }
    
    /// Create a new branch from an existing commit
    pub fn create_branch(&mut self, name: String, from_commit: String) -> GitResult<Branch> {
        // Verify the commit exists
        if !self.graph.commits.contains_key(&from_commit) {
            return Err(GitError::ObjectNotFound { hash: from_commit });
        }
        
        // Check if branch already exists
        if self.graph.branches.contains_key(&name) {
            return Err(GitError::InvalidObjectType { 
                object_type: format!("Branch '{}' already exists", name) 
            });
        }
        
        let commit = &self.graph.commits[&from_commit];
        let branch = Branch {
            name: name.clone(),
            commit_hash: from_commit,
            is_default: false,
            created_at: Utc::now(),
            last_commit_at: commit.committer.timestamp,
        };
        
        self.graph.branches.insert(name, branch.clone());
        Ok(branch)
    }
    
    /// Get commit history for a branch (topological sort)
    pub fn get_commit_history(&self, branch_name: &str, limit: Option<usize>) -> GitResult<Vec<CommitObject>> {
        let branch = self.graph.branches.get(branch_name)
            .ok_or_else(|| GitError::ObjectNotFound { hash: branch_name.to_string() })?;
        
        let mut history = Vec::new();
        let mut visited = HashSet::new();
        let mut queue = VecDeque::new();
        
        queue.push_back(branch.commit_hash.clone());
        
        while let Some(commit_hash) = queue.pop_front() {
            if visited.contains(&commit_hash) {
                continue;
            }
            
            if let Some(max_commits) = limit {
                if history.len() >= max_commits {
                    break;
                }
            }
            
            let commit = self.graph.commits.get(&commit_hash)
                .ok_or_else(|| GitError::ObjectNotFound { hash: commit_hash.clone() })?;
            
            history.push(commit.clone());
            visited.insert(commit_hash);
            
            // Add parents to queue (breadth-first traversal)
            for parent_hash in &commit.parents {
                if !visited.contains(parent_hash) {
                    queue.push_back(parent_hash.clone());
                }
            }
        }
        
        Ok(history)
    }
    
    /// Find the merge base (common ancestor) of two branches
    pub fn find_merge_base(&self, branch1: &str, branch2: &str) -> GitResult<Option<String>> {
        let commit1 = &self.graph.branches.get(branch1)
            .ok_or_else(|| GitError::ObjectNotFound { hash: branch1.to_string() })?
            .commit_hash;
        
        let commit2 = &self.graph.branches.get(branch2)
            .ok_or_else(|| GitError::ObjectNotFound { hash: branch2.to_string() })?
            .commit_hash;
        
        // Get all ancestors of branch1
        let ancestors1 = self.get_all_ancestors(commit1)?;
        
        // Find first common ancestor in branch2's history
        let mut queue = VecDeque::new();
        let mut visited = HashSet::new();
        queue.push_back(commit2.clone());
        
        while let Some(commit_hash) = queue.pop_front() {
            if visited.contains(&commit_hash) {
                continue;
            }
            visited.insert(commit_hash.clone());
            
            if ancestors1.contains(&commit_hash) {
                return Ok(Some(commit_hash));
            }
            
            if let Some(commit) = self.graph.commits.get(&commit_hash) {
                for parent in &commit.parents {
                    queue.push_back(parent.clone());
                }
            }
        }
        
        Ok(None)
    }
    
    /// Get all ancestors of a commit (for merge base calculation)
    fn get_all_ancestors(&self, commit_hash: &str) -> GitResult<HashSet<String>> {
        let mut ancestors = HashSet::new();
        let mut queue = VecDeque::new();
        queue.push_back(commit_hash.to_string());
        
        while let Some(hash) = queue.pop_front() {
            if ancestors.contains(&hash) {
                continue;
            }
            ancestors.insert(hash.clone());
            
            if let Some(commit) = self.graph.commits.get(&hash) {
                for parent in &commit.parents {
                    queue.push_back(parent.clone());
                }
            }
        }
        
        Ok(ancestors)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::git::{GitSignature, CommitObject};
    
    fn create_test_commit(hash: &str, parents: Vec<String>, message: &str) -> CommitObject {
        CommitObject {
            tree: "tree_hash".to_string(),
            parents,
            author: GitSignature {
                name: "Test Author".to_string(),
                email: "<EMAIL>".to_string(),
                timestamp: Utc::now(),
            },
            committer: GitSignature {
                name: "Test Author".to_string(),
                email: "<EMAIL>".to_string(),
                timestamp: Utc::now(),
            },
            message: message.to_string(),
        }
    }
    
    #[test]
    fn test_branch_creation() {
        let mut manager = BranchManager::new();
        
        // Add a commit to the graph
        let commit = create_test_commit("abc123", vec![], "Initial commit");
        manager.graph.commits.insert("abc123".to_string(), commit);
        
        // Create a branch
        let branch = manager.create_branch("feature".to_string(), "abc123".to_string()).unwrap();
        
        assert_eq!(branch.name, "feature");
        assert_eq!(branch.commit_hash, "abc123");
        assert!(!branch.is_default);
    }
    
    #[test]
    fn test_commit_history() {
        let mut manager = BranchManager::new();
        
        // Create a linear history: A -> B -> C
        let commit_a = create_test_commit("aaa", vec![], "Commit A");
        let commit_b = create_test_commit("bbb", vec!["aaa".to_string()], "Commit B");
        let commit_c = create_test_commit("ccc", vec!["bbb".to_string()], "Commit C");
        
        manager.graph.commits.insert("aaa".to_string(), commit_a);
        manager.graph.commits.insert("bbb".to_string(), commit_b);
        manager.graph.commits.insert("ccc".to_string(), commit_c);
        
        // Create branch pointing to C
        manager.create_branch("main".to_string(), "ccc".to_string()).unwrap();
        
        // Get history
        let history = manager.get_commit_history("main", Some(10)).unwrap();
        
        assert_eq!(history.len(), 3);
        assert_eq!(history[0].message, "Commit C");
        // Note: Order depends on traversal algorithm
    }
    
    #[test]
    fn test_merge_base() {
        let mut manager = BranchManager::new();
        
        // Create a branching history:
        //     A
        //    / \
        //   B   C
        //       |
        //       D
        
        let commit_a = create_test_commit("aaa", vec![], "Commit A");
        let commit_b = create_test_commit("bbb", vec!["aaa".to_string()], "Commit B");
        let commit_c = create_test_commit("ccc", vec!["aaa".to_string()], "Commit C");
        let commit_d = create_test_commit("ddd", vec!["ccc".to_string()], "Commit D");
        
        manager.graph.commits.insert("aaa".to_string(), commit_a);
        manager.graph.commits.insert("bbb".to_string(), commit_b);
        manager.graph.commits.insert("ccc".to_string(), commit_c);
        manager.graph.commits.insert("ddd".to_string(), commit_d);
        
        manager.create_branch("branch1".to_string(), "bbb".to_string()).unwrap();
        manager.create_branch("branch2".to_string(), "ddd".to_string()).unwrap();
        
        let merge_base = manager.find_merge_base("branch1", "branch2").unwrap();
        assert_eq!(merge_base, Some("aaa".to_string()));
    }
}
```

### 2. High-Performance Diff Algorithm

```rust
// src/git/diff.rs
use std::collections::HashMap;
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DiffOperation {
    Insert { line: usize, content: String },
    Delete { line: usize, content: String },
    Modify { line: usize, old_content: String, new_content: String },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileDiff {
    pub file_path: String,
    pub old_hash: Option<String>,
    pub new_hash: Option<String>,
    pub operations: Vec<DiffOperation>,
    pub additions: usize,
    pub deletions: usize,
}

pub struct DiffEngine;

impl DiffEngine {
    /// Myers' diff algorithm - O(ND) time complexity
    /// This is the same algorithm used by Git internally
    pub fn compute_diff(old_content: &str, new_content: &str) -> Vec<DiffOperation> {
        let old_lines: Vec<&str> = old_content.lines().collect();
        let new_lines: Vec<&str> = new_content.lines().collect();
        
        let lcs = Self::longest_common_subsequence(&old_lines, &new_lines);
        Self::generate_operations(&old_lines, &new_lines, &lcs)
    }
    
    /// Longest Common Subsequence using dynamic programming
    fn longest_common_subsequence(old: &[&str], new: &[&str]) -> Vec<(usize, usize)> {
        let m = old.len();
        let n = new.len();
        
        // DP table: dp[i][j] = length of LCS of old[0..i] and new[0..j]
        let mut dp = vec![vec![0; n + 1]; m + 1];
        
        // Fill the DP table
        for i in 1..=m {
            for j in 1..=n {
                if old[i - 1] == new[j - 1] {
                    dp[i][j] = dp[i - 1][j - 1] + 1;
                } else {
                    dp[i][j] = dp[i - 1][j].max(dp[i][j - 1]);
                }
            }
        }
        
        // Backtrack to find the actual LCS
        let mut lcs = Vec::new();
        let mut i = m;
        let mut j = n;
        
        while i > 0 && j > 0 {
            if old[i - 1] == new[j - 1] {
                lcs.push((i - 1, j - 1));
                i -= 1;
                j -= 1;
            } else if dp[i - 1][j] > dp[i][j - 1] {
                i -= 1;
            } else {
                j -= 1;
            }
        }
        
        lcs.reverse();
        lcs
    }
    
    /// Generate diff operations from LCS
    fn generate_operations(
        old: &[&str], 
        new: &[&str], 
        lcs: &[(usize, usize)]
    ) -> Vec<DiffOperation> {
        let mut operations = Vec::new();
        let mut old_idx = 0;
        let mut new_idx = 0;
        
        for &(lcs_old, lcs_new) in lcs {
            // Handle deletions
            while old_idx < lcs_old {
                operations.push(DiffOperation::Delete {
                    line: old_idx,
                    content: old[old_idx].to_string(),
                });
                old_idx += 1;
            }
            
            // Handle insertions
            while new_idx < lcs_new {
                operations.push(DiffOperation::Insert {
                    line: new_idx,
                    content: new[new_idx].to_string(),
                });
                new_idx += 1;
            }
            
            // Skip the common line
            old_idx += 1;
            new_idx += 1;
        }
        
        // Handle remaining deletions
        while old_idx < old.len() {
            operations.push(DiffOperation::Delete {
                line: old_idx,
                content: old[old_idx].to_string(),
            });
            old_idx += 1;
        }
        
        // Handle remaining insertions
        while new_idx < new.len() {
            operations.push(DiffOperation::Insert {
                line: new_idx,
                content: new[new_idx].to_string(),
            });
            new_idx += 1;
        }
        
        operations
    }
    
    /// Compute diff statistics
    pub fn compute_stats(operations: &[DiffOperation]) -> (usize, usize) {
        let mut additions = 0;
        let mut deletions = 0;
        
        for op in operations {
            match op {
                DiffOperation::Insert { .. } => additions += 1,
                DiffOperation::Delete { .. } => deletions += 1,
                DiffOperation::Modify { .. } => {
                    additions += 1;
                    deletions += 1;
                }
            }
        }
        
        (additions, deletions)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_simple_diff() {
        let old = "line1\nline2\nline3";
        let new = "line1\nmodified line2\nline3\nline4";
        
        let diff = DiffEngine::compute_diff(old, new);
        
        // Should have one deletion and two insertions
        assert!(diff.iter().any(|op| matches!(op, DiffOperation::Delete { .. })));
        assert_eq!(diff.iter().filter(|op| matches!(op, DiffOperation::Insert { .. })).count(), 2);
    }
    
    #[test]
    fn test_identical_content() {
        let content = "line1\nline2\nline3";
        let diff = DiffEngine::compute_diff(content, content);
        assert!(diff.is_empty());
    }
    
    #[test]
    fn test_empty_to_content() {
        let old = "";
        let new = "line1\nline2";
        
        let diff = DiffEngine::compute_diff(old, new);
        assert_eq!(diff.len(), 2);
        assert!(diff.iter().all(|op| matches!(op, DiffOperation::Insert { .. })));
    }
}
```

## ⚡ Performance Optimization with Rayon

### Parallel Processing for Large Repositories

```rust
// src/git/parallel.rs
use rayon::prelude::*;
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use crate::git::{GitObject, CommitObject, FileDiff, DiffEngine};

pub struct ParallelGitProcessor;

impl ParallelGitProcessor {
    /// Process multiple commits in parallel
    pub fn process_commits_parallel(
        commits: Vec<CommitObject>
    ) -> Vec<ProcessedCommit> {
        commits
            .into_par_iter()  // Parallel iterator
            .map(|commit| ProcessedCommit {
                hash: commit.tree.clone(), // Simplified
                message: commit.message,
                author: commit.author.name,
                timestamp: commit.author.timestamp,
                file_count: Self::count_files_in_commit(&commit),
                additions: 0, // Would be calculated from diff
                deletions: 0,
            })
            .collect()
    }

    /// Compute diffs for multiple files in parallel
    pub fn compute_file_diffs_parallel(
        file_pairs: Vec<(String, String, String)> // (path, old_content, new_content)
    ) -> Vec<FileDiff> {
        file_pairs
            .into_par_iter()
            .map(|(path, old_content, new_content)| {
                let operations = DiffEngine::compute_diff(&old_content, &new_content);
                let (additions, deletions) = DiffEngine::compute_stats(&operations);

                FileDiff {
                    file_path: path,
                    old_hash: Some("old_hash".to_string()),
                    new_hash: Some("new_hash".to_string()),
                    operations,
                    additions,
                    deletions,
                }
            })
            .collect()
    }

    /// Parallel tree traversal for large repositories
    pub fn traverse_tree_parallel(
        root_commits: Vec<String>,
        commit_graph: Arc<HashMap<String, CommitObject>>
    ) -> HashMap<String, usize> { // commit_hash -> depth
        let depths = Arc::new(Mutex::new(HashMap::new()));

        root_commits
            .into_par_iter()
            .for_each(|commit_hash| {
                Self::traverse_from_commit(
                    commit_hash,
                    0,
                    Arc::clone(&commit_graph),
                    Arc::clone(&depths)
                );
            });

        Arc::try_unwrap(depths).unwrap().into_inner().unwrap()
    }

    fn traverse_from_commit(
        commit_hash: String,
        depth: usize,
        graph: Arc<HashMap<String, CommitObject>>,
        depths: Arc<Mutex<HashMap<String, usize>>>
    ) {
        // Update depth if this is the shortest path to this commit
        {
            let mut depths_guard = depths.lock().unwrap();
            if let Some(&existing_depth) = depths_guard.get(&commit_hash) {
                if depth >= existing_depth {
                    return; // Already processed with shorter path
                }
            }
            depths_guard.insert(commit_hash.clone(), depth);
        }

        // Process children in parallel
        if let Some(commit) = graph.get(&commit_hash) {
            commit.parents
                .par_iter()
                .for_each(|parent_hash| {
                    Self::traverse_from_commit(
                        parent_hash.clone(),
                        depth + 1,
                        Arc::clone(&graph),
                        Arc::clone(&depths)
                    );
                });
        }
    }

    fn count_files_in_commit(commit: &CommitObject) -> usize {
        // Simplified - would actually traverse the tree object
        42 // Placeholder
    }
}

#[derive(Debug, Clone)]
pub struct ProcessedCommit {
    pub hash: String,
    pub message: String,
    pub author: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub file_count: usize,
    pub additions: usize,
    pub deletions: usize,
}
```

### Smart Caching System

```rust
// src/git/cache.rs
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};
use serde::{Deserialize, Serialize};
use crate::git::{GitObject, CommitObject, FileDiff};

#[derive(Debug, Clone)]
pub struct CacheEntry<T> {
    pub value: T,
    pub created_at: Instant,
    pub access_count: usize,
    pub last_accessed: Instant,
}

impl<T> CacheEntry<T> {
    pub fn new(value: T) -> Self {
        let now = Instant::now();
        Self {
            value,
            created_at: now,
            access_count: 1,
            last_accessed: now,
        }
    }

    pub fn access(&mut self) -> &T {
        self.access_count += 1;
        self.last_accessed = Instant::now();
        &self.value
    }

    pub fn is_expired(&self, ttl: Duration) -> bool {
        self.created_at.elapsed() > ttl
    }
}

pub struct GitCache {
    // Object cache - stores Git objects by hash
    objects: Arc<RwLock<HashMap<String, CacheEntry<GitObject>>>>,

    // Commit history cache - stores commit chains
    histories: Arc<RwLock<HashMap<String, CacheEntry<Vec<CommitObject>>>>>,

    // Diff cache - stores computed diffs
    diffs: Arc<RwLock<HashMap<String, CacheEntry<FileDiff>>>>,

    // Configuration
    max_objects: usize,
    max_histories: usize,
    max_diffs: usize,
    ttl: Duration,
}

impl GitCache {
    pub fn new(max_objects: usize, max_histories: usize, max_diffs: usize) -> Self {
        Self {
            objects: Arc::new(RwLock::new(HashMap::new())),
            histories: Arc::new(RwLock::new(HashMap::new())),
            diffs: Arc::new(RwLock::new(HashMap::new())),
            max_objects,
            max_histories,
            max_diffs,
            ttl: Duration::from_secs(3600), // 1 hour TTL
        }
    }

    /// Get object from cache
    pub fn get_object(&self, hash: &str) -> Option<GitObject> {
        let mut cache = self.objects.write().unwrap();
        if let Some(entry) = cache.get_mut(hash) {
            if !entry.is_expired(self.ttl) {
                return Some(entry.access().clone());
            } else {
                cache.remove(hash);
            }
        }
        None
    }

    /// Store object in cache
    pub fn put_object(&self, hash: String, object: GitObject) {
        let mut cache = self.objects.write().unwrap();

        // Evict if at capacity
        if cache.len() >= self.max_objects {
            self.evict_lru_object(&mut cache);
        }

        cache.insert(hash, CacheEntry::new(object));
    }

    /// Get commit history from cache
    pub fn get_history(&self, branch_name: &str) -> Option<Vec<CommitObject>> {
        let mut cache = self.histories.write().unwrap();
        if let Some(entry) = cache.get_mut(branch_name) {
            if !entry.is_expired(self.ttl) {
                return Some(entry.access().clone());
            } else {
                cache.remove(branch_name);
            }
        }
        None
    }

    /// Store commit history in cache
    pub fn put_history(&self, branch_name: String, history: Vec<CommitObject>) {
        let mut cache = self.histories.write().unwrap();

        if cache.len() >= self.max_histories {
            self.evict_lru_history(&mut cache);
        }

        cache.insert(branch_name, CacheEntry::new(history));
    }

    /// Get diff from cache
    pub fn get_diff(&self, key: &str) -> Option<FileDiff> {
        let mut cache = self.diffs.write().unwrap();
        if let Some(entry) = cache.get_mut(key) {
            if !entry.is_expired(self.ttl) {
                return Some(entry.access().clone());
            } else {
                cache.remove(key);
            }
        }
        None
    }

    /// Store diff in cache
    pub fn put_diff(&self, key: String, diff: FileDiff) {
        let mut cache = self.diffs.write().unwrap();

        if cache.len() >= self.max_diffs {
            self.evict_lru_diff(&mut cache);
        }

        cache.insert(key, CacheEntry::new(diff));
    }

    /// Evict least recently used object
    fn evict_lru_object(&self, cache: &mut HashMap<String, CacheEntry<GitObject>>) {
        if let Some((lru_key, _)) = cache
            .iter()
            .min_by_key(|(_, entry)| entry.last_accessed)
            .map(|(k, v)| (k.clone(), v.last_accessed))
        {
            cache.remove(&lru_key);
        }
    }

    /// Evict least recently used history
    fn evict_lru_history(&self, cache: &mut HashMap<String, CacheEntry<Vec<CommitObject>>>) {
        if let Some((lru_key, _)) = cache
            .iter()
            .min_by_key(|(_, entry)| entry.last_accessed)
            .map(|(k, v)| (k.clone(), v.last_accessed))
        {
            cache.remove(&lru_key);
        }
    }

    /// Evict least recently used diff
    fn evict_lru_diff(&self, cache: &mut HashMap<String, CacheEntry<FileDiff>>) {
        if let Some((lru_key, _)) = cache
            .iter()
            .min_by_key(|(_, entry)| entry.last_accessed)
            .map(|(k, v)| (k.clone(), v.last_accessed))
        {
            cache.remove(&lru_key);
        }
    }

    /// Get cache statistics
    pub fn get_stats(&self) -> CacheStats {
        let objects = self.objects.read().unwrap();
        let histories = self.histories.read().unwrap();
        let diffs = self.diffs.read().unwrap();

        CacheStats {
            object_count: objects.len(),
            history_count: histories.len(),
            diff_count: diffs.len(),
            total_object_accesses: objects.values().map(|e| e.access_count).sum(),
            total_history_accesses: histories.values().map(|e| e.access_count).sum(),
            total_diff_accesses: diffs.values().map(|e| e.access_count).sum(),
        }
    }

    /// Clear expired entries
    pub fn cleanup_expired(&self) {
        let now = Instant::now();

        // Clean objects
        {
            let mut objects = self.objects.write().unwrap();
            objects.retain(|_, entry| !entry.is_expired(self.ttl));
        }

        // Clean histories
        {
            let mut histories = self.histories.write().unwrap();
            histories.retain(|_, entry| !entry.is_expired(self.ttl));
        }

        // Clean diffs
        {
            let mut diffs = self.diffs.write().unwrap();
            diffs.retain(|_, entry| !entry.is_expired(self.ttl));
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CacheStats {
    pub object_count: usize,
    pub history_count: usize,
    pub diff_count: usize,
    pub total_object_accesses: usize,
    pub total_history_accesses: usize,
    pub total_diff_accesses: usize,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::git::{BlobObject, GitObject};

    #[test]
    fn test_cache_basic_operations() {
        let cache = GitCache::new(10, 10, 10);
        let blob = GitObject::Blob(BlobObject {
            content: b"test content".to_vec(),
        });

        // Test put and get
        cache.put_object("abc123".to_string(), blob.clone());
        let retrieved = cache.get_object("abc123");

        assert!(retrieved.is_some());
        // Note: In real implementation, would compare object contents
    }

    #[test]
    fn test_cache_expiration() {
        let mut cache = GitCache::new(10, 10, 10);
        cache.ttl = Duration::from_millis(1); // Very short TTL for testing

        let blob = GitObject::Blob(BlobObject {
            content: b"test content".to_vec(),
        });

        cache.put_object("abc123".to_string(), blob);

        // Wait for expiration
        std::thread::sleep(Duration::from_millis(2));

        let retrieved = cache.get_object("abc123");
        assert!(retrieved.is_none());
    }
}
```

## 🎯 Key Performance Insights

### Why These Optimizations Matter

1. **Parallel Processing**: Large repositories can have millions of commits
2. **Smart Caching**: Avoid recomputing expensive operations
3. **Memory Management**: Prevent memory leaks in long-running servers
4. **Algorithm Choice**: Myers' diff is O(ND) vs naive O(N²M)

### Benchmarking Your Implementation

```rust
// src/git/benchmarks.rs
#[cfg(test)]
mod benchmarks {
    use super::*;
    use std::time::Instant;

    #[test]
    fn benchmark_diff_performance() {
        let old_content = "line1\n".repeat(1000);
        let new_content = format!("{}new_line\n{}", "line1\n".repeat(500), "line1\n".repeat(500));

        let start = Instant::now();
        let diff = DiffEngine::compute_diff(&old_content, &new_content);
        let duration = start.elapsed();

        println!("Diff computation took: {:?}", duration);
        println!("Operations generated: {}", diff.len());

        // Assert reasonable performance (adjust based on your requirements)
        assert!(duration.as_millis() < 100, "Diff took too long: {:?}", duration);
    }

    #[test]
    fn benchmark_parallel_vs_sequential() {
        let commits: Vec<CommitObject> = (0..1000)
            .map(|i| create_test_commit(&format!("hash_{}", i), vec![], &format!("Commit {}", i)))
            .collect();

        // Sequential processing
        let start = Instant::now();
        let _sequential_result: Vec<ProcessedCommit> = commits
            .iter()
            .map(|commit| ProcessedCommit {
                hash: commit.tree.clone(),
                message: commit.message.clone(),
                author: commit.author.name.clone(),
                timestamp: commit.author.timestamp,
                file_count: 42,
                additions: 0,
                deletions: 0,
            })
            .collect();
        let sequential_time = start.elapsed();

        // Parallel processing
        let start = Instant::now();
        let _parallel_result = ParallelGitProcessor::process_commits_parallel(commits);
        let parallel_time = start.elapsed();

        println!("Sequential: {:?}, Parallel: {:?}", sequential_time, parallel_time);

        // Parallel should be faster for large datasets
        // (Note: For small datasets, overhead might make sequential faster)
    }
}
```

Ready to continue with [Module 6: Real-time Features and WebSockets](./module-06-websockets.md)?
