# Module 6: Real-time Features and WebSockets

## 🎯 Learning Objectives

By the end of this module, you will:
- Implement WebSocket communication in Rust with Tokio
- Build real-time notifications and live updates
- Understand the Actor model for concurrent message handling
- Create collaborative features like live code review
- Master state synchronization across multiple clients
- Build scalable real-time architecture patterns

## 🔄 Why Real-time Features Matter

Modern Git platforms like GitHub and GitLab provide real-time updates for:
- **Live notifications**: New commits, pull requests, issues
- **Collaborative editing**: Multiple users viewing the same code
- **Build status updates**: CI/CD pipeline progress
- **Live comments**: Real-time code review discussions

### WebSocket vs HTTP Polling

```mermaid
graph TB
    subgraph "HTTP Polling (Traditional)"
        CLIENT1[Client]
        SERVER1[Server]
        CLIENT1 -->|Request every 5s| SERVER1
        SERVER1 -->|Response| CLIENT1
        NOTE1[❌ Inefficient<br/>❌ High latency<br/>❌ Server load]
    end
    
    subgraph "WebSocket (Real-time)"
        CLIENT2[Client]
        SERVER2[Server]
        CLIENT2 <-->|Persistent Connection| SERVER2
        SERVER2 -->|Push Updates| CLIENT2
        NOTE2[✅ Efficient<br/>✅ Low latency<br/>✅ Bidirectional]
    end
    
    style NOTE1 fill:#ffebee
    style NOTE2 fill:#e8f5e8
```

## 🛠️ WebSocket Implementation in Rust

### 1. WebSocket Server with Axum

```rust
// Cargo.toml additions
[dependencies]
axum = { version = "0.7", features = ["ws"] }
tokio-tungstenite = "0.20"
futures-util = "0.3"
dashmap = "5.5" // Thread-safe HashMap
uuid = { version = "1.0", features = ["v4"] }

// src/websocket/mod.rs
use axum::{
    extract::{
        ws::{Message, WebSocket, WebSocketUpgrade},
        State, Path,
    },
    response::Response,
};
use dashmap::DashMap;
use futures_util::{sink::SinkExt, stream::StreamExt};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::broadcast;
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum WebSocketMessage {
    // Repository events
    RepositoryCreated { repository_id: String, name: String },
    CommitPushed { repository_id: String, commit_hash: String, message: String },
    BranchCreated { repository_id: String, branch_name: String },
    
    // Collaboration events
    UserJoinedRepository { repository_id: String, username: String },
    UserLeftRepository { repository_id: String, username: String },
    LiveComment { repository_id: String, file_path: String, line: u32, comment: String, author: String },
    
    // System events
    Heartbeat,
    Error { message: String },
}

#[derive(Debug, Clone)]
pub struct ConnectedClient {
    pub id: Uuid,
    pub user_id: String,
    pub username: String,
    pub subscribed_repositories: Vec<String>,
    pub sender: broadcast::Sender<WebSocketMessage>,
}

pub struct WebSocketManager {
    // Active connections: client_id -> client info
    clients: Arc<DashMap<Uuid, ConnectedClient>>,
    
    // Repository subscriptions: repo_id -> set of client_ids
    repository_subscriptions: Arc<DashMap<String, Vec<Uuid>>>,
    
    // Global broadcast channel for system-wide events
    global_sender: broadcast::Sender<WebSocketMessage>,
}

impl WebSocketManager {
    pub fn new() -> Self {
        let (global_sender, _) = broadcast::channel(1000);
        
        Self {
            clients: Arc::new(DashMap::new()),
            repository_subscriptions: Arc::new(DashMap::new()),
            global_sender,
        }
    }
    
    /// Handle new WebSocket connection
    pub async fn handle_socket(
        &self,
        socket: WebSocket,
        user_id: String,
        username: String,
    ) {
        let client_id = Uuid::new_v4();
        let (client_sender, client_receiver) = broadcast::channel(100);
        
        // Create client record
        let client = ConnectedClient {
            id: client_id,
            user_id: user_id.clone(),
            username: username.clone(),
            subscribed_repositories: Vec::new(),
            sender: client_sender.clone(),
        };
        
        self.clients.insert(client_id, client);
        
        // Split socket into sender and receiver
        let (mut ws_sender, mut ws_receiver) = socket.split();
        
        // Clone necessary data for tasks
        let clients = Arc::clone(&self.clients);
        let repo_subs = Arc::clone(&self.repository_subscriptions);
        let mut global_receiver = self.global_sender.subscribe();
        let mut client_msg_receiver = client_receiver;
        
        // Task 1: Handle incoming messages from client
        let clients_clone = Arc::clone(&clients);
        let repo_subs_clone = Arc::clone(&repo_subs);
        let incoming_task = tokio::spawn(async move {
            while let Some(msg) = ws_receiver.next().await {
                match msg {
                    Ok(Message::Text(text)) => {
                        if let Ok(parsed) = serde_json::from_str::<ClientMessage>(&text) {
                            Self::handle_client_message(
                                client_id,
                                parsed,
                                &clients_clone,
                                &repo_subs_clone,
                            ).await;
                        }
                    }
                    Ok(Message::Close(_)) => break,
                    Err(e) => {
                        eprintln!("WebSocket error: {}", e);
                        break;
                    }
                    _ => {}
                }
            }
        });
        
        // Task 2: Send messages to client
        let outgoing_task = tokio::spawn(async move {
            loop {
                tokio::select! {
                    // Global messages
                    global_msg = global_receiver.recv() => {
                        if let Ok(msg) = global_msg {
                            let json = serde_json::to_string(&msg).unwrap();
                            if ws_sender.send(Message::Text(json)).await.is_err() {
                                break;
                            }
                        }
                    }
                    
                    // Client-specific messages
                    client_msg = client_msg_receiver.recv() => {
                        if let Ok(msg) = client_msg {
                            let json = serde_json::to_string(&msg).unwrap();
                            if ws_sender.send(Message::Text(json)).await.is_err() {
                                break;
                            }
                        }
                    }
                }
            }
        });
        
        // Wait for either task to complete (connection closed)
        tokio::select! {
            _ = incoming_task => {},
            _ = outgoing_task => {},
        }
        
        // Cleanup: remove client from all subscriptions
        self.cleanup_client(client_id).await;
    }
    
    async fn handle_client_message(
        client_id: Uuid,
        message: ClientMessage,
        clients: &DashMap<Uuid, ConnectedClient>,
        repo_subs: &DashMap<String, Vec<Uuid>>,
    ) {
        match message {
            ClientMessage::SubscribeToRepository { repository_id } => {
                // Add client to repository subscription
                repo_subs
                    .entry(repository_id.clone())
                    .or_insert_with(Vec::new)
                    .push(client_id);
                
                // Update client's subscription list
                if let Some(mut client) = clients.get_mut(&client_id) {
                    client.subscribed_repositories.push(repository_id);
                }
            }
            
            ClientMessage::UnsubscribeFromRepository { repository_id } => {
                // Remove client from repository subscription
                if let Some(mut subscribers) = repo_subs.get_mut(&repository_id) {
                    subscribers.retain(|&id| id != client_id);
                }
                
                // Update client's subscription list
                if let Some(mut client) = clients.get_mut(&client_id) {
                    client.subscribed_repositories.retain(|repo| repo != &repository_id);
                }
            }
            
            ClientMessage::SendLiveComment { repository_id, file_path, line, comment } => {
                if let Some(client) = clients.get(&client_id) {
                    let live_comment = WebSocketMessage::LiveComment {
                        repository_id: repository_id.clone(),
                        file_path,
                        line,
                        comment,
                        author: client.username.clone(),
                    };
                    
                    // Send to all subscribers of this repository
                    Self::broadcast_to_repository(&repository_id, live_comment, clients, repo_subs).await;
                }
            }
        }
    }
    
    async fn broadcast_to_repository(
        repository_id: &str,
        message: WebSocketMessage,
        clients: &DashMap<Uuid, ConnectedClient>,
        repo_subs: &DashMap<String, Vec<Uuid>>,
    ) {
        if let Some(subscribers) = repo_subs.get(repository_id) {
            for &client_id in subscribers.iter() {
                if let Some(client) = clients.get(&client_id) {
                    let _ = client.sender.send(message.clone());
                }
            }
        }
    }
    
    async fn cleanup_client(&self, client_id: Uuid) {
        // Remove from all repository subscriptions
        for mut entry in self.repository_subscriptions.iter_mut() {
            entry.value_mut().retain(|&id| id != client_id);
        }
        
        // Remove client record
        self.clients.remove(&client_id);
    }
    
    /// Broadcast message to all connected clients
    pub fn broadcast_global(&self, message: WebSocketMessage) {
        let _ = self.global_sender.send(message);
    }
    
    /// Send message to specific repository subscribers
    pub async fn notify_repository(&self, repository_id: &str, message: WebSocketMessage) {
        Self::broadcast_to_repository(
            repository_id,
            message,
            &self.clients,
            &self.repository_subscriptions,
        ).await;
    }
    
    /// Get connection statistics
    pub fn get_stats(&self) -> ConnectionStats {
        ConnectionStats {
            total_connections: self.clients.len(),
            repository_subscriptions: self.repository_subscriptions.len(),
            active_repositories: self.repository_subscriptions
                .iter()
                .filter(|entry| !entry.value().is_empty())
                .count(),
        }
    }
}

#[derive(Debug, Deserialize)]
pub enum ClientMessage {
    SubscribeToRepository { repository_id: String },
    UnsubscribeFromRepository { repository_id: String },
    SendLiveComment { repository_id: String, file_path: String, line: u32, comment: String },
}

#[derive(Debug, Serialize)]
pub struct ConnectionStats {
    pub total_connections: usize,
    pub repository_subscriptions: usize,
    pub active_repositories: usize,
}

// WebSocket upgrade handler for Axum
pub async fn websocket_handler(
    ws: WebSocketUpgrade,
    State(ws_manager): State<Arc<WebSocketManager>>,
    // In real implementation, extract user from JWT token
    Path(user_id): Path<String>,
) -> Response {
    ws.on_upgrade(move |socket| {
        ws_manager.handle_socket(socket, user_id.clone(), format!("user_{}", user_id))
    })
}
```

### 2. Integration with Git Events

```rust
// src/websocket/git_events.rs
use super::{WebSocketManager, WebSocketMessage};
use crate::git::{CommitObject, Repository};
use std::sync::Arc;

pub struct GitEventNotifier {
    ws_manager: Arc<WebSocketManager>,
}

impl GitEventNotifier {
    pub fn new(ws_manager: Arc<WebSocketManager>) -> Self {
        Self { ws_manager }
    }
    
    /// Notify when a new commit is pushed
    pub async fn on_commit_pushed(
        &self,
        repository_id: &str,
        commit: &CommitObject,
    ) {
        let message = WebSocketMessage::CommitPushed {
            repository_id: repository_id.to_string(),
            commit_hash: "commit_hash".to_string(), // Would be actual hash
            message: commit.message.clone(),
        };
        
        self.ws_manager.notify_repository(repository_id, message).await;
    }
    
    /// Notify when a new branch is created
    pub async fn on_branch_created(
        &self,
        repository_id: &str,
        branch_name: &str,
    ) {
        let message = WebSocketMessage::BranchCreated {
            repository_id: repository_id.to_string(),
            branch_name: branch_name.to_string(),
        };
        
        self.ws_manager.notify_repository(repository_id, message).await;
    }
    
    /// Notify when a new repository is created
    pub fn on_repository_created(&self, repository: &Repository) {
        let message = WebSocketMessage::RepositoryCreated {
            repository_id: repository.id.to_string(),
            name: repository.name.clone(),
        };
        
        self.ws_manager.broadcast_global(message);
    }
}

// Integration example in your Git service
impl RepositoryService {
    pub async fn create_commit_with_notification(
        &self,
        repository_id: &str,
        commit: CommitObject,
        notifier: &GitEventNotifier,
    ) -> Result<(), GitError> {
        // 1. Store the commit
        self.store_commit(&commit).await?;
        
        // 2. Update branch reference
        self.update_branch_ref(repository_id, &commit).await?;
        
        // 3. Send real-time notification
        notifier.on_commit_pushed(repository_id, &commit).await;
        
        Ok(())
    }
}
```

### 3. Angular WebSocket Client

```typescript
// services/websocket.service.ts
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { filter, map } from 'rxjs/operators';

export interface WebSocketMessage {
  type: 'RepositoryCreated' | 'CommitPushed' | 'BranchCreated' | 'LiveComment' | 'Heartbeat' | 'Error';
  data: any;
}

export interface LiveComment {
  repository_id: string;
  file_path: string;
  line: number;
  comment: string;
  author: string;
}

@Injectable({
  providedIn: 'root'
})
export class WebSocketService {
  private socket: WebSocket | null = null;
  private messageSubject = new Subject<WebSocketMessage>();
  private connectionStatus = new BehaviorSubject<'connecting' | 'connected' | 'disconnected'>('disconnected');
  
  public messages$ = this.messageSubject.asObservable();
  public connectionStatus$ = this.connectionStatus.asObservable();
  
  constructor() {}
  
  connect(userId: string): void {
    if (this.socket?.readyState === WebSocket.OPEN) {
      return; // Already connected
    }
    
    this.connectionStatus.next('connecting');
    
    const wsUrl = `ws://localhost:3000/ws/${userId}`;
    this.socket = new WebSocket(wsUrl);
    
    this.socket.onopen = () => {
      console.log('WebSocket connected');
      this.connectionStatus.next('connected');
      this.startHeartbeat();
    };
    
    this.socket.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);
        this.messageSubject.next(message);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };
    
    this.socket.onclose = () => {
      console.log('WebSocket disconnected');
      this.connectionStatus.next('disconnected');
      this.stopHeartbeat();
      
      // Attempt to reconnect after 3 seconds
      setTimeout(() => this.connect(userId), 3000);
    };
    
    this.socket.onerror = (error) => {
      console.error('WebSocket error:', error);
      this.connectionStatus.next('disconnected');
    };
  }
  
  disconnect(): void {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
    this.stopHeartbeat();
  }
  
  subscribeToRepository(repositoryId: string): void {
    this.send({
      type: 'SubscribeToRepository',
      repository_id: repositoryId
    });
  }
  
  unsubscribeFromRepository(repositoryId: string): void {
    this.send({
      type: 'UnsubscribeFromRepository',
      repository_id: repositoryId
    });
  }
  
  sendLiveComment(repositoryId: string, filePath: string, line: number, comment: string): void {
    this.send({
      type: 'SendLiveComment',
      repository_id: repositoryId,
      file_path: filePath,
      line: line,
      comment: comment
    });
  }
  
  // Observable for specific message types
  getRepositoryEvents(repositoryId: string): Observable<WebSocketMessage> {
    return this.messages$.pipe(
      filter(msg => 
        ['RepositoryCreated', 'CommitPushed', 'BranchCreated'].includes(msg.type) &&
        msg.data.repository_id === repositoryId
      )
    );
  }
  
  getLiveComments(repositoryId: string): Observable<LiveComment> {
    return this.messages$.pipe(
      filter(msg => msg.type === 'LiveComment' && msg.data.repository_id === repositoryId),
      map(msg => msg.data as LiveComment)
    );
  }
  
  private send(message: any): void {
    if (this.socket?.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket not connected, message not sent:', message);
    }
  }
  
  private heartbeatInterval: any;
  
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.send({ type: 'Heartbeat' });
    }, 30000); // Send heartbeat every 30 seconds
  }
  
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }
}
```

## 🤝 Collaborative Features

### Live Code Review Component

```typescript
// components/live-code-review/live-code-review.component.ts
import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { WebSocketService, LiveComment } from '../../services/websocket.service';
import { FormControl } from '@angular/forms';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-live-code-review',
  template: `
    <div class="code-review-container">
      <div class="file-viewer">
        <div class="file-header">
          <h3>{{ filePath }}</h3>
          <div class="live-users">
            <mat-chip-list>
              <mat-chip *ngFor="let user of liveUsers" color="primary">
                <mat-icon>person</mat-icon>
                {{ user }}
              </mat-chip>
            </mat-chip-list>
          </div>
        </div>

        <div class="code-lines">
          <div *ngFor="let line of codeLines; let i = index"
               class="code-line"
               [class.has-comments]="hasCommentsOnLine(i + 1)">

            <span class="line-number">{{ i + 1 }}</span>
            <span class="line-content">{{ line }}</span>

            <button mat-icon-button
                    class="add-comment-btn"
                    (click)="startComment(i + 1)">
              <mat-icon>comment</mat-icon>
            </button>

            <!-- Live comments for this line -->
            <div *ngIf="getCommentsForLine(i + 1).length > 0"
                 class="live-comments">
              <div *ngFor="let comment of getCommentsForLine(i + 1)"
                   class="live-comment"
                   [class.own-comment]="comment.author === currentUser">
                <div class="comment-header">
                  <strong>{{ comment.author }}</strong>
                  <span class="comment-time">{{ comment.timestamp | date:'short' }}</span>
                </div>
                <div class="comment-content">{{ comment.comment }}</div>
              </div>
            </div>

            <!-- Comment input -->
            <div *ngIf="commentingLine === i + 1" class="comment-input">
              <mat-form-field appearance="outline">
                <textarea matInput
                         [formControl]="commentControl"
                         placeholder="Add a comment..."
                         rows="3"></textarea>
              </mat-form-field>
              <div class="comment-actions">
                <button mat-button (click)="cancelComment()">Cancel</button>
                <button mat-raised-button
                        color="primary"
                        (click)="submitComment(i + 1)"
                        [disabled]="!commentControl.value?.trim()">
                  Comment
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./live-code-review.component.scss']
})
export class LiveCodeReviewComponent implements OnInit, OnDestroy {
  @Input() repositoryId!: string;
  @Input() filePath!: string;
  @Input() fileContent!: string;
  @Input() currentUser!: string;

  codeLines: string[] = [];
  liveComments: LiveComment[] = [];
  liveUsers: string[] = [];
  commentingLine: number | null = null;
  commentControl = new FormControl('');

  private subscriptions = new Subscription();

  constructor(private wsService: WebSocketService) {}

  ngOnInit(): void {
    this.codeLines = this.fileContent.split('\n');

    // Subscribe to live comments for this repository
    const commentsSubscription = this.wsService
      .getLiveComments(this.repositoryId)
      .subscribe(comment => {
        if (comment.file_path === this.filePath) {
          this.liveComments.push({
            ...comment,
            timestamp: new Date()
          });
        }
      });

    this.subscriptions.add(commentsSubscription);

    // Subscribe to repository to get live updates
    this.wsService.subscribeToRepository(this.repositoryId);
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    this.wsService.unsubscribeFromRepository(this.repositoryId);
  }

  hasCommentsOnLine(lineNumber: number): boolean {
    return this.liveComments.some(comment => comment.line === lineNumber);
  }

  getCommentsForLine(lineNumber: number): LiveComment[] {
    return this.liveComments
      .filter(comment => comment.line === lineNumber)
      .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }

  startComment(lineNumber: number): void {
    this.commentingLine = lineNumber;
    this.commentControl.setValue('');
  }

  cancelComment(): void {
    this.commentingLine = null;
    this.commentControl.setValue('');
  }

  submitComment(lineNumber: number): void {
    const comment = this.commentControl.value?.trim();
    if (comment) {
      this.wsService.sendLiveComment(
        this.repositoryId,
        this.filePath,
        lineNumber,
        comment
      );

      this.cancelComment();
    }
  }
}
```

### State Synchronization with Operational Transform

```rust
// src/websocket/operational_transform.rs
use serde::{Deserialize, Serialize};
use std::cmp::Ordering;

/// Operational Transform for collaborative text editing
/// Based on the algorithm used by Google Docs and other collaborative editors
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Operation {
    Insert { position: usize, text: String, author: String },
    Delete { position: usize, length: usize, author: String },
    Retain { length: usize },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DocumentState {
    pub content: String,
    pub version: u64,
    pub operations: Vec<Operation>,
}

pub struct OperationalTransform;

impl OperationalTransform {
    /// Transform operation A against operation B
    /// Returns the transformed version of A that can be applied after B
    pub fn transform(op_a: Operation, op_b: Operation) -> (Operation, Operation) {
        match (op_a, op_b) {
            // Insert vs Insert
            (Operation::Insert { position: pos_a, text: text_a, author: author_a },
             Operation::Insert { position: pos_b, text: text_b, author: author_b }) => {
                match pos_a.cmp(&pos_b) {
                    Ordering::Less => (
                        Operation::Insert { position: pos_a, text: text_a, author: author_a },
                        Operation::Insert { position: pos_b + text_a.len(), text: text_b, author: author_b }
                    ),
                    Ordering::Greater => (
                        Operation::Insert { position: pos_a + text_b.len(), text: text_a, author: author_a },
                        Operation::Insert { position: pos_b, text: text_b, author: author_b }
                    ),
                    Ordering::Equal => {
                        // Tie-breaking: use author name for deterministic ordering
                        if author_a < author_b {
                            (
                                Operation::Insert { position: pos_a, text: text_a, author: author_a },
                                Operation::Insert { position: pos_b + text_a.len(), text: text_b, author: author_b }
                            )
                        } else {
                            (
                                Operation::Insert { position: pos_a + text_b.len(), text: text_a, author: author_a },
                                Operation::Insert { position: pos_b, text: text_b, author: author_b }
                            )
                        }
                    }
                }
            }

            // Insert vs Delete
            (Operation::Insert { position: pos_a, text, author },
             Operation::Delete { position: pos_b, length, author: author_b }) => {
                if pos_a <= pos_b {
                    (
                        Operation::Insert { position: pos_a, text, author },
                        Operation::Delete { position: pos_b + text.len(), length, author: author_b }
                    )
                } else if pos_a > pos_b + length {
                    (
                        Operation::Insert { position: pos_a - length, text, author },
                        Operation::Delete { position: pos_b, length, author: author_b }
                    )
                } else {
                    // Insert position is within deleted range
                    (
                        Operation::Insert { position: pos_b, text, author },
                        Operation::Delete { position: pos_b, length, author: author_b }
                    )
                }
            }

            // Delete vs Insert (symmetric case)
            (Operation::Delete { position: pos_a, length, author },
             Operation::Insert { position: pos_b, text, author: author_b }) => {
                let (transformed_b, transformed_a) = Self::transform(
                    Operation::Insert { position: pos_b, text, author: author_b },
                    Operation::Delete { position: pos_a, length, author }
                );
                (transformed_a, transformed_b)
            }

            // Delete vs Delete
            (Operation::Delete { position: pos_a, length: len_a, author: author_a },
             Operation::Delete { position: pos_b, length: len_b, author: author_b }) => {
                if pos_a + len_a <= pos_b {
                    // A is completely before B
                    (
                        Operation::Delete { position: pos_a, length: len_a, author: author_a },
                        Operation::Delete { position: pos_b - len_a, length: len_b, author: author_b }
                    )
                } else if pos_b + len_b <= pos_a {
                    // B is completely before A
                    (
                        Operation::Delete { position: pos_a - len_b, length: len_a, author: author_a },
                        Operation::Delete { position: pos_b, length: len_b, author: author_b }
                    )
                } else {
                    // Overlapping deletes - complex case
                    let start_a = pos_a;
                    let end_a = pos_a + len_a;
                    let start_b = pos_b;
                    let end_b = pos_b + len_b;

                    let overlap_start = start_a.max(start_b);
                    let overlap_end = end_a.min(end_b);
                    let overlap_len = overlap_end.saturating_sub(overlap_start);

                    (
                        Operation::Delete {
                            position: pos_a.min(pos_b),
                            length: len_a - overlap_len,
                            author: author_a
                        },
                        Operation::Delete {
                            position: pos_b.min(pos_a),
                            length: len_b - overlap_len,
                            author: author_b
                        }
                    )
                }
            }

            // Retain operations don't need transformation
            (op_a, op_b) => (op_a, op_b),
        }
    }

    /// Apply operation to document content
    pub fn apply_operation(content: &str, operation: &Operation) -> Result<String, String> {
        match operation {
            Operation::Insert { position, text, .. } => {
                if *position > content.len() {
                    return Err("Insert position out of bounds".to_string());
                }

                let mut result = String::with_capacity(content.len() + text.len());
                result.push_str(&content[..*position]);
                result.push_str(text);
                result.push_str(&content[*position..]);
                Ok(result)
            }

            Operation::Delete { position, length, .. } => {
                if *position + *length > content.len() {
                    return Err("Delete range out of bounds".to_string());
                }

                let mut result = String::with_capacity(content.len());
                result.push_str(&content[..*position]);
                result.push_str(&content[*position + *length..]);
                Ok(result)
            }

            Operation::Retain { .. } => Ok(content.to_string()),
        }
    }

    /// Compose multiple operations into a single operation
    pub fn compose_operations(ops: Vec<Operation>) -> Vec<Operation> {
        // Simplified composition - in practice, this would be more complex
        ops
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_insert_insert_transform() {
        let op_a = Operation::Insert {
            position: 5,
            text: "hello".to_string(),
            author: "alice".to_string()
        };
        let op_b = Operation::Insert {
            position: 3,
            text: "world".to_string(),
            author: "bob".to_string()
        };

        let (transformed_a, transformed_b) = OperationalTransform::transform(op_a, op_b);

        // After transformation, A should be shifted by B's text length
        match transformed_a {
            Operation::Insert { position, text, .. } => {
                assert_eq!(position, 10); // 5 + "world".len()
                assert_eq!(text, "hello");
            }
            _ => panic!("Expected Insert operation"),
        }
    }

    #[test]
    fn test_apply_insert_operation() {
        let content = "Hello World";
        let operation = Operation::Insert {
            position: 6,
            text: "Beautiful ".to_string(),
            author: "alice".to_string()
        };

        let result = OperationalTransform::apply_operation(content, &operation).unwrap();
        assert_eq!(result, "Hello Beautiful World");
    }

    #[test]
    fn test_apply_delete_operation() {
        let content = "Hello Beautiful World";
        let operation = Operation::Delete {
            position: 6,
            length: 10, // "Beautiful "
            author: "alice".to_string()
        };

        let result = OperationalTransform::apply_operation(content, &operation).unwrap();
        assert_eq!(result, "Hello World");
    }
}
```

## 🎯 Key Takeaways

1. **WebSocket Benefits**: Real-time bidirectional communication
2. **Actor Model**: Concurrent message handling with isolated state
3. **Operational Transform**: Conflict-free collaborative editing
4. **State Synchronization**: Maintaining consistency across clients
5. **Scalability**: Efficient broadcasting and subscription management

## 🚀 Performance Considerations

### Connection Management
- **Connection Pooling**: Reuse connections efficiently
- **Heartbeat Mechanism**: Detect and clean up dead connections
- **Graceful Degradation**: Fall back to polling if WebSocket fails

### Memory Management
- **Message Buffering**: Limit buffer sizes to prevent memory leaks
- **Client Cleanup**: Remove disconnected clients from subscriptions
- **Garbage Collection**: Periodic cleanup of expired data

### Scaling WebSockets
- **Horizontal Scaling**: Use Redis for message broadcasting across servers
- **Load Balancing**: Sticky sessions or connection migration
- **Rate Limiting**: Prevent abuse and DoS attacks

Ready to continue with [Module 7: Performance and Scalability](./module-07-performance.md)?
